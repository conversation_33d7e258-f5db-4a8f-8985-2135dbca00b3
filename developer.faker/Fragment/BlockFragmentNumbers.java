package com.developer.faker.Fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Adapter.BlockNumberListAdapter;
import com.developer.faker.Data.BlockNumberData;
import com.developer.faker.Data.BlockNumberHistory;
import com.developer.faker.Model.BlockNumberDeleteListener;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilBlock;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public class BlockFragmentNumbers extends BaseFragment {
    View view = null;
    TextView txtTotalCount = null;
    ListView lstNumbers = null;
    ArrayList<BlockNumberData> lstBlockNumberData = new ArrayList<>();
    BlockNumberListAdapter blockAdater = null;
    UtilBlock utilBlock = null;

    @Override // com.developer.faker.Fragment.BaseFragment, android.support.v4.app.Fragment
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.utilBlock = UtilBlock.getInstance(getContext());
    }

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            this.view = layoutInflater.inflate(R.layout.fragment_block_numbers, viewGroup, false);
        }
        InitUI();
        return this.view;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void InitUI() {
        this.txtTotalCount = (TextView) this.view.findViewById(R.id.txtTotalCount);
        View viewFindViewById = this.view.findViewById(R.id.layout_nodata);
        this.lstNumbers = (ListView) this.view.findViewById(R.id.lstBlockHistory);
        int size = this.utilBlock.lstPrefNumbers.size() + this.utilBlock.lstSpecNumbers.size();
        this.txtTotalCount.setText(String.valueOf(size) + " 건");
        if (size == 0) {
            viewFindViewById.setVisibility(0);
            this.lstNumbers.setVisibility(8);
            return;
        }
        viewFindViewById.setVisibility(8);
        this.lstNumbers.setVisibility(0);
        this.lstBlockNumberData.clear();
        Iterator<String> it = this.utilBlock.lstPrefNumbers.iterator();
        while (it.hasNext()) {
            String next = it.next();
            Iterator<BlockNumberHistory> it2 = this.utilBlock.lstBlockHistory.iterator();
            int i = 0;
            while (it2.hasNext()) {
                BlockNumberHistory next2 = it2.next();
                if (next2.type == 4 && next2.number.startsWith(next)) {
                    i++;
                }
            }
            this.lstBlockNumberData.add(new BlockNumberData(next, 1, i));
        }
        Iterator<String> it3 = this.utilBlock.lstSpecNumbers.iterator();
        while (it3.hasNext()) {
            String next3 = it3.next();
            Iterator<BlockNumberHistory> it4 = this.utilBlock.lstBlockHistory.iterator();
            int i2 = 0;
            while (it4.hasNext()) {
                BlockNumberHistory next4 = it4.next();
                if (next4.type == 3 && next4.number.equals(next3)) {
                    i2++;
                }
            }
            this.lstBlockNumberData.add(new BlockNumberData(next3, 0, i2));
        }
        this.blockAdater = new BlockNumberListAdapter((BaseActivity) getActivity(), 0, this.lstBlockNumberData);
        this.blockAdater.setOnClickCloseListener(new BlockNumberDeleteListener() { // from class: com.developer.faker.Fragment.BlockFragmentNumbers.1
            @Override // com.developer.faker.Model.BlockNumberDeleteListener
            public void onResult(BlockNumberData blockNumberData) {
                if (blockNumberData.nBlockType == BlockNumberData.BlockType_Pref) {
                    BlockFragmentNumbers.this.utilBlock.lstPrefNumbers.remove(blockNumberData.phoneNumber);
                } else if (blockNumberData.nBlockType == BlockNumberData.BlockType_Spec) {
                    BlockFragmentNumbers.this.utilBlock.lstSpecNumbers.remove(blockNumberData.phoneNumber);
                } else {
                    BlockFragmentNumbers.this.utilBlock.lstCallExplosion.remove(blockNumberData.phoneNumber);
                }
                BlockFragmentNumbers.this.InitUI();
            }
        });
        this.lstNumbers.setAdapter((ListAdapter) this.blockAdater);
    }
}