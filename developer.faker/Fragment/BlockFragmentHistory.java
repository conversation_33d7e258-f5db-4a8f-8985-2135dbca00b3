package com.developer.faker.Fragment;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Adapter.BlockNumberHistoryAdapter;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilBlock;

/* loaded from: classes.dex */
public class BlockFragmentHistory extends BaseFragment {
    BlockNumberHistoryAdapter histAdapter;
    View view = null;
    ListView lstHistory = null;
    TextView txtTotalCount = null;
    UtilBlock utilBlock = null;

    @Override // com.developer.faker.Fragment.BaseFragment, android.support.v4.app.Fragment
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.utilBlock = UtilBlock.getInstance(getContext());
    }

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            this.view = layoutInflater.inflate(R.layout.fragment_block_history, viewGroup, false);
        }
        InitUI();
        return this.view;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void InitUI() {
        this.txtTotalCount = (TextView) this.view.findViewById(R.id.txtTotalCount);
        View viewFindViewById = this.view.findViewById(R.id.layout_nodata);
        this.lstHistory = (ListView) this.view.findViewById(R.id.lstBlockHistory);
        this.view.findViewById(R.id.layout_close).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentHistory.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                AlertDialog.Builder builder = new AlertDialog.Builder(view.getContext());
                builder.setTitle("삭제");
                builder.setMessage("기록을 전부 삭제하시겠습니까?");
                builder.setPositiveButton("예", new DialogInterface.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentHistory.1.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i) {
                        BlockFragmentHistory.this.utilBlock.lstBlockHistory.clear();
                        BlockFragmentHistory.this.InitUI();
                    }
                });
                builder.setNegativeButton("아니", (DialogInterface.OnClickListener) null);
                builder.show();
            }
        });
        int size = this.utilBlock.lstBlockHistory.size();
        this.txtTotalCount.setText(String.valueOf(size) + " 건");
        if (size == 0) {
            viewFindViewById.setVisibility(0);
            this.lstHistory.setVisibility(8);
        } else {
            viewFindViewById.setVisibility(8);
            this.lstHistory.setVisibility(0);
            this.histAdapter = new BlockNumberHistoryAdapter((BaseActivity) getActivity(), 0, this.utilBlock.lstBlockHistory);
            this.lstHistory.setAdapter((ListAdapter) this.histAdapter);
        }
    }
}