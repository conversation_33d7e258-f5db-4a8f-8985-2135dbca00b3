package com.developer.faker.Fragment;

import android.content.DialogInterface;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.v7.app.AlertDialog;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;
import com.developer.faker.Adapter.RecentCallListAdapter;
import com.developer.faker.BuildConfig;
import com.developer.faker.Data.RecentCallData;
import com.developer.faker.Data.RecentIncomeInfo;
import com.developer.faker.Model.CallLogListener;
import com.developer.faker.R;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.Utils;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public class MainFragment extends BaseFragment {
    public static MainFragment m_instance;
    EditText editSearch;
    TextView incomeEmptyTV;
    ListView lstRecentCall;
    FrameLayout m_layoutWatermark;
    private ArrayList<RecentCallData> recentCallDataItems = new ArrayList<>();
    private RecentCallListAdapter recentCallListAdapter;
    TextView recentCallTV;
    View view;

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        m_instance = this;
        if (this.view == null) {
            this.view = layoutInflater.inflate(R.layout.fragment_main, viewGroup, false);
            initUI(this.view);
        }
        this.baseActivity.hideSoftKeyboard();
        return this.view;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void Search() {
        if (Global.RemainQueryCount <= 0 && !Global.SearchHistory) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("오류");
            builder.setMessage("오늘 검색횟수가 만료되었습니다.");
            builder.setPositiveButton("OK", (DialogInterface.OnClickListener) null);
            builder.show();
            return;
        }
        String string = this.editSearch.getText().toString();
        if (string != BuildConfig.FLAVOR && !string.isEmpty()) {
            Global.Incoming_Call_Number = Utils.getCorrectPhoneNumber(string);
            this.baseActivity.gotoSearchFragment();
            this.baseActivity.hideSoftKeyboard();
            this.editSearch.setText(BuildConfig.FLAVOR);
            return;
        }
        Toast.makeText(getContext(), R.string.search_hint, 0).show();
    }

    private void initUI(View view) {
        this.editSearch = (EditText) view.findViewById(R.id.edtSearch);
        this.editSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() { // from class: com.developer.faker.Fragment.MainFragment.1
            @Override // android.widget.TextView.OnEditorActionListener
            public boolean onEditorAction(TextView textView, int i, KeyEvent keyEvent) {
                if (i != 3) {
                    return false;
                }
                MainFragment.this.Search();
                return true;
            }
        });
        this.recentCallTV = (TextView) view.findViewById(R.id.recentCallTV);
        this.lstRecentCall = (ListView) view.findViewById(R.id.lstRecentCall);
        this.incomeEmptyTV = (TextView) view.findViewById(R.id.incomeEmptyTV);
        view.findViewById(R.id.btnsearch).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.MainFragment.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                Global.SearchHistory = false;
                MainFragment.this.Search();
            }
        });
        this.editSearch.setHint("전화번호를 입력하세요 [" + Global.RemainQueryCount + "]");
        Utils.getInstance().GetQueryLimitCount(getContext(), new Handler() { // from class: com.developer.faker.Fragment.MainFragment.3
            @Override // android.os.Handler
            public void handleMessage(Message message) {
                super.handleMessage(message);
                MainFragment.this.editSearch.setHint("전화번호를 입력하세요 [" + Global.RemainQueryCount + "]");
            }
        });
        this.m_layoutWatermark = (FrameLayout) view.findViewById(R.id.layoutWatermark);
        this.m_layoutWatermark.setBackground(new BitmapDrawable(Utils.getInstance().MakeWaterMark(BitmapFactory.decodeResource(getResources(), R.drawable.search_back), UtilAuth.getInstance(getContext()).UserCompany, 20)));
    }

    public void RefreshCallLog() {
        Utils.getInstance().getCallLog(this.baseActivity, new CallLogListener() { // from class: com.developer.faker.Fragment.MainFragment.4
            @Override // com.developer.faker.Model.CallLogListener
            public void onResult(int i, List<RecentIncomeInfo> list) {
                MainFragment.this.recentCallDataItems.clear();
                if (list == null || list.size() == 0) {
                    MainFragment.this.recentCallTV.setText("호출이력 없음");
                    MainFragment.this.lstRecentCall.setVisibility(8);
                    MainFragment.this.incomeEmptyTV.setVisibility(0);
                    return;
                }
                MainFragment.this.recentCallTV.setText("총 " + list.size() + "건");
                MainFragment.this.lstRecentCall.setVisibility(0);
                MainFragment.this.incomeEmptyTV.setVisibility(8);
                for (int i2 = 0; i2 < list.size(); i2++) {
                    RecentCallData recentCallData = new RecentCallData();
                    recentCallData.callType = list.get(i2).callType;
                    Utils.getInstance();
                    recentCallData.phonenumber = Utils.getHypenPhoneNumber(list.get(i2).phoneNumber);
                    recentCallData.date = list.get(i2).callDate;
                    try {
                        recentCallData.memo = list.get(i2).contactName;
                        if (recentCallData.memo == BuildConfig.FLAVOR || recentCallData.memo.isEmpty()) {
                            recentCallData.memo = MainFragment.this.getString(R.string.noData);
                        }
                    } catch (Exception unused) {
                        recentCallData.memo = MainFragment.this.getString(R.string.noData);
                    }
                    MainFragment.this.recentCallDataItems.add(recentCallData);
                }
                MainFragment mainFragment = MainFragment.this;
                mainFragment.recentCallListAdapter = new RecentCallListAdapter(mainFragment.baseActivity, 0, MainFragment.this.recentCallDataItems);
                MainFragment.this.lstRecentCall.setAdapter((ListAdapter) MainFragment.this.recentCallListAdapter);
                MainFragment.this.lstRecentCall.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.developer.faker.Fragment.MainFragment.4.1
                    @Override // android.widget.AdapterView.OnItemClickListener
                    public void onItemClick(AdapterView<?> adapterView, View view, int i3, long j) {
                        MainFragment.this.editSearch.setText(((RecentCallData) MainFragment.this.recentCallDataItems.get(i3)).phonenumber);
                        Global.SearchHistory = true;
                        MainFragment.this.Search();
                    }
                });
            }
        });
    }

    @Override // com.developer.faker.Fragment.BaseFragment, android.support.v4.app.Fragment
    public void onResume() {
        super.onResume();
        RefreshCallLog();
    }
}