package com.developer.faker.Utils;

import android.util.Base64;
import com.developer.faker.BuildConfig;
import java.io.UnsupportedEncodingException;

/* loaded from: classes.dex */
public class RC4 {
    private static RC4 m_instacne;

    public static RC4 getInstance() {
        if (m_instacne == null) {
            m_instacne = new RC4();
        }
        return m_instacne;
    }

    public String encrypt(String str, String str2) {
        byte[] bytes = new byte[0];
        byte[] bytes2 = new byte[0];
        try {
            bytes = str.getBytes("UTF-8");
            bytes2 = str2.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return Base64.encodeToString(encrypt(bytes, bytes2), 2);
    }

    public String decrypt(String str, String str2) throws UnsupportedEncodingException {
        byte[] bArr = new byte[0];
        byte[] bytes = new byte[0];
        byte[] bArrDecode = Base64.decode(str, 2);
        try {
            bytes = str2.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            return new String(decrypt(bArrDecode, bytes), "UTF-8");
        } catch (UnsupportedEncodingException e2) {
            e2.printStackTrace();
            return BuildConfig.FLAVOR;
        }
    }

    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    public byte[] encrypt(byte[] bArr, byte[] bArr2) {
        short[] sArr = new short[256];
        short[] sArr2 = new short[256];
        if (bArr2.length < 1 || bArr2.length > 256) {
            throw new IllegalArgumentException("key must be between 1 and 256 bytes");
        }
        int length = bArr2.length;
        for (int i = 0; i < 256; i++) {
            sArr[i] = (short) i;
            sArr2[i] = bArr2[i % length];
        }
        int i2 = 0;
        for (int i3 = 0; i3 < 256; i3++) {
            i2 = (i2 + sArr[i3] + sArr2[i3]) & 255;
            short s = sArr[i3];
            sArr[i3] = sArr[i2];
            sArr[i2] = s;
        }
        byte[] bArr3 = new byte[bArr.length];
        int i4 = 0;
        int i5 = 0;
        for (int i6 = 0; i6 < bArr.length; i6++) {
            i4 = (i4 + 1) & 255;
            i5 = (i5 + sArr[i4]) & 255;
            short s2 = sArr[i5];
            sArr[i5] = sArr[i4];
            sArr[i4] = s2;
            bArr3[i6] = (byte) (sArr[(sArr[i4] + sArr[i5]) & 255] ^ bArr[i6]);
        }
        return bArr3;
    }

    public byte[] decrypt(byte[] bArr, byte[] bArr2) {
        return encrypt(bArr, bArr2);
    }
}