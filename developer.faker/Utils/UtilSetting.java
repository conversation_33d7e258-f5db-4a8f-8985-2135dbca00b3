package com.developer.faker.Utils;

import android.content.Context;
import android.content.SharedPreferences;

/* loaded from: classes.dex */
public class UtilSetting {
    private static UtilSetting mInstance;
    private Context _ctx = null;
    public boolean TODAY_SHOW = false;
    public int POPUP_POSITION = 1;
    public boolean POPUP_REMAIN = false;

    public static UtilSetting getInstance(Context context) {
        if (mInstance == null) {
            mInstance = new UtilSetting();
            UtilSetting utilSetting = mInstance;
            utilSetting._ctx = context;
            utilSetting.loadSetting();
        }
        return mInstance;
    }

    private void loadSetting() {
        SharedPreferences sharedPreferences = this._ctx.getSharedPreferences(Const.MYPREFS, 0);
        this.TODAY_SHOW = sharedPreferences.getBoolean(Const.MYPREFS_Setting_ShowTodayCall, true);
        this.POPUP_REMAIN = sharedPreferences.getBoolean(Const.MYPREFS_Setting_PopupRemain, false);
        this.POPUP_POSITION = sharedPreferences.getInt(Const.MYPREFS_Setting_PopupPos, 1);
    }

    public void saveSetting() {
        SharedPreferences.Editor editorEdit = this._ctx.getSharedPreferences(Const.MYPREFS, 0).edit();
        editorEdit.putBoolean(Const.MYPREFS_Setting_ShowTodayCall, this.TODAY_SHOW);
        editorEdit.putBoolean(Const.MYPREFS_Setting_PopupRemain, this.POPUP_REMAIN);
        editorEdit.putInt(Const.MYPREFS_Setting_PopupPos, this.POPUP_POSITION);
        editorEdit.commit();
    }
}