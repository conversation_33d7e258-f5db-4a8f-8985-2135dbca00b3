package com.developer.faker.Utils;

import android.app.Activity;
import android.widget.Toast;

/* loaded from: classes.dex */
public class BackPressHandler {
    private Activity activity;
    private long backKeyPressedTime = 0;
    private Toast toast;

    public BackPressHandler(Activity activity) {
        this.activity = activity;
    }

    public void onBackPressed() {
        if (System.currentTimeMillis() > this.backKeyPressedTime + 2000) {
            this.backKeyPressedTime = System.currentTimeMillis();
            showGuide();
        } else if (System.currentTimeMillis() <= this.backKeyPressedTime + 2000) {
            this.activity.finish();
            this.toast.cancel();
        }
    }

    public void onBackPressed(String str) {
        if (System.currentTimeMillis() > this.backKeyPressedTime + 2000) {
            this.backKeyPressedTime = System.currentTimeMillis();
            showGuide(str);
        } else if (System.currentTimeMillis() <= this.backKeyPressedTime + 2000) {
            this.activity.finish();
            this.toast.cancel();
        }
    }

    public void onBackPressed(int i) {
        long j = i;
        if (System.currentTimeMillis() > this.backKeyPressedTime + j) {
            this.backKeyPressedTime = System.currentTimeMillis();
            showGuide();
        } else if (System.currentTimeMillis() <= this.backKeyPressedTime + j) {
            this.activity.finish();
            this.toast.cancel();
        }
    }

    public void onBackPressed(String str, int i) {
        long j = i;
        if (System.currentTimeMillis() > this.backKeyPressedTime + j) {
            this.backKeyPressedTime = System.currentTimeMillis();
            showGuide(str);
        } else if (System.currentTimeMillis() <= this.backKeyPressedTime + j) {
            this.activity.finish();
            this.toast.cancel();
        }
    }

    private void showGuide() {
        this.toast = Toast.makeText(this.activity, "'뒤로' 버튼을 한번 더 누르시면 종료됩니다.", 0);
        this.toast.show();
    }

    private void showGuide(String str) {
        this.toast = Toast.makeText(this.activity, str, 0);
        this.toast.show();
    }
}