package com.developer.faker.Utils;

import android.content.Context;
import android.provider.Settings;
import com.developer.faker.BuildConfig;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/* loaded from: classes.dex */
public class UtilAuth {
    private static UtilAuth mInstance;
    private String LicenseEndDate;
    private int UserRemainMinutes;
    private Context _ctx = null;
    public int UserID = 0;
    public String UserToken = BuildConfig.FLAVOR;
    public String UserEmail = BuildConfig.FLAVOR;
    public String UserPWD = BuildConfig.FLAVOR;
    public String UserCompany = BuildConfig.FLAVOR;

    public static UtilAuth getInstance(Context context) {
        if (mInstance == null) {
            mInstance = new UtilAuth();
            UtilAuth utilAuth = mInstance;
            utilAuth._ctx = context;
            utilAuth.loadAuthInfo();
        }
        return mInstance;
    }

    public Boolean isHaveToken() {
        if (Utils.isNullOrEmptyString(this.UserToken)) {
            return false;
        }
        return true;
    }

    public int GetRemainMinutes() {
        return this.UserRemainMinutes;
    }

    public String GetLicenseEndDate() {
        return this.LicenseEndDate;
    }

    public void SetRemainMinutes(int i) {
        this.UserRemainMinutes = i;
        Calendar calendar = Calendar.getInstance();
        calendar.add(12, this.UserRemainMinutes);
        this.LicenseEndDate = new SimpleDateFormat("yyyy-MM-dd HH:mm").format(calendar.getTime());
    }

    public void saveAuthInfo() {
        UtilSharedPref.setString(this._ctx, Const.USER_TOKEN, this.UserToken);
        UtilSharedPref.setString(this._ctx, Const.USER_ID, this.UserEmail);
        UtilSharedPref.setString(this._ctx, Const.USER_PWD, this.UserPWD);
        UtilSharedPref.setString(this._ctx, Const.USER_COMPANY, this.UserCompany);
        UtilSharedPref.setInt(this._ctx, Const.REMAIN_MINUTES, this.UserRemainMinutes);
        UtilSharedPref.setString(this._ctx, Const.LICENSEEND_DATE, this.LicenseEndDate);
    }

    public void loadAuthInfo() {
        this._ctx.getSharedPreferences(Const.MYPREFS, 0);
        this.UserEmail = UtilSharedPref.getString(this._ctx, Const.USER_ID, BuildConfig.FLAVOR);
        this.UserPWD = UtilSharedPref.getString(this._ctx, Const.USER_PWD, BuildConfig.FLAVOR);
        this.UserToken = UtilSharedPref.getString(this._ctx, Const.USER_TOKEN, BuildConfig.FLAVOR);
        this.UserCompany = UtilSharedPref.getString(this._ctx, Const.USER_COMPANY, BuildConfig.FLAVOR);
        this.UserRemainMinutes = UtilSharedPref.getInt(this._ctx, Const.REMAIN_MINUTES, 1);
        this.LicenseEndDate = UtilSharedPref.getString(this._ctx, Const.LICENSEEND_DATE, BuildConfig.FLAVOR);
    }

    public String getDeviceToken() {
        return Settings.Secure.getString(this._ctx.getContentResolver(), "android_id");
    }
}