package com.developer.faker.Utils;

import android.content.Context;
import android.content.SharedPreferences;
import com.developer.faker.BuildConfig;
import com.developer.faker.Data.BlockNumberHistory;
import com.developer.faker.Data.PhoneInfo;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;

/* loaded from: classes.dex */
public class UtilBlock {
    private static UtilBlock mInstance;
    private Context _ctx;
    public ArrayList<String> lstSpecNumbers = null;
    public ArrayList<String> lstPrefNumbers = null;
    public ArrayList<BlockNumberHistory> lstBlockHistory = null;
    public ArrayList<String> lstCallExplosion = null;
    public int callExplosionCount = 1;
    public boolean IsBlockUnknown = false;
    public boolean IsBlockTodayCall = false;
    public boolean IsBlockPrefNumbers = false;
    public boolean IsBlockSpecNumbers = false;
    public boolean IsBlockAll = false;
    public boolean IsBlockCallExp = false;
    public int nBlockLimitTodayCall = 5;

    public static UtilBlock getInstance(Context context) {
        if (mInstance == null) {
            mInstance = new UtilBlock();
            UtilBlock utilBlock = mInstance;
            utilBlock._ctx = context;
            utilBlock.LoadSetting();
        }
        return mInstance;
    }

    public void LoadSetting() {
        SharedPreferences sharedPreferences = this._ctx.getSharedPreferences(Const.MYPREFS, 0);
        this.IsBlockUnknown = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockUnknown, false);
        this.IsBlockPrefNumbers = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockPrefix, false);
        this.IsBlockSpecNumbers = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockSpecNumber, false);
        this.IsBlockTodayCall = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockTodayCall, false);
        this.IsBlockAll = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockAll, false);
        this.IsBlockCallExp = sharedPreferences.getBoolean(Const.MYPREFS_Block_CallExplosion, false);
        this.callExplosionCount = sharedPreferences.getInt(Const.MYPREFS_Block_CallExplosionCount, 1);
        this.nBlockLimitTodayCall = sharedPreferences.getInt(Const.MYPREFS_Block_LimitTodayCall, 5);
        this.lstSpecNumbers = UtilSharedPref.getStringArray(sharedPreferences, Const.MYPREFS_Block_SpecNumbers);
        this.lstPrefNumbers = UtilSharedPref.getStringArray(sharedPreferences, Const.MYPREFS_Block_PrefNumbers);
        this.lstCallExplosion = UtilSharedPref.getStringArray(sharedPreferences, Const.MYPREFS_Block_CallExplosion);
        LoadBlockHistory(sharedPreferences);
    }

    public void AddCallExpList(String str) {
        boolean z = false;
        for (int i = 0; i < this.lstCallExplosion.size(); i++) {
            if (str.compareTo(this.lstCallExplosion.get(i)) == 0) {
                z = true;
            }
        }
        if (z) {
            return;
        }
        this.lstCallExplosion.add(str);
    }

    private void LoadBlockHistory(SharedPreferences sharedPreferences) {
        this.lstBlockHistory = new ArrayList<>();
        int i = sharedPreferences.getInt("MYPREFS_Block_History_count", 0);
        for (int i2 = 0; i2 < i; i2++) {
            String string = sharedPreferences.getString("MYPREFS_Block_History_phone_" + String.valueOf(i2), BuildConfig.FLAVOR);
            int i3 = sharedPreferences.getInt("MYPREFS_Block_History_type_" + String.valueOf(i2), 0);
            long j = sharedPreferences.getLong("MYPREFS_Block_History_time_" + String.valueOf(i2), 0L);
            if (!isOldDate(j)) {
                BlockNumberHistory blockNumberHistory = new BlockNumberHistory();
                blockNumberHistory.number = string;
                blockNumberHistory.type = i3;
                blockNumberHistory.dateTick = j;
                this.lstBlockHistory.add(blockNumberHistory);
            }
        }
    }

    public void SaveSetting() {
        SharedPreferences.Editor editorEdit = this._ctx.getSharedPreferences(Const.MYPREFS, 0).edit();
        editorEdit.putBoolean(Const.MYPREFS_Block_IsBlockUnknown, this.IsBlockUnknown);
        editorEdit.putBoolean(Const.MYPREFS_Block_IsBlockPrefix, this.IsBlockPrefNumbers);
        editorEdit.putBoolean(Const.MYPREFS_Block_IsBlockSpecNumber, this.IsBlockSpecNumbers);
        editorEdit.putBoolean(Const.MYPREFS_Block_IsBlockTodayCall, this.IsBlockTodayCall);
        editorEdit.putBoolean(Const.MYPREFS_Block_IsBlockAll, this.IsBlockAll);
        editorEdit.putInt(Const.MYPREFS_Block_LimitTodayCall, this.nBlockLimitTodayCall);
        editorEdit.putBoolean(Const.MYPREFS_Block_CallExplosion, this.IsBlockCallExp);
        editorEdit.putInt(Const.MYPREFS_Block_CallExplosionCount, this.callExplosionCount);
        UtilSharedPref.putStringArray(editorEdit, Const.MYPREFS_Block_SpecNumbers, this.lstSpecNumbers);
        UtilSharedPref.putStringArray(editorEdit, Const.MYPREFS_Block_PrefNumbers, this.lstPrefNumbers);
        UtilSharedPref.putStringArray(editorEdit, Const.MYPREFS_Block_CallExplosion, this.lstCallExplosion);
        SaveBlockHistory(editorEdit);
        editorEdit.commit();
    }

    private boolean isOldDate(long j) {
        return new Date(j).getDate() < new Date().getDate();
    }

    private void SaveBlockHistory(SharedPreferences.Editor editor) {
        int i = 0;
        for (int i2 = 0; i2 < this.lstBlockHistory.size(); i2++) {
            BlockNumberHistory blockNumberHistory = this.lstBlockHistory.get(i2);
            if (!isOldDate(blockNumberHistory.dateTick)) {
                editor.putString("MYPREFS_Block_History_phone_" + String.valueOf(i2), blockNumberHistory.number);
                editor.putInt("MYPREFS_Block_History_type_" + String.valueOf(i2), blockNumberHistory.type);
                editor.putLong("MYPREFS_Block_History_time_" + String.valueOf(i2), blockNumberHistory.dateTick);
                i++;
            }
        }
        editor.putInt("MYPREFS_Block_History_count", i);
    }

    public void addBlockHistory(BlockNumberHistory blockNumberHistory) {
        this.lstBlockHistory.add(blockNumberHistory);
        SharedPreferences.Editor editorEdit = this._ctx.getSharedPreferences(Const.MYPREFS, 0).edit();
        SaveBlockHistory(editorEdit);
        editorEdit.commit();
    }

    public void addBlockHistory(String str, int i, long j) {
        BlockNumberHistory blockNumberHistory = new BlockNumberHistory();
        blockNumberHistory.number = str;
        blockNumberHistory.type = i;
        blockNumberHistory.dateTick = j;
        addBlockHistory(blockNumberHistory);
    }

    public int IsNeedBlock(String str) {
        boolean z;
        try {
            if (this.IsBlockAll) {
                return 5;
            }
            ArrayList<PhoneInfo> contactsListRaw = UtilContact.getInstance(this._ctx).getContactsListRaw(false);
            int i = 0;
            while (true) {
                if (i >= contactsListRaw.size()) {
                    z = false;
                    break;
                }
                if (contactsListRaw.get(i).phoneNumber.equals(str)) {
                    z = true;
                    break;
                }
                i++;
            }
            if (!z) {
                if (this.IsBlockUnknown) {
                    return 1;
                }
                if (this.IsBlockSpecNumbers) {
                    Iterator<String> it = this.lstSpecNumbers.iterator();
                    while (it.hasNext()) {
                        if (it.next().compareTo(str) == 0) {
                            return 3;
                        }
                    }
                }
                if (this.IsBlockPrefNumbers) {
                    Iterator<String> it2 = this.lstPrefNumbers.iterator();
                    while (it2.hasNext()) {
                        if (str.startsWith(it2.next())) {
                            return 4;
                        }
                    }
                }
                if (this.IsBlockCallExp) {
                    Iterator<String> it3 = this.lstCallExplosion.iterator();
                    while (it3.hasNext()) {
                        if (it3.next().compareTo(str) == 0) {
                            return 6;
                        }
                    }
                }
            }
            return 0;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}