package com.developer.faker.Service;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Icon;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import com.developer.faker.BuildConfig;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilLogFile;
import com.developer.faker.Utils.UtilSetting;
import com.developer.faker.Utils.Utils;
import java.io.IOException;
import java.util.Timer;
import java.util.TimerTask;

/* loaded from: classes.dex */
public class FloatingViewService extends Service {
    private View mFloatingView;
    private WindowManager mWindowManager;
    LinearLayout m_LinearLayout;
    ScrollView m_ScrollView;
    Button m_btnDetail;
    TextView m_txtNewNotice;
    TextView m_txtPhoneNumber;
    TextView m_txtResultCount;
    TextView m_txtResultPhoneNumber;
    View m_viewDetail;
    BlinkTimer blinkTimer = new BlinkTimer();
    Timer timer = new Timer();
    public boolean check_flag = false;
    private Handler m_Handler = new Handler() { // from class: com.developer.faker.Service.FloatingViewService.2
        @Override // android.os.Handler
        public void handleMessage(Message message) throws Exception {
            super.handleMessage(message);
            if (message.what == 10) {
                String str = (String) message.obj;
                if (str == null || str.isEmpty()) {
                    return;
                }
                FloatingViewService.this.m_txtPhoneNumber.setText(str);
                FloatingViewService.this.check_flag = true;
                return;
            }
            if (message.what == 0 || message.what == 1) {
                FloatingViewService.this.onResultFromServer(message.what, message.obj);
                FloatingViewService.this.check_flag = false;
            }
        }
    };
    private String mCallNumber = BuildConfig.FLAVOR;
    final int[] response_type = {R.drawable.icon_none, R.drawable.icon_reject, R.drawable.icon_accept, R.drawable.icon_miss, R.drawable.icon_sms};
    final int[] category_type = {R.drawable.icon_0, R.drawable.icon_1, R.drawable.icon_2, R.drawable.icon_3, R.drawable.icon_4, R.drawable.icon_5, R.drawable.icon_6, R.drawable.icon_7, R.drawable.icon_8, R.drawable.icon_9, R.drawable.icon_10, R.drawable.icon_11, R.drawable.icon_12};

    @Override // android.app.Service
    public IBinder onBind(Intent intent) {
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void foreGroundStop() {
        NewPhonecallReceiver.isIncoming = false;
        if (Build.VERSION.SDK_INT >= 26) {
            stopForeground(true);
            stopForeground(1);
            stopSelf();
            return;
        }
        new Handler(Looper.getMainLooper()).post(new Runnable() { // from class: com.developer.faker.Service.FloatingViewService.1
            @Override // java.lang.Runnable
            public void run() {
                FloatingViewService.this.stopForeground(true);
                FloatingViewService.this.stopSelf();
                FloatingViewService.this.stopSelfResult(1);
                FloatingViewService floatingViewService = FloatingViewService.this;
                floatingViewService.stopService(new Intent(floatingViewService, (Class<?>) FloatingViewService.class));
                FloatingViewService.this.onDestroy();
            }
        });
    }

    void createUI() {
        this.mFloatingView = LayoutInflater.from(this).inflate(R.layout.pop_up_window, (ViewGroup) null);
        DisplayMetrics displayMetrics = getApplicationContext().getResources().getDisplayMetrics();
        int i = displayMetrics.widthPixels;
        int i2 = displayMetrics.heightPixels;
        final WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams(-1, -2, Build.VERSION.SDK_INT >= 26 ? 2038 : 2002, 2629768, -3);
        layoutParams.gravity = 49;
        layoutParams.x = 0;
        int i3 = UtilSetting.getInstance(getBaseContext()).POPUP_POSITION;
        if (i3 == 0) {
            layoutParams.y = 0;
        } else if (i3 == 1) {
            layoutParams.y = (i2 / 10) * 3;
        } else {
            layoutParams.y = (i2 / 10) * 6;
        }
        this.mWindowManager = (WindowManager) getSystemService("window");
        this.mWindowManager.addView(this.mFloatingView, layoutParams);
        this.m_txtPhoneNumber = (TextView) this.mFloatingView.findViewById(R.id.txtPhoneNumber);
        this.m_txtPhoneNumber.setText(this.mCallNumber);
        this.m_txtNewNotice = (TextView) this.mFloatingView.findViewById(R.id.txtNewNotice);
        this.timer.scheduleAtFixedRate(this.blinkTimer, 0L, 500L);
        this.m_txtNewNotice.setVisibility(Utils.getInstance().getNewNoticeState(getBaseContext()) ? 0 : 8);
        Utils.getInstance().GetContactFromPhoneNumber(getApplicationContext(), this.mCallNumber, this.m_Handler);
        this.m_ScrollView = (ScrollView) this.mFloatingView.findViewById(R.id.scrollView);
        this.m_ScrollView.setVisibility(8);
        this.m_LinearLayout = (LinearLayout) this.mFloatingView.findViewById(R.id.lstDetail);
        this.m_btnDetail = (Button) this.mFloatingView.findViewById(R.id.btnDetail);
        this.m_viewDetail = this.mFloatingView.findViewById(R.id.viewDetail);
        this.m_viewDetail.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.FloatingViewService.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                FloatingViewService.this.m_ScrollView.setVisibility(0);
                FloatingViewService.this.m_btnDetail.setVisibility(8);
                FloatingViewService.this.m_viewDetail.setVisibility(8);
                FloatingViewService.this.m_ScrollView.setBackground(new BitmapDrawable(FloatingViewService.this.getBaseContext().getResources(), BitmapFactory.decodeResource(FloatingViewService.this.getResources(), R.drawable.search_back)));
                Display defaultDisplay = FloatingViewService.this.mWindowManager.getDefaultDisplay();
                WindowManager.LayoutParams layoutParams2 = (WindowManager.LayoutParams) FloatingViewService.this.mFloatingView.getLayoutParams();
                Point point = new Point();
                defaultDisplay.getSize(point);
                layoutParams2.width = -1;
                layoutParams2.height = (point.y / 3) * 2;
                layoutParams2.y = point.y / 6;
                FloatingViewService.this.mWindowManager.updateViewLayout(FloatingViewService.this.mFloatingView, layoutParams2);
            }
        });
        this.m_txtResultCount = (TextView) this.mFloatingView.findViewById(R.id.txtResultCount);
        this.m_txtResultPhoneNumber = (TextView) this.mFloatingView.findViewById(R.id.txtResultPhoneNumber);
        if (Build.VERSION.SDK_INT < 28) {
            this.mFloatingView.findViewById(R.id.control).setVisibility(8);
        } else {
            this.mFloatingView.findViewById(R.id.control).setVisibility(0);
        }
        this.mFloatingView.findViewById(R.id.btnClose).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.FloatingViewService.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                FloatingViewService.this.foreGroundStop();
            }
        });
        this.mFloatingView.findViewById(R.id.btnAccept).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.FloatingViewService.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws NoSuchMethodException, SecurityException {
                Utils.getInstance().acceptCall(FloatingViewService.this.getApplicationContext());
            }
        });
        this.mFloatingView.findViewById(R.id.btnReject).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.FloatingViewService.6
            @Override // android.view.View.OnClickListener
            public void onClick(View view) throws NoSuchMethodException, SecurityException {
                Utils.getInstance().rejectCall(FloatingViewService.this.getApplicationContext());
            }
        });
        this.mFloatingView.findViewById(R.id.titlebar).setOnTouchListener(new View.OnTouchListener() { // from class: com.developer.faker.Service.FloatingViewService.7
            private float initialTouchX;
            private float initialTouchY;
            private int initialX;
            private int initialY;

            @Override // android.view.View.OnTouchListener
            public boolean onTouch(View view, MotionEvent motionEvent) {
                int action = motionEvent.getAction();
                if (action == 0) {
                    this.initialX = layoutParams.x;
                    this.initialY = layoutParams.y;
                    this.initialTouchX = motionEvent.getRawX();
                    this.initialTouchY = motionEvent.getRawY();
                    return true;
                }
                if (action == 1) {
                    motionEvent.getRawX();
                    float f = this.initialTouchX;
                    motionEvent.getRawY();
                    float f2 = this.initialTouchY;
                    return true;
                }
                if (action != 2) {
                    return false;
                }
                layoutParams.x = this.initialX + ((int) (motionEvent.getRawX() - this.initialTouchX));
                layoutParams.y = this.initialY + ((int) (motionEvent.getRawY() - this.initialTouchY));
                FloatingViewService.this.mWindowManager.updateViewLayout(FloatingViewService.this.mFloatingView, layoutParams);
                return true;
            }
        });
    }

    private class BlinkTimer extends TimerTask {
        private int nBlink;

        private BlinkTimer() {
            this.nBlink = 0;
        }

        @Override // java.util.TimerTask, java.lang.Runnable
        @SuppressLint({"ResourceAsColor"})
        public void run() {
            if (this.nBlink == 0) {
                FloatingViewService.this.m_txtNewNotice.setTextColor(Color.rgb(255, 0, 0));
                this.nBlink = 1;
            } else {
                FloatingViewService.this.m_txtNewNotice.setTextColor(Color.rgb(200, 200, 200));
                this.nBlink = 0;
            }
        }
    }

    @Override // android.app.Service
    public void onCreate() {
        if (Build.VERSION.SDK_INT >= 26) {
            O.createNotification(this);
            return;
        }
        Notification.Builder builder = new Notification.Builder(this);
        builder.setContentTitle(BuildConfig.FLAVOR);
        startForeground(1, builder.getNotification());
    }

    @Override // android.app.Service
    public void onDestroy() {
        super.onDestroy();
        View view = this.mFloatingView;
        if (view != null) {
            this.mWindowManager.removeView(view);
        }
        this.timer.cancel();
    }

    @Override // android.app.Service
    public int onStartCommand(Intent intent, int i, int i2) {
        if (Build.VERSION.SDK_INT >= 26) {
            O.createNotification(this);
        } else {
            Notification.Builder builder = new Notification.Builder(this);
            builder.setContentTitle(BuildConfig.FLAVOR);
            startForeground(1, builder.getNotification());
        }
        if (!intent.getAction().contains("ACTION_SHOW_NUMBER")) {
            if (!intent.getAction().contains("ACTION_FOREGROUND_STOP")) {
                return 2;
            }
            foreGroundStop();
            return 2;
        }
        if (intent == null) {
            return 2;
        }
        this.mCallNumber = intent.getStringExtra("tel");
        TextUtils.isEmpty(this.mCallNumber);
        new Handler(Looper.getMainLooper()).post(new Runnable() { // from class: com.developer.faker.Service.FloatingViewService.8
            @Override // java.lang.Runnable
            public void run() {
                FloatingViewService.this.createUI();
            }
        });
        return 2;
    }

    public static class O {
        private static Notification buildNotification(Service service, String str) {
            return new Notification.Builder(service, str).setSmallIcon(Icon.createWithBitmap(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888))).build();
        }

        private static String createChannel(Service service) throws IOException {
            String packageName = service.getPackageName();
            try {
                packageName = (String) service.getPackageManager().getApplicationLabel(service.getPackageManager().getApplicationInfo(service.getPackageName(), 8192));
            } catch (PackageManager.NameNotFoundException e) {
                UtilLogFile.getInstance(service.getApplicationContext()).writeLog(e.toString());
            }
            NotificationManager notificationManager = (NotificationManager) service.getSystemService("notification");
            NotificationChannel notificationChannel = new NotificationChannel(packageName, packageName, 1);
            notificationChannel.setVibrationPattern(new long[]{0});
            notificationManager.createNotificationChannel(notificationChannel);
            return packageName;
        }

        public static void createNotification(Service service) {
            service.startForeground(1, buildNotification(service, createChannel(service)));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Can't wrap try/catch for region: R(20:(5:117|25|26|(8:123|28|29|125|30|31|121|32)(1:39)|40)|(4:42|43|(3:129|45|(1:50))(0)|84)(1:51)|127|52|53|113|54|131|55|59|60|119|61|62|(3:64|115|65)(1:66)|70|71|135|84|23) */
    /* JADX WARN: Can't wrap try/catch for region: R(24:117|25|26|(8:123|28|29|125|30|31|121|32)(1:39)|40|(4:42|43|(3:129|45|(1:50))(0)|84)(1:51)|127|52|53|113|54|131|55|59|60|119|61|62|(3:64|115|65)(1:66)|70|71|135|84|23) */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x01b2, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x01b3, code lost:
    
        r0.printStackTrace();
        r0 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x026d, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x026f, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x0270, code lost:
    
        r19 = r8;
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x0272, code lost:
    
        r14 = r22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x0275, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x0276, code lost:
    
        r19 = r8;
     */
    /* JADX WARN: Removed duplicated region for block: B:50:0x0195  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void onResultFromServer(int r21, java.lang.Object r22) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 1067
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.developer.faker.Service.FloatingViewService.onResultFromServer(int, java.lang.Object):void");
    }
}