package com.developer.faker.Service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.view.View;
import com.developer.faker.Data.RecentIncomeInfo;
import com.developer.faker.Fragment.MainFragment;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.UtilBlock;
import com.developer.faker.Utils.UtilLogFile;
import com.developer.faker.Utils.Utils;
import java.io.IOException;
import java.util.Date;

/* loaded from: classes.dex */
public class NewPhonecallReceiver extends BroadcastReceiver {
    private static Date callStartTime;
    public static boolean isIncoming;
    private static int lastState;
    private static String savedNumber;
    private Context mContext = null;
    View view;

    public void onOutgoingCallStarted(Context context, String str, Date date) {
    }

    private void RefreshLog() {
        if (Global.fragment_State != Const.FRAGMENT_STATE_MAIN || MainFragment.m_instance == null) {
            return;
        }
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Service.NewPhonecallReceiver.1
            @Override // java.lang.Runnable
            public void run() throws IOException {
                try {
                    MainFragment.m_instance.RefreshCallLog();
                } catch (Exception e) {
                    UtilLogFile.getInstance(NewPhonecallReceiver.this.mContext).writeLog(e.toString());
                }
            }
        }, 1000L);
    }

    private void createUI(Context context, String str, String str2) {
        Intent intent = new Intent(context, (Class<?>) FloatingViewService.class);
        Utils.getInstance();
        intent.putExtra("tel", Utils.getHypenPhoneNumber(str));
        intent.setAction(str2);
        if (Build.VERSION.SDK_INT >= 26) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
        if (str2 == "ACTION_FOREGROUND_STOP") {
            RefreshLog();
        }
    }

    public void onCallStateChanged(Context context, int i, String str) throws NoSuchMethodException, SecurityException {
        this.mContext = context;
        if (lastState == i || str == null || str.isEmpty()) {
            return;
        }
        String correctPhoneNumber = Utils.getCorrectPhoneNumber(str);
        if (i != 0) {
            if (i == 1) {
                int iIsNeedBlock = UtilBlock.getInstance(context).IsNeedBlock(correctPhoneNumber);
                if (iIsNeedBlock > 0) {
                    Utils.getInstance().rejectCall(context);
                    UtilBlock.getInstance(context).addBlockHistory(correctPhoneNumber, iIsNeedBlock, System.currentTimeMillis());
                    return;
                } else {
                    isIncoming = true;
                    callStartTime = new Date();
                    savedNumber = correctPhoneNumber;
                    onIncomingCallStarted(context, correctPhoneNumber, callStartTime);
                }
            } else if (i == 2 && lastState != 1) {
                isIncoming = false;
                callStartTime = new Date();
                onOutgoingCallStarted(context, savedNumber, callStartTime);
            }
        } else if (lastState == 1) {
            onMissedCall(context, savedNumber, callStartTime);
        } else if (isIncoming) {
            onIncomingCallEnded(context, savedNumber, callStartTime, new Date());
        } else {
            onOutgoingCallEnded(context, savedNumber, callStartTime, new Date());
        }
        lastState = i;
    }

    protected void onIncomingCallEnded(Context context, String str, Date date, Date date2) {
        createUI(context, str, "ACTION_FOREGROUND_STOP");
        SendCallResponse();
    }

    protected void onIncomingCallStarted(Context context, String str, Date date) {
        createUI(context, str, "ACTION_SHOW_NUMBER");
    }

    protected void onMissedCall(Context context, String str, Date date) {
        createUI(context, str, "ACTION_FOREGROUND_STOP");
        SendCallResponse();
    }

    public void onOutgoingCallEnded(Context context, String str, Date date, Date date2) {
        SendCallResponse();
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0051  */
    @Override // android.content.BroadcastReceiver
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void onReceive(android.content.Context r5, android.content.Intent r6) {
        /*
            r4 = this;
            com.developer.faker.Utils.UtilAuth r0 = com.developer.faker.Utils.UtilAuth.getInstance(r5)     // Catch: java.lang.Exception -> Lc8
            java.lang.Boolean r0 = r0.isHaveToken()     // Catch: java.lang.Exception -> Lc8
            boolean r0 = r0.booleanValue()     // Catch: java.lang.Exception -> Lc8
            if (r0 != 0) goto Lf
            return
        Lf:
            java.lang.String r0 = r6.getAction()     // Catch: java.lang.Exception -> Lc8
            java.lang.String r1 = "android.intent.action.NEW_OUTGOING_CALL"
            boolean r0 = r0.equals(r1)     // Catch: java.lang.Exception -> Lc8
            r1 = 0
            if (r0 == 0) goto L29
            android.os.Bundle r0 = r6.getExtras()     // Catch: java.lang.Exception -> Lc8
            java.lang.String r2 = "android.intent.extra.PHONE_NUMBER"
            java.lang.String r0 = r0.getString(r2)     // Catch: java.lang.Exception -> Lc8
            com.developer.faker.Service.NewPhonecallReceiver.savedNumber = r0     // Catch: java.lang.Exception -> Lc8
            goto L84
        L29:
            java.lang.String r0 = r6.getAction()     // Catch: java.lang.Exception -> Lc8
            java.lang.String r2 = "android.intent.action.PHONE_STATE"
            boolean r0 = r0.equals(r2)     // Catch: java.lang.Exception -> Lc8
            if (r0 == 0) goto L84
            android.os.Bundle r0 = r6.getExtras()     // Catch: java.lang.Exception -> Lc8
            java.lang.String r2 = "state"
            java.lang.String r0 = r0.getString(r2)     // Catch: java.lang.Exception -> Lc8
            android.os.Bundle r2 = r6.getExtras()     // Catch: java.lang.Exception -> Lc8
            java.lang.String r3 = "incoming_number"
            java.lang.String r2 = r2.getString(r3)     // Catch: java.lang.Exception -> Lc8
            java.lang.String r3 = android.telephony.TelephonyManager.EXTRA_STATE_IDLE     // Catch: java.lang.Exception -> Lc8
            boolean r3 = r0.equals(r3)     // Catch: java.lang.Exception -> Lc8
            if (r3 == 0) goto L53
        L51:
            r0 = 0
            goto L81
        L53:
            java.lang.String r3 = android.telephony.TelephonyManager.EXTRA_STATE_OFFHOOK     // Catch: java.lang.Exception -> Lc8
            boolean r3 = r0.equals(r3)     // Catch: java.lang.Exception -> Lc8
            if (r3 == 0) goto L78
            r0 = 2
            if (r2 == 0) goto L81
            boolean r3 = r2.isEmpty()     // Catch: java.lang.Exception -> Lc8
            if (r3 != 0) goto L81
            com.developer.faker.Utils.UtilSetting r3 = com.developer.faker.Utils.UtilSetting.getInstance(r5)     // Catch: java.lang.Exception -> Lc8
            boolean r3 = r3.POPUP_REMAIN     // Catch: java.lang.Exception -> Lc8
            if (r3 == 0) goto L72
            java.lang.String r3 = ""
            r4.createUI(r5, r2, r3)     // Catch: java.lang.Exception -> Lc8
            goto L81
        L72:
            java.lang.String r3 = "ACTION_FOREGROUND_STOP"
            r4.createUI(r5, r2, r3)     // Catch: java.lang.Exception -> Lc8
            goto L81
        L78:
            java.lang.String r3 = android.telephony.TelephonyManager.EXTRA_STATE_RINGING     // Catch: java.lang.Exception -> Lc8
            boolean r0 = r0.equals(r3)     // Catch: java.lang.Exception -> Lc8
            if (r0 == 0) goto L51
            r0 = 1
        L81:
            r4.onCallStateChanged(r5, r0, r2)     // Catch: java.lang.Exception -> Lc8
        L84:
            java.lang.String r0 = "android.provider.Telephony.SMS_RECEIVED"
            java.lang.String r2 = r6.getAction()     // Catch: java.lang.Exception -> Lc8
            boolean r0 = r0.equals(r2)     // Catch: java.lang.Exception -> Lc8
            if (r0 == 0) goto Lc8
            android.os.Bundle r6 = r6.getExtras()     // Catch: java.lang.Exception -> Lc8
            java.lang.String r0 = "pdus"
            java.lang.Object r6 = r6.get(r0)     // Catch: java.lang.Exception -> Lc8
            java.lang.Object[] r6 = (java.lang.Object[]) r6     // Catch: java.lang.Exception -> Lc8
            java.lang.Object[] r6 = (java.lang.Object[]) r6     // Catch: java.lang.Exception -> Lc8
            int r0 = r6.length     // Catch: java.lang.Exception -> Lc8
            if (r0 <= 0) goto Lc8
            int r0 = r6.length     // Catch: java.lang.Exception -> Lc8
            android.telephony.SmsMessage[] r0 = new android.telephony.SmsMessage[r0]     // Catch: java.lang.Exception -> Lc8
            r2 = 0
        La5:
            int r3 = r6.length     // Catch: java.lang.Exception -> Lc8
            if (r2 >= r3) goto Lb7
            r3 = r6[r2]     // Catch: java.lang.Exception -> Lc8
            byte[] r3 = (byte[]) r3     // Catch: java.lang.Exception -> Lc8
            byte[] r3 = (byte[]) r3     // Catch: java.lang.Exception -> Lc8
            android.telephony.SmsMessage r3 = android.telephony.SmsMessage.createFromPdu(r3)     // Catch: java.lang.Exception -> Lc8
            r0[r2] = r3     // Catch: java.lang.Exception -> Lc8
            int r2 = r2 + 1
            goto La5
        Lb7:
            r6 = r0[r1]     // Catch: java.lang.Exception -> Lc8
            java.lang.String r6 = r6.getOriginatingAddress()     // Catch: java.lang.Exception -> Lc8
            com.developer.faker.Utils.Utils r0 = com.developer.faker.Utils.Utils.getInstance()     // Catch: java.lang.Exception -> Lc8
            r1 = 4
            r0.SendCallResult(r5, r6, r1)     // Catch: java.lang.Exception -> Lc8
            r4.RefreshLog()     // Catch: java.lang.Exception -> Lc8
        Lc8:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.developer.faker.Service.NewPhonecallReceiver.onReceive(android.content.Context, android.content.Intent):void");
    }

    private void SendCallResponse() {
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Service.NewPhonecallReceiver.2
            @Override // java.lang.Runnable
            public void run() throws NumberFormatException {
                RecentIncomeInfo latestCall = Utils.getInstance().getLatestCall(NewPhonecallReceiver.this.mContext);
                if (latestCall == null || latestCall.phoneNumber == null) {
                    return;
                }
                String str = latestCall.phoneNumber;
                NewPhonecallReceiver newPhonecallReceiver = NewPhonecallReceiver.this;
                if (str.equals(NewPhonecallReceiver.savedNumber)) {
                    int i = 0;
                    if (latestCall.callTypeDetail == 5) {
                        i = 1;
                    } else if (latestCall.callTypeDetail == 1) {
                        i = 2;
                    } else if (latestCall.callTypeDetail == 3) {
                        i = 3;
                    }
                    Utils utils = Utils.getInstance();
                    Context context = NewPhonecallReceiver.this.mContext;
                    NewPhonecallReceiver newPhonecallReceiver2 = NewPhonecallReceiver.this;
                    utils.SendCallResult(context, NewPhonecallReceiver.savedNumber, i);
                }
            }
        }, 1000L);
    }
}