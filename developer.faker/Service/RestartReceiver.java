package com.developer.faker.Service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

/* loaded from: classes.dex */
public class RestartReceiver extends BroadcastReceiver {
    public static String ACTION_RESTART_ALARM_SERVICE = "ACTION_RESTART_ALARM_SERVICE";

    @Override // android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        if (intent.getAction().equals(ACTION_RESTART_ALARM_SERVICE)) {
            Intent intent2 = new Intent(context, (Class<?>) MainService.class);
            if (Build.VERSION.SDK_INT >= 26) {
                context.startForegroundService(intent2);
            } else {
                context.startService(intent2);
            }
        }
    }
}