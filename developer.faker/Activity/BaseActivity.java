package com.developer.faker.Activity;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.ProgressDialog;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.support.v4.app.NotificationCompat;
import android.support.v7.app.AppCompatActivity;
import android.view.inputmethod.InputMethodManager;
import android.widget.Toast;
import com.developer.faker.Fragment.BlockFragment;
import com.developer.faker.Fragment.MainFragment;
import com.developer.faker.Fragment.NoticeFragment;
import com.developer.faker.Fragment.SearchFragment;
import com.developer.faker.Fragment.SettingFragment;
import com.developer.faker.Fragment.WebSearchFragment;
import com.developer.faker.R;
import com.developer.faker.Service.MainService;
import com.developer.faker.Service.RestartReceiver;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.UtilLogFile;
import com.developer.faker.Utils.Utils;
import com.mashape.relocation.HttpStatus;
import com.mashape.unirest.http.options.Options;
import java.io.IOException;

/* loaded from: classes.dex */
public class BaseActivity extends AppCompatActivity {
    private int MENU_DELAY = HttpStatus.SC_MULTIPLE_CHOICES;
    protected InputMethodManager inputMethodManager;
    private ProgressDialog mProgressDlg;

    @Override // android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.support.v4.app.SupportActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getWindow().addFlags(8192);
    }

    public boolean hideSoftKeyboard() {
        try {
            if (getCurrentFocus() != null) {
                this.inputMethodManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public void ShowProgress(String str) throws IOException {
        try {
            if (this.mProgressDlg != null && this.mProgressDlg.isShowing()) {
                this.mProgressDlg.dismiss();
            }
            this.mProgressDlg = new ProgressDialog(this);
            this.mProgressDlg.setMessage(str);
            this.mProgressDlg.setIndeterminate(true);
            this.mProgressDlg.setProgressStyle(0);
            this.mProgressDlg.setCancelable(false);
            this.mProgressDlg.show();
        } catch (Exception e) {
            UtilLogFile.getInstance(getBaseContext()).writeLog(e.toString());
        }
    }

    public void DismissProgress() throws IOException {
        try {
            if (this.mProgressDlg == null || !this.mProgressDlg.isShowing()) {
                return;
            }
            this.mProgressDlg.dismiss();
        } catch (Exception e) {
            UtilLogFile.getInstance(getBaseContext()).writeLog(e.toString());
        }
    }

    protected void showToast(String str) {
        Toast.makeText(this, str, 0).show();
    }

    public void gotoMainFragment() {
        final MainFragment mainFragment = new MainFragment();
        Global.fragment_State = Const.FRAGMENT_STATE_MAIN;
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Activity.BaseActivity.1
            @Override // java.lang.Runnable
            public void run() {
                BaseActivity.this.getSupportFragmentManager().beginTransaction().replace(R.id.mainLayout, mainFragment).commit();
            }
        }, this.MENU_DELAY);
    }

    public void gotoSearchFragment() {
        final SearchFragment searchFragment = new SearchFragment();
        Global.fragment_State = Const.FRAGMENT_STATE_SEARCH;
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Activity.BaseActivity.2
            @Override // java.lang.Runnable
            public void run() {
                BaseActivity.this.getSupportFragmentManager().beginTransaction().replace(R.id.mainLayout, searchFragment).commit();
            }
        }, this.MENU_DELAY);
    }

    public void gotoWebSearchFragment(String str) {
        final WebSearchFragment webSearchFragmentNewInstance = WebSearchFragment.NewInstance(str);
        Global.fragment_State = Const.FRAGMENT_STATE_WEB;
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Activity.BaseActivity.3
            @Override // java.lang.Runnable
            public void run() {
                BaseActivity.this.getSupportFragmentManager().beginTransaction().replace(R.id.mainLayout, webSearchFragmentNewInstance).commit();
            }
        }, this.MENU_DELAY);
    }

    public void gotoNoticeFragment() {
        Utils.getInstance().setNewNoticeState(getBaseContext(), false);
        final NoticeFragment noticeFragment = new NoticeFragment();
        Global.fragment_State = Const.FRAGMENT_STATE_NOTICE;
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Activity.BaseActivity.4
            @Override // java.lang.Runnable
            public void run() {
                BaseActivity.this.getSupportFragmentManager().beginTransaction().replace(R.id.mainLayout, noticeFragment).commit();
            }
        }, this.MENU_DELAY);
    }

    public void gotoSettingFragment() {
        final SettingFragment settingFragment = new SettingFragment();
        Global.fragment_State = Const.FRAGMENT_STATE_SETTING;
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Activity.BaseActivity.5
            @Override // java.lang.Runnable
            public void run() {
                BaseActivity.this.getSupportFragmentManager().beginTransaction().replace(R.id.mainLayout, settingFragment).commit();
            }
        }, this.MENU_DELAY);
    }

    public void gotoBlockFragment() {
        final BlockFragment blockFragment = new BlockFragment();
        Global.fragment_State = Const.FRAGMENT_STATE_BLOCK;
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Activity.BaseActivity.6
            @Override // java.lang.Runnable
            public void run() {
                BaseActivity.this.getSupportFragmentManager().beginTransaction().replace(R.id.mainLayout, blockFragment).commit();
            }
        }, this.MENU_DELAY);
    }

    public void startMyService() {
        Intent intent = new Intent(this, (Class<?>) MainService.class);
        if (Build.VERSION.SDK_INT >= 26) {
            startForegroundService(intent);
        } else {
            startService(intent);
        }
        AlarmManager alarmManager = (AlarmManager) getSystemService(NotificationCompat.CATEGORY_ALARM);
        Intent intent2 = new Intent(this, (Class<?>) RestartReceiver.class);
        intent2.setAction(RestartReceiver.ACTION_RESTART_ALARM_SERVICE);
        alarmManager.setRepeating(0, System.currentTimeMillis(), Options.CONNECTION_TIMEOUT, PendingIntent.getBroadcast(this, 0, intent2, 67108864));
    }
}