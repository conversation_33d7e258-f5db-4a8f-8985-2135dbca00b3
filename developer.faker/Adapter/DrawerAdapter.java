package com.developer.faker.Adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.DrawerData;
import com.developer.faker.R;
import com.developer.faker.Utils.Utils;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class DrawerAdapter extends ArrayAdapter<DrawerData> {
    BaseActivity activity;

    public DrawerAdapter(BaseActivity baseActivity, int i, ArrayList<DrawerData> arrayList) {
        super(baseActivity, i, arrayList);
        this.activity = baseActivity;
    }

    @Override // android.widget.ArrayAdapter, android.widget.Adapter
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);
        }
        bindView(i, view);
        return view;
    }

    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_drawer, viewGroup, false);
    }

    private void bindView(int i, View view) {
        ImageView imageView = (ImageView) view.findViewById(R.id.iconIV);
        TextView textView = (TextView) view.findViewById(R.id.nameTV);
        TextView textView2 = (TextView) view.findViewById(R.id.txtNew);
        DrawerData item = getItem(i);
        imageView.setImageResource(item.icon.intValue());
        textView.setText(item.name);
        if (i == 2) {
            if (Utils.getInstance().getNewNoticeState(getContext())) {
                textView2.setVisibility(0);
            } else {
                textView2.setVisibility(4);
            }
        }
    }
}