package com.developer.faker.Adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import com.developer.faker.Data.SearchResultData;
import com.developer.faker.R;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

/* loaded from: classes.dex */
public class SearchResultListAdapter extends ArrayAdapter<SearchResultData> {
    final int[] category_type;
    Context mContext;
    final int[] response_type;

    public SearchResultListAdapter(Context context, int i, ArrayList<SearchResultData> arrayList) {
        super(context, i, arrayList);
        this.response_type = new int[]{R.drawable.icon_none, <PERSON>.drawable.icon_reject, R.drawable.icon_accept, R.drawable.icon_miss, R.drawable.icon_sms};
        this.category_type = new int[]{R.drawable.icon_0, R.drawable.icon_1, R.drawable.icon_2, R.drawable.icon_3, R.drawable.icon_4, R.drawable.icon_5, R.drawable.icon_6, R.drawable.icon_7, R.drawable.icon_8, R.drawable.icon_9, R.drawable.icon_10, R.drawable.icon_11, R.drawable.icon_12};
        this.mContext = context;
    }

    @Override // android.widget.ArrayAdapter, android.widget.Adapter
    public View getView(int i, View view, ViewGroup viewGroup) throws ParseException {
        if (view == null) {
            view = newView(viewGroup);
        }
        bindView(i, view);
        return view;
    }

    private View newView(ViewGroup viewGroup) {
        return ((LayoutInflater) this.mContext.getSystemService("layout_inflater")).inflate(R.layout.adapter_phone, (ViewGroup) null);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r3v2, types: [android.widget.TextView] */
    /* JADX WARN: Type inference failed for: r6v10, types: [java.lang.CharSequence, java.lang.String] */
    /* JADX WARN: Type inference failed for: r6v2, types: [com.developer.faker.Data.SearchResultData] */
    /* JADX WARN: Type inference failed for: r6v3 */
    /* JADX WARN: Type inference failed for: r7v2, types: [android.widget.ImageView] */
    /* JADX WARN: Type inference failed for: r7v3, types: [android.widget.ImageView] */
    /* JADX WARN: Type inference failed for: r7v8, types: [java.lang.StringBuilder] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:9:0x0063 -> B:15:0x006e). Please report as a decompilation issue!!! */
    private void bindView(int i, View view) throws ParseException {
        SearchResultData searchResultData;
        TextView textView = (TextView) view.findViewById(R.id.txtCompany);
        TextView textView2 = (TextView) view.findViewById(R.id.txtContact);
        TextView textView3 = (TextView) view.findViewById(R.id.txtUpdateDate);
        ?? r3 = (TextView) view.findViewById(R.id.txtUpdateTime);
        ?? sb = (ImageView) view.findViewById(R.id.imgAction);
        SearchResultData item = getItem(i);
        textView.setText(item.compamy);
        textView2.setText(item.memo);
        textView2.setTextColor(item.color);
        textView3.setText(item.date);
        try {
            if (item.action >= 256) {
                sb.setImageResource(this.category_type[item.action - 256]);
                searchResultData = item;
            } else {
                sb.setImageResource(this.response_type[item.action]);
                searchResultData = item;
            }
        } catch (Exception e) {
            sb.setImageResource(this.category_type[0]);
            e.printStackTrace();
            searchResultData = item;
        }
        try {
            Date date = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss").parse(searchResultData.date);
            textView3.setText(new SimpleDateFormat("yyyy-MM-dd").format(date));
            String str = new SimpleDateFormat("HH:mm:ss").format(date);
            sb = new StringBuilder();
            sb.append(str);
            sb.append("  ");
            item = sb.toString();
            r3.setText(item);
        } catch (ParseException e2) {
            e2.printStackTrace();
        }
    }
}