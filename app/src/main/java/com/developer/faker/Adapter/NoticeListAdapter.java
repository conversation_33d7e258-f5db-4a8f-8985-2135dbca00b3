package com.developer.faker.Adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.NoticeInfo;
import com.developer.faker.R;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class NoticeListAdapter extends ArrayAdapter<NoticeInfo> {
    BaseActivity activity;

    public NoticeListAdapter(BaseActivity baseActivity, int i, ArrayList<NoticeInfo> arrayList) {
        super(baseActivity, i, arrayList);
        this.activity = baseActivity;

    @Override // android.widget.ArrayAdapter, android.widget.Adapter
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);
        bindView(i, view);
        return view;

    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_noticelist, viewGroup, false);

    private void bindView(int i, View view) {
        NoticeInfo item = getItem(i);
        ((TextView) view.findViewById(R.id.txtNoticeTitle)).setText(item.subject);
        ((TextView) view.findViewById(R.id.txtNoticeDate)).setText(item.date);
        ((TextView) view.findViewById(R.id.txtNoticeContent)).setText(item.content);
