package com.developer.faker.Adapter;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.v4.os.EnvironmentCompat;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.BlockNumberHistory;
import com.developer.faker.R;
import com.developer.faker.Utils.Utils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/* loaded from: classes.dex */
public class BlockNumberHistoryAdapter extends ArrayAdapter<BlockNumberHistory> {
    BaseActivity activity;

    public BlockNumberHistoryAdapter(@NonNull Context context, int i, @NonNull List<BlockNumberHistory> list) {
        super(context, i, list);
        this.activity = (BaseActivity) context;

    @Override // android.widget.ArrayAdapter, android.widget.Adapter
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);
        bindView(i, view);
        return view;

    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_blockhistory, viewGroup, false);

    private void bindView(int i, View view) {
        String str;
        BlockNumberHistory item = getItem(i);
        TextView textView = (TextView) view.findViewById(R.id.txt_phone);
        TextView textView2 = (TextView) view.findViewById(R.id.txtDate);
        TextView textView3 = (TextView) view.findViewById(R.id.txtBlockComment);
        textView.setText(Utils.getHypenPhoneNumber(item.number));
        textView2.setText(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(item.dateTick)));
        switch (item.type) {
            case 1:
                str = "모르는번호 차단";
                break;
            case 2:
                str = "오늘전화문의 차단";
                break;
            case 3:
                str = "지정번호 차단";
                break;
            case 4:
                str = "시작번호 차단";
                break;
            case 5:
                str = "모든번호 차단";
                break;
            case 6:
                str = "콜폭 차단";
                break;
            default:
                str = EnvironmentCompat.MEDIA_UNKNOWN;
                break;
        textView3.setText(str);
        view.setTag(item);
