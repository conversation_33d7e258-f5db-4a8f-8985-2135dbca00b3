package com.developer.faker.Data;

import android.support.annotation.Nullable;

/* loaded from: classes.dex */
public class RecentIncomeInfo implements Comparable<RecentIncomeInfo> {
    public String callDate;
    public int callType;
    public int callTypeDetail;
    public String color;
    public String contactName;
    public String phoneNumber;

    @Override // java.lang.Comparable
    public int compareTo(RecentIncomeInfo recentIncomeInfo) {
        return this.callDate.compareTo(recentIncomeInfo.callDate) * (-1);
    }

    public boolean equals(@Nullable Object obj) {
        return ((RecentIncomeInfo) obj).phoneNumber == this.phoneNumber;
    }
}
