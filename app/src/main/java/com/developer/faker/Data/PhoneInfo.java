package com.developer.faker.Data;

import android.support.annotation.Nullable;
import java.text.SimpleDateFormat;
import java.util.Date;

/* loaded from: classes.dex */
public class PhoneInfo {
    public int id;
    public String phoneNumber;
    public long updatetime;
    public String userName;

    public String getUpdateTimeString() {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(this.updatetime));
        } catch (Exception e) {
            e.printStackTrace();
            return "2000-01-01 00:00:00";
        }
    }

    public boolean equals(@Nullable Object obj) {
        PhoneInfo phoneInfo = (PhoneInfo) obj;
        return phoneInfo.userName == this.userName && phoneInfo.phoneNumber == this.phoneNumber;
    }
}
