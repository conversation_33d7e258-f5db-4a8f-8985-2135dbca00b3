package com.developer.faker.Fragment;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import com.developer.faker.Adapter.NoticeListAdapter;
import com.developer.faker.Data.NoticeInfo;
import com.developer.faker.R;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.Utils;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class NoticeFragment extends BaseFragment {
    ListView lstReport;
    private Handler m_WaitHandler;
    private NoticeListAdapter reportListAdapter;
    JSONObject responseData;
    TextView txtDetailContent;
    TextView txtDetailDate;
    TextView txtDetailTitle;
    View vDetail;
    View vList;
    View view;

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            this.view = layoutInflater.inflate(R.layout.fragment_notice, viewGroup, false);
            initUI(this.view);
        }
        return this.view;
    }

    private void initUI(View view) {
        this.lstReport = (ListView) view.findViewById(R.id.lstReport);
        this.vDetail = view.findViewById(R.id.viewDetail);
        this.vList = view.findViewById(R.id.lstReport);
        this.txtDetailDate = (TextView) view.findViewById(R.id.txtNoticeDate);
        this.txtDetailTitle = (TextView) view.findViewById(R.id.txtNoticeTitle);
        this.txtDetailContent = (TextView) view.findViewById(R.id.txtNoticeContent);
        this.txtDetailContent.setMovementMethod(new ScrollingMovementMethod());
        ShowDetail(false);
        view.findViewById(R.id.btnGoList).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.NoticeFragment.1
            AnonymousClass1() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                NoticeFragment.this.ShowDetail(false);
            }
        });
        getSearchResultFromServer(0, 40);
    }

    /* renamed from: com.developer.faker.Fragment.NoticeFragment$1 */
    class AnonymousClass1 implements View.OnClickListener {
        AnonymousClass1() {
        }

        @Override // android.view.View.OnClickListener
        public void onClick(View view2) {
            NoticeFragment.this.ShowDetail(false);
        }
    }

    public void refreshUI() {
        this.reportListAdapter = new NoticeListAdapter(this.baseActivity, 0, Global.NoticeList);
        this.lstReport.setAdapter((ListAdapter) this.reportListAdapter);
        this.lstReport.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.developer.faker.Fragment.NoticeFragment.2
            AnonymousClass2() {
            }

            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
                NoticeInfo noticeInfo = Global.NoticeList.get(i);
                NoticeFragment.this.txtDetailDate.setText(noticeInfo.date);
                NoticeFragment.this.txtDetailTitle.setText(noticeInfo.subject);
                NoticeFragment.this.txtDetailContent.setText(noticeInfo.content);
                NoticeFragment.this.ShowDetail(true);
            }
        });
    }

    /* renamed from: com.developer.faker.Fragment.NoticeFragment$2 */
    class AnonymousClass2 implements AdapterView.OnItemClickListener {
        AnonymousClass2() {
        }

        @Override // android.widget.AdapterView.OnItemClickListener
        public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
            NoticeInfo noticeInfo = Global.NoticeList.get(i);
            NoticeFragment.this.txtDetailDate.setText(noticeInfo.date);
            NoticeFragment.this.txtDetailTitle.setText(noticeInfo.subject);
            NoticeFragment.this.txtDetailContent.setText(noticeInfo.content);
            NoticeFragment.this.ShowDetail(true);
        }
    }

    /* renamed from: com.developer.faker.Fragment.NoticeFragment$3 */
    class AnonymousClass3 extends Handler {
        AnonymousClass3() {
        }

        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            NoticeFragment.this.refreshUI();
            NoticeFragment.this.dismissProgress();
        }
    }

    private void getSearchResultFromServer(int i, int i2) {
        this.m_WaitHandler = new Handler() { // from class: com.developer.faker.Fragment.NoticeFragment.3
            AnonymousClass3() {
            }

            @Override // android.os.Handler
            public void handleMessage(Message message) {
                super.handleMessage(message);
                NoticeFragment.this.refreshUI();
                NoticeFragment.this.dismissProgress();
            }
        };
        showProgress(getResources().getString(R.string.wait));
        Utils.getInstance().getNoticeDataFromServer(getContext(), this.m_WaitHandler);
    }

    public void ShowDetail(boolean z) {
        this.txtDetailContent.scrollTo(0, 0);
        if (z) {
            this.vDetail.setVisibility(0);
            this.vList.setVisibility(4);
        } else {
            this.vDetail.setVisibility(4);
            this.vList.setVisibility(0);
        }
    }
}
