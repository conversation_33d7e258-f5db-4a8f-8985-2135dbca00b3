package com.developer.faker.Fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import com.developer.faker.R;

/* loaded from: classes.dex */
public class WebSearchFragment extends BaseFragment {
    private static final String ARG_URL = "web_url";
    private String strUrl;
    View view;
    WebView webView;

    public static WebSearchFragment NewInstance(String str) {
        WebSearchFragment webSearchFragment = new WebSearchFragment();
        Bundle bundle = new Bundle();
        bundle.putString(ARG_URL, str);
        webSearchFragment.setArguments(bundle);
        return webSearchFragment;

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (getArguments() != null) {
            this.strUrl = getArguments().getString(ARG_URL);
        if (this.view == null) {
            this.view = layoutInflater.inflate(R.layout.fragment_websearch, viewGroup, false);
            initUI(this.view);
        return this.view;

    private void initUI(View view) {
        this.webView = (WebView) view.findViewById(R.id.webView);
        this.webView.getSettings().setJavaScriptEnabled(true);
        this.webView.getSettings().setDomStorageEnabled(true);
        this.webView.loadUrl(this.strUrl);
        this.webView.setWebViewClient(new WebViewClient());
        this.webView.setInitialScale(1);
        this.webView.getSettings().setBuiltInZoomControls(true);
        this.webView.getSettings().setUseWideViewPort(true);
