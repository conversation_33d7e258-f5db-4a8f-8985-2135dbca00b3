package com.developer.faker.Fragment;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;
import com.developer.faker.Activity.LoginActivity;
import com.developer.faker.Adapter.SearchResultListAdapter;
import com.developer.faker.BuildConfig;
import com.developer.faker.Data.SearchResultData;
import com.developer.faker.Model.MyException;
import com.developer.faker.R;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.RC4;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilContact;
import com.developer.faker.Utils.UtilSetting;
import com.developer.faker.Utils.Utils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class SearchFragment extends BaseFragment implements View.OnClickListener {
    private Handler QueryPhoneNumberResultHandler = new Handler() { // from class: com.developer.faker.Fragment.SearchFragment.1
        AnonymousClass1() {
        }

        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            SearchFragment.this.dismissProgress();
            if (message.what == 0) {
                try {
                    SearchFragment.this.refreshUI(new JSONObject(message.obj.toString()));
                    return;
                } catch (Exception e) {
                    e.printStackTrace();
                    return;
                }
            }
            UtilAuth utilAuth = UtilAuth.getInstance(SearchFragment.this.getContext());
            utilAuth.UserToken = null;
            utilAuth.saveAuthInfo();
            Intent intent = new Intent(SearchFragment.this.baseActivity.getBaseContext(), (Class<?>) LoginActivity.class);
            intent.addFlags(67108864);
            intent.addFlags(1048576);
            SearchFragment.this.startActivity(intent);
            SearchFragment.this.baseActivity.overridePendingTransition(R.anim.slidein, R.anim.slideout);
            SearchFragment.this.baseActivity.finish();
        }
    };
    View facebookBT;
    View googleBT;
    View lineBT;
    FrameLayout m_layoutWatermark;
    View makeCallBT;
    TextView searchResultCount;
    TextView searchResultEmptyTV;
    ListView searchResultLV;
    SearchResultListAdapter searchResultListAdapter;
    TextView searchResultTV;
    View sendSMSBT;
    View talkBT;
    View view;

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            this.view = layoutInflater.inflate(R.layout.fragment_search, viewGroup, false);
            initUI(this.view);
        }
        return this.view;
    }

    private void initUI(View view) {
        String str;
        this.searchResultTV = (TextView) view.findViewById(R.id.searchresultTX);
        this.searchResultCount = (TextView) view.findViewById(R.id.searchResultCount);
        this.searchResultLV = (ListView) view.findViewById(R.id.searchResultLV);
        this.searchResultEmptyTV = (TextView) view.findViewById(R.id.searchResultEmptyTV);
        this.searchResultLV.setVisibility(8);
        this.searchResultEmptyTV.setVisibility(0);
        this.m_layoutWatermark = (FrameLayout) view.findViewById(R.id.layoutWatermark);
        String strReplaceAll = Global.Incoming_Call_Number.replaceAll("-", BuildConfig.FLAVOR);
        try {
            str = strReplaceAll.substring(0, 3) + "-" + strReplaceAll.substring(3, 7) + "-" + strReplaceAll.substring(7);
        } catch (Exception unused) {
            str = strReplaceAll;
        }
        this.searchResultTV.setText("전화번호 : " + str);
        this.makeCallBT = view.findViewById(R.id.makeCallBT);
        this.sendSMSBT = view.findViewById(R.id.sendSMSBT);
        this.googleBT = view.findViewById(R.id.googleBT);
        this.facebookBT = view.findViewById(R.id.facebookBT);
        this.talkBT = view.findViewById(R.id.talkBT);
        this.lineBT = view.findViewById(R.id.lineBT);
        this.makeCallBT.setOnClickListener(this);
        this.sendSMSBT.setOnClickListener(this);
        this.googleBT.setOnClickListener(this);
        this.facebookBT.setOnClickListener(this);
        this.talkBT.setOnClickListener(this);
        this.lineBT.setOnClickListener(this);
        this.m_layoutWatermark = (FrameLayout) view.findViewById(R.id.layoutWatermark);
        this.m_layoutWatermark.setBackground(new BitmapDrawable(getContext().getResources(), Utils.getInstance().MakeWaterMark(BitmapFactory.decodeResource(getResources(), R.drawable.search_back), UtilAuth.getInstance(getContext()).UserCompany, 20)));
        getSearchResultFromServer(strReplaceAll);
    }

    /* JADX WARN: Removed duplicated region for block: B:61:0x011e  */
    /* JADX WARN: Removed duplicated region for block: B:62:0x0139  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public void refreshUI(JSONObject jSONObject) throws MyException, JSONException, ParseException {
        int length;
        JSONObject jSONObject2;
        new JSONArray();
        new JSONObject();
        byte[] bArr = new byte[0];
        ArrayList arrayList = new ArrayList();
        this.searchResultEmptyTV.setText(R.string.empty_data);
        try {
            jSONObject2 = new JSONObject(new String(RC4.getInstance().decrypt(jSONObject.get("data").toString(), Const.AUTH_KEY).getBytes("UTF-8")));
        } catch (MyException unused) {
            length = 0;
        } catch (Exception unused2) {
            length = 0;
        }
        if (jSONObject2.has("Error")) {
            this.searchResultEmptyTV.setText(jSONObject2.getString("Error"));
            throw new MyException("Error");
        }
        JSONArray jSONArray = new JSONArray(jSONObject2.getString("Data"));
        Global.RemainQueryCount = jSONObject2.getInt("RemainQueryCount");
        UtilAuth.getInstance(getContext()).SetRemainMinutes(jSONObject2.getInt("RemainMinutes"));
        length = jSONArray.length();
        for (int i = 0; i < jSONArray.length(); i++) {
            try {
                try {
                    SearchResultData searchResultData = new SearchResultData();
                    String string = jSONArray.getJSONObject(i).getString("UpdatedDate");
                    Calendar calendar = Calendar.getInstance();
                    Date date = null;
                    try {
                        date = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(string);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    calendar.setTime(date);
                    searchResultData.date = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss").format(calendar.getTime());
                    searchResultData.memo = jSONArray.getJSONObject(i).getString("Memo");
                    searchResultData.color = jSONArray.getJSONObject(i).getInt("Color");
                    searchResultData.compamy = jSONArray.getJSONObject(i).getString("CompanyInfo");
                    searchResultData.action = jSONArray.getJSONObject(i).getInt("ActionType");
                    arrayList.add(searchResultData);
                } catch (MyException unused3) {
                }
            } catch (Exception unused4) {
                this.searchResultEmptyTV.setText(R.string.service_fail);
                this.searchResultCount.setText("총 " + length + "건");
                if (length <= 0) {
                }
            }
        }
        this.searchResultCount.setText("총 " + length + "건");
        if (length <= 0) {
            this.searchResultLV.setVisibility(0);
            this.searchResultEmptyTV.setVisibility(8);
            this.searchResultListAdapter = new SearchResultListAdapter(this.baseActivity, 0, arrayList);
            this.searchResultLV.setAdapter((ListAdapter) this.searchResultListAdapter);
            return;
        }
        this.searchResultLV.setVisibility(8);
        this.searchResultEmptyTV.setVisibility(0);
    }

    @Override // android.view.View.OnClickListener
    public void onClick(View view) {
        String hypenPhoneNumber = Utils.getHypenPhoneNumber(Global.Incoming_Call_Number);
        switch (view.getId()) {
            case R.id.facebookBT /* 2131296361 */:
                this.baseActivity.gotoWebSearchFragment("https://m.facebook.com/search?q=" + hypenPhoneNumber);
                break;
            case R.id.googleBT /* 2131296369 */:
                this.baseActivity.gotoWebSearchFragment("https://www.google.com/search?q=" + hypenPhoneNumber);
                break;
            case R.id.lineBT /* 2131296394 */:
                try {
                    Intent launchIntentForPackage = getContext().getPackageManager().getLaunchIntentForPackage("jp.naver.line.android");
                    launchIntentForPackage.addFlags(67108864);
                    startActivity(launchIntentForPackage);
                    break;
                } catch (Exception unused) {
                    Toast.makeText(getContext(), "나인앱이 없습니다.", 0).show();
                    return;
                }
            case R.id.makeCallBT /* 2131296404 */:
                Intent intent = new Intent("android.intent.action.CALL");
                intent.setData(Uri.parse("tel:" + hypenPhoneNumber));
                startActivity(intent);
                break;
            case R.id.sendSMSBT /* 2131296464 */:
                Intent intent2 = new Intent("android.intent.action.VIEW");
                intent2.setType("vnd.android-dir/mms-sms");
                intent2.putExtra("address", hypenPhoneNumber);
                intent2.putExtra("sms_body", BuildConfig.FLAVOR);
                try {
                    startActivity(intent2);
                    break;
                } catch (ActivityNotFoundException unused2) {
                    Toast.makeText(this.baseActivity, "SMS failed!", 0).show();
                    return;
                }
            case R.id.talkBT /* 2131296501 */:
                try {
                    Intent launchIntentForPackage2 = getContext().getPackageManager().getLaunchIntentForPackage("com.kakao.talk");
                    launchIntentForPackage2.addFlags(67108864);
                    startActivity(launchIntentForPackage2);
                    break;
                } catch (Exception unused3) {
                    Toast.makeText(getContext(), "카카오톡앱이 없습니다.", 0).show();
                    return;
                }
        }
    }

    /* renamed from: com.developer.faker.Fragment.SearchFragment$1 */
    class AnonymousClass1 extends Handler {
        AnonymousClass1() {
        }

        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            SearchFragment.this.dismissProgress();
            if (message.what == 0) {
                try {
                    SearchFragment.this.refreshUI(new JSONObject(message.obj.toString()));
                    return;
                } catch (Exception e) {
                    e.printStackTrace();
                    return;
                }
            }
            UtilAuth utilAuth = UtilAuth.getInstance(SearchFragment.this.getContext());
            utilAuth.UserToken = null;
            utilAuth.saveAuthInfo();
            Intent intent = new Intent(SearchFragment.this.baseActivity.getBaseContext(), (Class<?>) LoginActivity.class);
            intent.addFlags(67108864);
            intent.addFlags(1048576);
            SearchFragment.this.startActivity(intent);
            SearchFragment.this.baseActivity.overridePendingTransition(R.anim.slidein, R.anim.slideout);
            SearchFragment.this.baseActivity.finish();
        }
    }

    private void getSearchResultFromServer(String str) {
        if (UtilContact.getInstance(getContext()).QueryPhoneNumber(str, Boolean.valueOf(UtilSetting.getInstance(getContext()).TODAY_SHOW).booleanValue(), Global.SearchHistory ? 2 : 0, 0, this.QueryPhoneNumberResultHandler)) {
            showProgress(getResources().getString(R.string.wait));
        }
    }
}
