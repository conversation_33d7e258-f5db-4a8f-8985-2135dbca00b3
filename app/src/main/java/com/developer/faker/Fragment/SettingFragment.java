package com.developer.faker.Fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.Switch;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilSetting;

/* loaded from: classes.dex */
public class SettingFragment extends BaseFragment {
    private Button m_btnCancel;
    private Button m_btnConfirm;
    private RadioButton m_rbBottom;
    private RadioButton m_rbMiddle;
    private RadioButton m_rbTop;
    private Switch m_swhShowPopupRemain;
    private Switch m_swhShowVisit;
    private UtilSetting utilSetting;
    private View view;

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            this.view = layoutInflater.inflate(R.layout.fragment_setting, viewGroup, false);
            this.utilSetting = UtilSetting.getInstance(getContext());
            initUI(this.view);
        }
        return this.view;
    }

    @Override // android.support.v4.app.Fragment
    public void onPause() {
        super.onPause();
        this.utilSetting.TODAY_SHOW = this.m_swhShowVisit.isChecked();
        this.utilSetting.POPUP_REMAIN = this.m_swhShowPopupRemain.isChecked();
        if (this.m_rbTop.isChecked()) {
            this.utilSetting.POPUP_POSITION = 0;
        } else if (this.m_rbMiddle.isChecked()) {
            this.utilSetting.POPUP_POSITION = 1;
        } else {
            this.utilSetting.POPUP_POSITION = 2;
        }
        this.utilSetting.saveSetting();
    }

    private void LoadSettingInfo() {
        this.m_swhShowVisit.setChecked(this.utilSetting.TODAY_SHOW);
        this.m_swhShowPopupRemain.setChecked(this.utilSetting.POPUP_REMAIN);
        this.m_rbTop.setChecked(false);
        this.m_rbMiddle.setChecked(false);
        this.m_rbBottom.setChecked(false);
        if (this.utilSetting.POPUP_POSITION == 0) {
            this.m_rbTop.setChecked(true);
        } else if (this.utilSetting.POPUP_POSITION == 1) {
            this.m_rbMiddle.setChecked(true);
        } else {
            this.m_rbBottom.setChecked(true);
        }
    }

    private void initUI(View view) {
        this.m_swhShowVisit = (Switch) view.findViewById(R.id.swhShowTodayCall);
        this.m_swhShowPopupRemain = (Switch) view.findViewById(R.id.swhShowPopupRemain);
        this.m_rbTop = (RadioButton) view.findViewById(R.id.chkTop);
        this.m_rbMiddle = (RadioButton) view.findViewById(R.id.chkMiddle);
        this.m_rbBottom = (RadioButton) view.findViewById(R.id.chkBottom);
        this.m_rbTop.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.SettingFragment.1
            AnonymousClass1() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                SettingFragment.this.m_rbMiddle.setChecked(false);
                SettingFragment.this.m_rbBottom.setChecked(false);
            }
        });
        this.m_rbMiddle.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.SettingFragment.2
            AnonymousClass2() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                SettingFragment.this.m_rbTop.setChecked(false);
                SettingFragment.this.m_rbBottom.setChecked(false);
            }
        });
        this.m_rbBottom.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.SettingFragment.3
            AnonymousClass3() {
            }

            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                SettingFragment.this.m_rbTop.setChecked(false);
                SettingFragment.this.m_rbMiddle.setChecked(false);
            }
        });
        LoadSettingInfo();
    }

    /* renamed from: com.developer.faker.Fragment.SettingFragment$1 */
    class AnonymousClass1 implements View.OnClickListener {
        AnonymousClass1() {
        }

        @Override // android.view.View.OnClickListener
        public void onClick(View view2) {
            SettingFragment.this.m_rbMiddle.setChecked(false);
            SettingFragment.this.m_rbBottom.setChecked(false);
        }
    }

    /* renamed from: com.developer.faker.Fragment.SettingFragment$2 */
    class AnonymousClass2 implements View.OnClickListener {
        AnonymousClass2() {
        }

        @Override // android.view.View.OnClickListener
        public void onClick(View view2) {
            SettingFragment.this.m_rbTop.setChecked(false);
            SettingFragment.this.m_rbBottom.setChecked(false);
        }
    }

    /* renamed from: com.developer.faker.Fragment.SettingFragment$3 */
    class AnonymousClass3 implements View.OnClickListener {
        AnonymousClass3() {
        }

        @Override // android.view.View.OnClickListener
        public void onClick(View view2) {
            SettingFragment.this.m_rbTop.setChecked(false);
            SettingFragment.this.m_rbMiddle.setChecked(false);
        }
    }
}
