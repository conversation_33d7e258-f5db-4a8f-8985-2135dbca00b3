package com.developer.faker.Fragment;

import android.app.Dialog;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import com.developer.faker.R;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilBlock;
import com.developer.faker.Utils.Utils;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.async.Callback;
import com.mashape.unirest.http.exceptions.UnirestException;
import java.util.HashMap;
import java.util.Iterator;

/* loaded from: classes.dex */
public class BlockFragmentSetting extends BaseFragment {
    View view = null;
    Switch swhBlockUnknown = null;
    Switch swhBlockTodayCall = null;
    Switch swhBlockSpecNumber = null;
    Switch swhBlockPref = null;
    Switch swhBlockAll = null;
    Switch swhBlockCallExplosion = null;
    private UtilBlock utilBlock = null;

    @Override // com.developer.faker.Fragment.BaseFragment, android.support.v4.app.Fragment
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.utilBlock = UtilBlock.getInstance(getContext());
    }

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        View view = this.view;
        if (view != null) {
            return view;
        }
        this.view = layoutInflater.inflate(R.layout.fragment_block_setting, viewGroup, false);
        InitUI();
        return this.view;
    }

    private void InitUI() {
        this.swhBlockUnknown = (Switch) this.view.findViewById(R.id.swhBlockUnknownNumber);
        this.swhBlockUnknown.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockUnknown(view);
            }
        });
        this.swhBlockTodayCall = (Switch) this.view.findViewById(R.id.swhBlockTodayCall);
        this.swhBlockTodayCall.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockTodayCall(view);
            }
        });
        this.swhBlockSpecNumber = (Switch) this.view.findViewById(R.id.swhBlockSpecNumber);
        this.swhBlockSpecNumber.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockSpecNumber(view);
            }
        });
        this.swhBlockPref = (Switch) this.view.findViewById(R.id.swhBlockPrefixNumber);
        this.swhBlockPref.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockPrefNumber(view);
            }
        });
        this.swhBlockAll = (Switch) this.view.findViewById(R.id.swhBlockAllNumber);
        this.swhBlockAll.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockAllNumber(view);
            }
        });
        this.swhBlockCallExplosion = (Switch) this.view.findViewById(R.id.swhCallExplosion);
        this.swhBlockCallExplosion.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.6
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhCallExplosion(view);
            }
        });
        this.view.findViewById(R.id.btnBlockTodayCall).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.7
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onBtnClickBlockTodayCall(view);
            }
        });
        this.view.findViewById(R.id.btnBlockSpecNumber).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.8
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onBtnClickBlockSpecNumber(view);
            }
        });
        this.view.findViewById(R.id.btnBlockPrefNumber).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.9
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onBtnClickBlockPrefNumber(view);
            }
        });
        this.view.findViewById(R.id.btnCallExplosion).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.10
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                BlockFragmentSetting.this.onBtnClickBlockCallExplosion(view);
            }
        });
        this.swhBlockUnknown.setChecked(this.utilBlock.IsBlockUnknown);
        this.swhBlockSpecNumber.setChecked(this.utilBlock.IsBlockSpecNumbers);
        this.swhBlockTodayCall.setChecked(this.utilBlock.IsBlockTodayCall);
        this.swhBlockPref.setChecked(this.utilBlock.IsBlockPrefNumbers);
        this.swhBlockAll.setChecked(this.utilBlock.IsBlockAll);
        this.swhBlockCallExplosion.setChecked(this.utilBlock.IsBlockCallExp);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onBtnClickBlockSpecNumber(View view) {
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blockspecnum);
        View.OnClickListener onClickListener = new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.11
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                boolean z;
                if (view2.getId() == R.id.btnOk) {
                    EditText editText = (EditText) dialog.findViewById(R.id.txtPhoneNumber);
                    TextView textView = (TextView) dialog.findViewById(R.id.txtError);
                    String correctPhoneNumber = Utils.getCorrectPhoneNumber(editText.getText().toString());
                    if (correctPhoneNumber.length() >= 8) {
                        Iterator<String> it = BlockFragmentSetting.this.utilBlock.lstSpecNumbers.iterator();
                        while (true) {
                            if (!it.hasNext()) {
                                z = false;
                                break;
                            } else if (it.next().compareTo(correctPhoneNumber) == 0) {
                                z = true;
                                break;
                            }
                        }
                        if (!z) {
                            BlockFragmentSetting.this.utilBlock.lstSpecNumbers.add(correctPhoneNumber);
                        } else {
                            textView.setText("입력하신 전화번호가 이미 존재 합니다");
                            textView.setVisibility(0);
                            return;
                        }
                    } else {
                        textView.setText("입력하신 전화번호가 옳바르지 않습니다");
                        textView.setVisibility(0);
                        return;
                    }
                }
                dialog.dismiss();
            }
        };
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onBtnClickBlockPrefNumber(View view) {
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blockprefnum);
        View.OnClickListener onClickListener = new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.12
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                boolean z;
                if (view2.getId() == R.id.btnOk) {
                    EditText editText = (EditText) dialog.findViewById(R.id.txtPhoneNumber);
                    TextView textView = (TextView) dialog.findViewById(R.id.txtError);
                    String correctPhoneNumber = Utils.getCorrectPhoneNumber(editText.getText().toString());
                    if (correctPhoneNumber.length() >= 4) {
                        Iterator<String> it = BlockFragmentSetting.this.utilBlock.lstPrefNumbers.iterator();
                        while (true) {
                            z = true;
                            if (!it.hasNext()) {
                                z = false;
                                break;
                            } else if (correctPhoneNumber.startsWith(it.next())) {
                                break;
                            }
                        }
                        if (!z) {
                            BlockFragmentSetting.this.utilBlock.lstPrefNumbers.add(correctPhoneNumber);
                        } else {
                            textView.setText("입력하신 전화번호가 이미 존재 합니다");
                            textView.setVisibility(0);
                            return;
                        }
                    } else {
                        textView.setText("입력하신 전화번호가 옳바르지 않습니다");
                        textView.setVisibility(0);
                        return;
                    }
                }
                dialog.dismiss();
            }
        };
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onBtnClickBlockTodayCall(View view) {
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blocktodaycall);
        final EditText editText = (EditText) dialog.findViewById(R.id.txtBlockLimit);
        editText.setText(Integer.toString(this.utilBlock.nBlockLimitTodayCall));
        View.OnClickListener onClickListener = new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.13
            @Override // android.view.View.OnClickListener
            public void onClick(View view){
                if (view2.getId() == R.id.btnOk) {
                    try {
                        int i = Integer.parseInt(editText.getText().toString());
                        if (i > 0) {
                            BlockFragmentSetting.this.utilBlock.nBlockLimitTodayCall = i;
                        } else {
                            throw new Exception("Invalid Input");
                        }
                    } catch (Exception unused) {
                        TextView textView = (TextView) dialog.findViewById(R.id.txtError);
                        textView.setText("입력하신 숫자가 옳바르지 않습니다");
                        textView.setVisibility(0);
                        return;
                    }
                }
                dialog.dismiss();
            }
        };
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onBtnClickBlockCallExplosion(View view) {
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blockcallexp);
        ((EditText) dialog.findViewById(R.id.txtCount)).setText(String.valueOf(this.utilBlock.callExplosionCount));
        View.OnClickListener onClickListener = new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.14
            @Override // android.view.View.OnClickListener
            public void onClick(View view){
                int i;
                if (view2.getId() == R.id.btnOk) {
                    EditText editText = (EditText) dialog.findViewById(R.id.txtCount);
                    TextView textView = (TextView) dialog.findViewById(R.id.txtError);
                    try {
                        i = Integer.parseInt(Utils.getCorrectPhoneNumber(editText.getText().toString()));
                    } catch (Exception unused) {
                        i = 1;
                    }
                    if (i > 1) {
                        BlockFragmentSetting.this.utilBlock.callExplosionCount = i;
                        BlockFragmentSetting.this.utilBlock.lstCallExplosion.clear();
                    } else {
                        textView.setText("입력하신 갯수가 옳바르지 않습니다");
                        textView.setVisibility(0);
                        return;
                    }
                }
                dialog.dismiss();
            }
        };
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void sendBlockAllConfigToServer() {
        try {
            final String str = Utils.getServerUrl() + Const.API_BLOCK_CONFIG;
            final HashMap map = new HashMap();
            map.put("apptype", "3");
            map.put("token", UtilAuth.getInstance(getContext()).UserToken);
            new Thread(new Runnable() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.15
                @Override // java.lang.Runnable
                public void run() {
                    Unirest.post(str).headers(map).field("flag", Boolean.valueOf(BlockFragmentSetting.this.utilBlock.IsBlockAll)).asJsonAsync(new Callback<JsonNode>() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.15.1
                        @Override // com.mashape.unirest.http.async.Callback
                        public void cancelled() {
                        }

                        @Override // com.mashape.unirest.http.async.Callback
                        public void completed(HttpResponse<JsonNode> httpResponse) {
                        }

                        @Override // com.mashape.unirest.http.async.Callback
                        public void failed(UnirestException unirestException) {
                        }
                    });
                }
            }).start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onClickSwhBlockAllNumber(View view) {
        if (this.utilBlock.IsBlockAll) {
            this.utilBlock.IsBlockAll = false;
            sendBlockAllConfigToServer();
            return;
        }
        this.swhBlockAll.setChecked(false);
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_block_all);
        View.OnClickListener onClickListener = new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.16
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                switch (view2.getId()) {
                    case R.id.btnCancel /* 2131296300 */:
                    case R.id.btnClose /* 2131296301 */:
                        BlockFragmentSetting.this.utilBlock.IsBlockAll = false;
                        break;
                    case R.id.btnOk /* 2131296306 */:
                        BlockFragmentSetting.this.swhBlockAll.setChecked(true);
                        BlockFragmentSetting.this.utilBlock.IsBlockAll = true;
                        BlockFragmentSetting.this.sendBlockAllConfigToServer();
                        break;
                }
                dialog.dismiss();
            }
        };
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onClickSwhBlockUnknown(View view) {
        if (this.utilBlock.IsBlockUnknown) {
            this.utilBlock.IsBlockUnknown = false;
            return;
        }
        this.swhBlockUnknown.setChecked(false);
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blockunknown);
        View.OnClickListener onClickListener = new View.OnClickListener() { // from class: com.developer.faker.Fragment.BlockFragmentSetting.17
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                switch (view2.getId()) {
                    case R.id.btnCancel /* 2131296300 */:
                    case R.id.btnClose /* 2131296301 */:
                        BlockFragmentSetting.this.utilBlock.IsBlockUnknown = false;
                        break;
                    case R.id.btnOk /* 2131296306 */:
                        BlockFragmentSetting.this.swhBlockUnknown.setChecked(true);
                        BlockFragmentSetting.this.utilBlock.IsBlockUnknown = true;
                        break;
                }
                dialog.dismiss();
            }
        };
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);
        setDlgWidthFull(dialog);
        dialog.show();
    }

    private void setDlgWidthFull(Dialog dialog) {
        DisplayMetrics displayMetrics = getContext().getResources().getDisplayMetrics();
        int i = displayMetrics.widthPixels;
        int i2 = displayMetrics.heightPixels;
        dialog.getWindow().getAttributes().width = i;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onClickSwhBlockTodayCall(View view) {
        this.utilBlock.IsBlockTodayCall = this.swhBlockTodayCall.isChecked();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onClickSwhBlockSpecNumber(View view) {
        this.utilBlock.IsBlockSpecNumbers = this.swhBlockSpecNumber.isChecked();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onClickSwhBlockPrefNumber(View view) {
        this.utilBlock.IsBlockPrefNumbers = this.swhBlockPref.isChecked();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onClickSwhCallExplosion(View view) {
        this.utilBlock.IsBlockCallExp = this.swhBlockCallExplosion.isChecked();
        this.utilBlock.lstCallExplosion.clear();
    }
}