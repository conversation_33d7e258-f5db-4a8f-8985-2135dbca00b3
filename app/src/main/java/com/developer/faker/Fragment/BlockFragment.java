package com.developer.faker.Fragment;

import android.os.Bundle;
import android.support.design.widget.TabLayout;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentTransaction;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilBlock;

/* loaded from: classes.dex */
public class BlockFragment extends BaseFragment {
    View view;
    private BlockFragmentSetting blockFSetting = null;
    private BlockFragmentNumbers blockFNumbers = null;
    private BlockFragmentHistory blockFHistory = null;

    @Override // android.support.v4.app.Fragment
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            this.view = layoutInflater.inflate(R.layout.fragment_block, viewGroup, false);
            initUI();
        }
        return this.view;
    }

    @Override // com.developer.faker.Fragment.BaseFragment, android.support.v4.app.Fragment
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        UtilBlock.getInstance(getContext()).LoadSetting();
        this.blockFSetting = new BlockFragmentSetting();
        this.blockFNumbers = new BlockFragmentNumbers();
        this.blockFHistory = new BlockFragmentHistory();
    }

    @Override // android.support.v4.app.Fragment
    public void onPause() {
        super.onPause();
        UtilBlock.getInstance(getContext()).SaveSetting();
    }

    @Override // android.support.v4.app.Fragment
    public void onStop() {
        super.onStop();
    }

    /* renamed from: com.developer.faker.Fragment.BlockFragment$1 */
    class AnonymousClass1 implements TabLayout.OnTabSelectedListener {
        @Override // android.support.design.widget.TabLayout.BaseOnTabSelectedListener
        public void onTabReselected(TabLayout.Tab tab) {
        }

        @Override // android.support.design.widget.TabLayout.BaseOnTabSelectedListener
        public void onTabUnselected(TabLayout.Tab tab) {
        }

        AnonymousClass1() {
        }

        @Override // android.support.design.widget.TabLayout.BaseOnTabSelectedListener
        public void onTabSelected(TabLayout.Tab tab) {
            int position = tab.getPosition();
            if (position == 0) {
                BlockFragment.this.gotoFrag_BlockSetting();
            } else if (position == 1) {
                BlockFragment.this.gotoFrag_BlockNumbers();
            } else {
                if (position != 2) {
                    return;
                }
                BlockFragment.this.gotoFrag_BlockHistory();
            }
        }
    }

    private void initUI() {
        ((TabLayout) this.view.findViewById(R.id.tabBlockCtrl)).addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() { // from class: com.developer.faker.Fragment.BlockFragment.1
            @Override // android.support.design.widget.TabLayout.BaseOnTabSelectedListener
            public void onTabReselected(TabLayout.Tab tab) {
            }

            @Override // android.support.design.widget.TabLayout.BaseOnTabSelectedListener
            public void onTabUnselected(TabLayout.Tab tab) {
            }

            AnonymousClass1() {
            }

            @Override // android.support.design.widget.TabLayout.BaseOnTabSelectedListener
            public void onTabSelected(TabLayout.Tab tab) {
                int position = tab.getPosition();
                if (position == 0) {
                    BlockFragment.this.gotoFrag_BlockSetting();
                } else if (position == 1) {
                    BlockFragment.this.gotoFrag_BlockNumbers();
                } else {
                    if (position != 2) {
                        return;
                    }
                    BlockFragment.this.gotoFrag_BlockHistory();
                }
            }
        });
        gotoFrag_BlockSetting();
    }

    public void gotoFrag_BlockSetting() {
        gotoFragment(this.blockFSetting);
    }

    public void gotoFrag_BlockNumbers() {
        gotoFragment(this.blockFNumbers);
    }

    public void gotoFrag_BlockHistory() {
        gotoFragment(this.blockFHistory);
    }

    private void gotoFragment(Fragment fragment) {
        FragmentTransaction fragmentTransactionBeginTransaction = getFragmentManager().beginTransaction();
        fragmentTransactionBeginTransaction.replace(R.id.block_content, fragment);
        fragmentTransactionBeginTransaction.commit();
    }
}
