package com.developer.faker.Utils;

/* loaded from: classes.dex */
public class Const {
    public static final String API_ANNOUNCEMENTS = "GetAnnouncementList?$format=json";
    public static final String API_BLOCK_CONFIG = "ReportBlockAllFlag?$format=json";
    public static final String API_CALL_RESULT = "ReportPhoneCallResult?$format=json";
    public static final String API_CHECK_NOTICE = "CheckNewNotice?$format=json";
    public static final String API_CHECK_UPDATE = "CheckApkUpdate";
    public static final String API_GETQUERYLIMIT_COUNT = "GetQueryLimitCount?$format=json";
    public static final String API_QUERYPHONENUMBER = "QueryPhoneNumber?data=";
    public static final String API_UPDATEPHONENUMBER = "UpdatePhoneNumber?$format=json";
    public static final String API_UPDATEPHONENUMBER_UNKNOWN = "UpdatePhoneNumber_Unknown?$format=json";
    public static final String API_USER_LOGIN = "Authenticate?$format=json";
    public static final String AUTH_KEY = "#$%f5ekkKKtriERT((89678$8uKL%9";
    public static final String LICENSEEND_DATE = "LicenseEndDate";
    public static final boolean LOCALTEST = false;
    public static final String MYPREFS = "sp";
    public static final String MYPREFS_Block_CallExplosion = "MYPREFS_Block_CallExplosion";
    public static final String MYPREFS_Block_CallExplosionCount = "MYPREFS_Block_CallExplosionCount";
    public static final String MYPREFS_Block_History = "MYPREFS_Block_History";
    public static final String MYPREFS_Block_IsBlockAll = "MYPREFS_Block_IsBlockAll";
    public static final String MYPREFS_Block_IsBlockPrefix = "MYPREFS_Block_IsBlockPrefix";
    public static final String MYPREFS_Block_IsBlockSpecNumber = "MYPREFS_Block_IsBlockSpecNumber";
    public static final String MYPREFS_Block_IsBlockTodayCall = "MYPREFS_Block_IsBlockTodayCall";
    public static final String MYPREFS_Block_IsBlockUnknown = "MYPREFS_Block_IsBlockNoContact";
    public static final String MYPREFS_Block_LimitTodayCall = "MYPREFS_Block_LimitTodayCall";
    public static final String MYPREFS_Block_PrefNumbers = "MYPREFS_Block_PrefNumbers";
    public static final String MYPREFS_Block_SpecNumbers = "MYPREFS_Block_Numbers";
    public static final String MYPREFS_CheckNewNoticeTick = "MYPREFS_CheckNewNotickTick";
    public static final String MYPREFS_IsNewNotice = "MYPREFS_IsNewNotice";
    public static final String MYPREFS_Setting_PopupPos = "MYPREFS_Setting_PopupPos";
    public static final String MYPREFS_Setting_PopupRemain = "MYPREFS_Setting_PopupRemain";
    public static final String MYPREFS_Setting_ShowTodayCall = "MYPREFS_Setting_ShowTodayCall";
    public static final String REMAIN_MINUTES = "remainminutes";
    public static final String UPDATECONTACK_TICK = "updatecontact_tick";
    public static final String USER_COMPANY = "usercompany";
    public static final String USER_ID = "user_id";
    public static final String USER_PWD = "user_pwd";
    public static final String USER_TOKEN = "user_token";
    public static final int WHAT_ONDOWNLOADFINISH = 100;
    public static final int WHAT_UPDATEURL_FAIL = 1;
    public static final int WHAT_UPDATEURL_SUCCESS = 0;
    public static final Integer FRAGMENT_STATE_MAIN = 0;
    public static final Integer FRAGMENT_STATE_SEARCH = 1;
    public static final Integer FRAGMENT_STATE_WEB = 2;
    public static final Integer FRAGMENT_STATE_NOTICE = 3;
    public static final Integer FRAGMENT_STATE_BLOCK = 4;
    public static final Integer FRAGMENT_STATE_SETTING = 7;
    public static final Integer CALL_TYPE_PHONE = 0;
    public static final Integer CALL_TYPE_SMS = 1;
}
