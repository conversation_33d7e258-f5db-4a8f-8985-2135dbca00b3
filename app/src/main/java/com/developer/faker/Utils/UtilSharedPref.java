package com.developer.faker.Utils;

import android.content.Context;
import android.content.SharedPreferences;
import com.developer.faker.BuildConfig;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class UtilSharedPref {
    public static SharedPreferences GetDefault(Context context) {
        return context.getSharedPreferences(Const.MYPREFS, 0);

    public static void setBoolean(Context context, String str, boolean z) {
        SharedPreferences.Editor editorEdit = GetDefault(context).edit();
        editorEdit.putBoolean(str, z);
        editorEdit.commit();

    public static boolean getBoolean(Context context, String str, boolean z) {
        return GetDefault(context).getBoolean(str, z);

    public static void setInt(Context context, String str, int i) {
        SharedPreferences.Editor editorEdit = GetDefault(context).edit();
        editorEdit.putInt(str, i);
        editorEdit.commit();

    public static int getInt(Context context, String str, int i) {
        return GetDefault(context).getInt(str, i);

    public static void setLong(Context context, String str, long j) {
        SharedPreferences.Editor editorEdit = GetDefault(context).edit();
        editorEdit.putLong(str, j);
        editorEdit.commit();

    public static long getLong(Context context, String str, long j) {
        return GetDefault(context).getLong(str, j);

    public static void setString(Context context, String str, String str2) {
        SharedPreferences.Editor editorEdit = GetDefault(context).edit();
        editorEdit.putString(str, str2);
        editorEdit.commit();

    public static String getString(Context context, String str, String str2) {
        return GetDefault(context).getString(str, str2);

    public static ArrayList<String> getStringArray(Context context, String str) {
        ArrayList<String> arrayList = new ArrayList<>();
        SharedPreferences sharedPreferencesGetDefault = GetDefault(context);
        Integer numValueOf = Integer.valueOf(sharedPreferencesGetDefault.getInt(str + "_count", 0));
        for (int i = 0; i < numValueOf.intValue(); i++) {
            arrayList.add(sharedPreferencesGetDefault.getString(str + String.valueOf(i), BuildConfig.FLAVOR));
        return arrayList;

    public static void putStringArray(Context context, String str, ArrayList<String> arrayList) {
        if (arrayList == null) {
            return;
        int size = arrayList.size();
        SharedPreferences.Editor editorEdit = GetDefault(context).edit();
        editorEdit.putInt(str + "_count", size);
        for (int i = 0; i < size; i++) {
            editorEdit.putString(str + String.valueOf(i), arrayList.get(i));
        editorEdit.commit();

    public static ArrayList<String> getStringArray(SharedPreferences sharedPreferences, String str) {
        ArrayList<String> arrayList = new ArrayList<>();
        Integer numValueOf = Integer.valueOf(sharedPreferences.getInt(str + "_count", 0));
        for (int i = 0; i < numValueOf.intValue(); i++) {
            arrayList.add(sharedPreferences.getString(str + String.valueOf(i), BuildConfig.FLAVOR));
        return arrayList;

    public static void putStringArray(SharedPreferences.Editor editor, String str, ArrayList<String> arrayList) {
        if (arrayList == null) {
            return;
        int size = arrayList.size();
        editor.putInt(str + "_count", size);
        for (int i = 0; i < size; i++) {
            editor.putString(str + String.valueOf(i), arrayList.get(i));
