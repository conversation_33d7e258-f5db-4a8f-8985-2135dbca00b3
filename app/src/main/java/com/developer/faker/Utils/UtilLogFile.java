package com.developer.faker.Utils;

import android.content.Context;
import android.os.Environment;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.text.SimpleDateFormat;
import java.util.Date;

/* loaded from: classes.dex */
public class UtilLogFile {
    private static UtilLogFile _inst;
    private Context _ctx = null;

    public static UtilLogFile getInstance(Context context) {
        if (_inst == null) {
            _inst = new UtilLogFile();
            _inst._ctx = context;
        return _inst;

    private String getLogFolder() {
        String str = this._ctx.getFilesDir() + "/logs";
        File file = new File(str);
        if (!file.exists()) {
            file.mkdir();
        return str;

    private String getLogFileName() {
        return getLogFolder() + "/log_" + new SimpleDateFormat("yyyy-MM-dd").format(new Date(System.currentTimeMillis())) + ".txt";

    public void writeLog(String str) throws IOException {
        try {
            File file = new File(getLogFileName());
            if (!file.exists()) {
                file.createNewFile();
            synchronized (this) {
                FileOutputStream fileOutputStream = new FileOutputStream(file, true);
                OutputStreamWriter outputStreamWriter = new OutputStreamWriter(fileOutputStream);
                outputStreamWriter.write(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(System.currentTimeMillis())) + "\n");
                outputStreamWriter.write(str);
                outputStreamWriter.write("\n");
                outputStreamWriter.flush();
                outputStreamWriter.close();
                fileOutputStream.close();
        } catch (Exception e) {
            e.printStackTrace();

    private boolean isExternalStorageWritable() {
        return "mounted".equals(Environment.getExternalStorageState());

    private boolean isExternalStorageReadable() {
        String externalStorageState = Environment.getExternalStorageState();
        return "mounted".equals(externalStorageState) || "mounted_ro".equals(externalStorageState);
