package com.developer.faker.Utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.ContentProviderOperation;
import android.content.Context;
import android.content.OperationApplicationException;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PointF;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.provider.CallLog;
import android.provider.ContactsContract;
import android.support.annotation.NonNull;
import android.support.v7.widget.helper.ItemTouchHelper;
import android.telecom.TelecomManager;
import android.telephony.TelephonyManager;
import android.widget.Toast;
// import com.android.internal.telephony.ITelephony;
import com.developer.faker.Data.NoticeInfo;
import com.developer.faker.Data.PhoneInfo;
import com.developer.faker.Data.RecentIncomeInfo;
import com.developer.faker.Model.CallLogListener;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.async.Callback;
import com.mashape.unirest.http.exceptions.UnirestException;
import java.io.IOException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class Utils {
    private static Utils mInstance;
    private int LIMIT_COUNT = 60;
    Handler GetNoticeDataFromServerHandler = new Handler() { // from class: com.developer.faker.Utils.Utils.2
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            HandlerClass handlerClass = (HandlerClass) message.obj;
            if (handlerClass.handler != null) {
                handlerClass.handler.sendEmptyMessage(0);
            }
            if (message.what == 0) {
                try {
                    JSONArray jSONArray = new JSONObject(RC4.getInstance().decrypt(new JSONObject(handlerClass.msg).get("data").toString(), Const.AUTH_KEY)).getJSONArray("Data");
                    int length = jSONArray.length();
                    Global.NoticeList.clear();
                    for (int i = 0; i < length; i++) {
                        NoticeInfo noticeInfo = new NoticeInfo();
                        JSONObject jSONObject = jSONArray.getJSONObject(i);
                        noticeInfo.id = jSONObject.getInt("ID");
                        noticeInfo.subject = jSONObject.getString("Subject");
                        noticeInfo.content = jSONObject.getString("Text");
                        noticeInfo.date = jSONObject.getString("time");
                        Global.NoticeList.add(noticeInfo);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    };
    Handler GetQueryLimitCountHandler = new Handler() { // from class: com.developer.faker.Utils.Utils.4
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            HandlerClass handlerClass = (HandlerClass) message.obj;
            if (handlerClass.handler != null) {
                handlerClass.handler.sendEmptyMessage(0);
            }
            if (message.what == 0) {
                try {
                    Global.RemainQueryCount = Integer.parseInt(new JSONObject(handlerClass.msg).get("data").toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    };

    public static String getServerUrl() {
        return "http://paker112.com/api/Zn/";
    }

    class HandlerClass {
        public Context context;
        public Handler handler;
        public String msg;

        HandlerClass() {
        }
    }

    public static Utils getInstance() {
        if (mInstance == null) {
            mInstance = new Utils();
        }
        return mInstance;
    }

    public static boolean isNullOrEmptyString(String str) {
        return str == null || str.isEmpty();
    }

    public ProgressDialog openNewDialog(Context context, String str, boolean z, boolean z2) {
        ProgressDialog progressDialog;
        try {
            progressDialog = new ProgressDialog(context);
        } catch (Exception e) {
            e = e;
            progressDialog = null;
        }
        try {
            progressDialog.setMessage(str);
            progressDialog.setIndeterminate(true);
            progressDialog.setProgressStyle(0);
            progressDialog.setCancelable(z);
            progressDialog.setCanceledOnTouchOutside(z2);
            progressDialog.show();
        } catch (Exception e2) {
            e2.printStackTrace();
            return progressDialog;
        }
        return progressDialog;
    }

    public static String getCorrectPhoneNumber(String str) {
        try {
            String strReplace = str.replace("-", "").replace(" ", "").replace("(", "").replace(")", "").replace("+", "");
            if (!strReplace.startsWith("8210") || strReplace.length() != 12) {
                return strReplace;
            }
            return "010" + strReplace.substring(4);
        } catch (Exception e) {
            e.printStackTrace();
            return str;
        }
    }

    public RecentIncomeInfo getLatestCall(Context context) throws NumberFormatException {
        Cursor cursorQuery;
        try {
            String[] strArr = {"type", "number", "date", "duration", "name"};
            String str = ("" + "type<>2") + " AND number<>''";
            Uri uri = Uri.parse("content://call_log/calls");
            if (Build.VERSION.SDK_INT >= 30) {
                Bundle bundle = new Bundle();
                bundle.putString("android:query-arg-sql-sort-order", "date DESC ");
                bundle.putString("android:query-arg-sql-selection", str);
                bundle.putInt("android:query-arg-limit", 1);
                bundle.putInt("android:query-arg-offset", 0);
                cursorQuery = context.getContentResolver().query(uri, strArr, bundle, null);
            } else {
                cursorQuery = context.getContentResolver().query(uri, strArr, str, null, "date DESC LIMIT 1");
            }
            int columnIndex = cursorQuery.getColumnIndex("type");
            int columnIndex2 = cursorQuery.getColumnIndex("number");
            int columnIndex3 = cursorQuery.getColumnIndex("date");
            cursorQuery.getColumnIndex("duration");
            int columnIndex4 = cursorQuery.getColumnIndex("name");
            if (cursorQuery.moveToFirst()) {
                String string = cursorQuery.getString(columnIndex4);
                String string2 = cursorQuery.getString(columnIndex2);
                String string3 = cursorQuery.getString(columnIndex3);
                int i = Integer.parseInt(cursorQuery.getString(columnIndex));
                String str2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(string3).longValue()));
                RecentIncomeInfo recentIncomeInfo = new RecentIncomeInfo();
                recentIncomeInfo.contactName = string;
                recentIncomeInfo.phoneNumber = string2;
                recentIncomeInfo.callDate = str2;
                recentIncomeInfo.callType = Const.CALL_TYPE_PHONE.intValue();
                recentIncomeInfo.callTypeDetail = i;
                cursorQuery.close();
                return recentIncomeInfo;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public ArrayList<RecentIncomeInfo> getCallLog(Context context) throws NumberFormatException {
        Cursor cursorQuery;
        boolean z;
        try {
            ArrayList<RecentIncomeInfo> arrayList = new ArrayList<>();
            String[] strArr = {"type", "number", "date", "duration", "name"};
            String str = ("" + "type<>2") + " AND number<>''";
            Uri uri = Uri.parse("content://call_log/calls");
            if (Build.VERSION.SDK_INT >= 30) {
                Bundle bundle = new Bundle();
                bundle.putString("android:query-arg-sql-sort-order", "date DESC ");
                bundle.putString("android:query-arg-sql-selection", str);
                bundle.putInt("android:query-arg-limit", this.LIMIT_COUNT);
                bundle.putInt("android:query-arg-offset", 0);
                cursorQuery = context.getContentResolver().query(uri, strArr, bundle, null);
            } else {
                cursorQuery = context.getContentResolver().query(uri, strArr, str, null, "date DESC LIMIT " + this.LIMIT_COUNT);
            }
            int columnIndex = cursorQuery.getColumnIndex("type");
            int columnIndex2 = cursorQuery.getColumnIndex("number");
            int columnIndex3 = cursorQuery.getColumnIndex("date");
            cursorQuery.getColumnIndex("duration");
            int columnIndex4 = cursorQuery.getColumnIndex("name");
            if (cursorQuery.moveToFirst()) {
                do {
                    String string = cursorQuery.getString(columnIndex4);
                    String string2 = cursorQuery.getString(columnIndex2);
                    String string3 = cursorQuery.getString(columnIndex3);
                    int i = Integer.parseInt(cursorQuery.getString(columnIndex));
                    Iterator<RecentIncomeInfo> it = arrayList.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            z = false;
                            break;
                        }
                        if (string2.equals(it.next().phoneNumber)) {
                            z = true;
                            break;
                        }
                    }
                    if (!z) {
                        String str2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(string3).longValue()));
                        RecentIncomeInfo recentIncomeInfo = new RecentIncomeInfo();
                        recentIncomeInfo.contactName = string;
                        recentIncomeInfo.phoneNumber = string2;
                        recentIncomeInfo.callDate = str2;
                        recentIncomeInfo.callType = Const.CALL_TYPE_PHONE.intValue();
                        recentIncomeInfo.callTypeDetail = i;
                        arrayList.add(recentIncomeInfo);
                    }
                } while (cursorQuery.moveToNext());
            }
            cursorQuery.close();
            return arrayList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public ArrayList<RecentIncomeInfo> getAllSms(Context context) {
        Cursor cursorQuery = null;
        ArrayList<RecentIncomeInfo> arrayList = new ArrayList<>();
        try {
            cursorQuery = context.getContentResolver().query(Uri.parse("content://sms/inbox/"), null, null, null, "date desc LIMIT " + this.LIMIT_COUNT);
            if (cursorQuery != null) {
                cursorQuery.getCount();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (cursorQuery != null && cursorQuery.moveToFirst()) {
            do {
                RecentIncomeInfo recentIncomeInfo = new RecentIncomeInfo();
                String string = cursorQuery.getString(cursorQuery.getColumnIndexOrThrow("address"));
                if (string != null && !string.toLowerCase().contains("#cmas")) {
                    boolean z = false;
                    Iterator<RecentIncomeInfo> it = arrayList.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            break;
                        }
                        if (string.equals(it.next().phoneNumber)) {
                            z = true;
                            break;
                        }
                    }
                    if (!z) {
                        String strGetContactName = GetContactName(context, string);
                        String str = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(cursorQuery.getString(cursorQuery.getColumnIndexOrThrow("date"))).longValue()));
                        recentIncomeInfo.contactName = strGetContactName;
                        recentIncomeInfo.phoneNumber = string;
                        recentIncomeInfo.callDate = str;
                        recentIncomeInfo.callType = Const.CALL_TYPE_SMS.intValue();
                        arrayList.add(recentIncomeInfo);
                        if (arrayList.size() >= this.LIMIT_COUNT) {
                            break;
                        }
                    }
                }
                return arrayList;
            } while (cursorQuery.moveToNext());
        }
        cursorQuery.close();
        return arrayList;
    }

    public void getCallLog(final Activity activity, final CallLogListener callLogListener) {
        activity.runOnUiThread(new Runnable() { // from class: com.developer.faker.Utils.Utils.1
            @Override // java.lang.Runnable
            public void run() {
                ArrayList arrayList = new ArrayList();
                try {
                    arrayList.addAll(Utils.this.getCallLog(activity));
                    arrayList.addAll(Utils.this.getAllSms(activity));
                    Collections.sort(arrayList);
                    ArrayList arrayList2 = new ArrayList();
                    for (int i = 0; i < arrayList.size() && i < Utils.this.LIMIT_COUNT / 2; i++) {
                        arrayList2.add(arrayList.get(i));
                    }
                    if (arrayList.isEmpty()) {
                        callLogListener.onResult(1, null);
                    } else {
                        callLogListener.onResult(0, arrayList2);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public void getNoticeDataFromServer(Context context, final Handler handler) {
        String str = getServerUrl() + Const.API_ANNOUNCEMENTS;
        HashMap map = new HashMap();
        map.put("apptype", "3");
        map.put("token", UtilAuth.getInstance(context).UserToken);
        Unirest.get(str).headers(map).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Utils.Utils.3
            @Override // com.mashape.unirest.http.async.Callback
            public void cancelled() {
            }

            @Override // com.mashape.unirest.http.async.Callback
            public void completed(HttpResponse<String> httpResponse) {
                Message message = new Message();
                message.what = 0;
                HandlerClass handlerClass = Utils.this.new HandlerClass();
                handlerClass.msg = httpResponse.getBody().toString();
                handlerClass.handler = handler;
                message.obj = handlerClass;
                Utils.this.GetNoticeDataFromServerHandler.sendMessage(message);
            }

            @Override // com.mashape.unirest.http.async.Callback
            public void failed(UnirestException unirestException) {
                Message message = new Message();
                message.what = 0;
                HandlerClass handlerClass = Utils.this.new HandlerClass();
                handlerClass.msg = unirestException.toString();
                handlerClass.handler = handler;
                message.obj = handlerClass;
                Utils.this.GetNoticeDataFromServerHandler.sendMessage(message);
            }
        });
    }

    public long getNewNoticeCheckTick(Context context) {
        return context.getSharedPreferences(Const.MYPREFS, 0).getLong(Const.MYPREFS_CheckNewNoticeTick, 0L);
    }

    public void setNewNoticCheckTick(Context context, long j) {
        SharedPreferences.Editor editorEdit = context.getSharedPreferences(Const.MYPREFS, 0).edit();
        editorEdit.putLong(Const.MYPREFS_CheckNewNoticeTick, j);
        editorEdit.commit();
    }

    public boolean getNewNoticeState(Context context) {
        return context.getSharedPreferences(Const.MYPREFS, 0).getBoolean(Const.MYPREFS_IsNewNotice, false);
    }

    public void setNewNoticeState(Context context, boolean z) {
        SharedPreferences.Editor editorEdit = context.getSharedPreferences(Const.MYPREFS, 0).edit();
        editorEdit.putBoolean(Const.MYPREFS_IsNewNotice, z);
        editorEdit.commit();
    }

    public void GetQueryLimitCount(Context context, final Handler handler) {
        final String str = getServerUrl() + Const.API_GETQUERYLIMIT_COUNT;
        final HashMap map = new HashMap();
        map.put("apptype", "3");
        map.put("token", UtilAuth.getInstance(context).UserToken);
        new Thread(new Runnable() { // from class: com.developer.faker.Utils.Utils.5
            @Override // java.lang.Runnable
            public void run() {
                Unirest.get(str).headers(map).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Utils.Utils.5.1
                    @Override // com.mashape.unirest.http.async.Callback
                    public void cancelled() {
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void completed(HttpResponse<String> httpResponse) {
                        Message message = new Message();
                        message.what = 0;
                        HandlerClass handlerClass = Utils.this.new HandlerClass();
                        handlerClass.handler = handler;
                        handlerClass.msg = httpResponse.getBody().toString();
                        message.obj = handlerClass;
                        Utils.this.GetQueryLimitCountHandler.sendMessage(message);
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void failed(UnirestException unirestException) {
                        Message message = new Message();
                        message.what = 1;
                        HandlerClass handlerClass = Utils.this.new HandlerClass();
                        handlerClass.handler = handler;
                        handlerClass.msg = unirestException.toString();
                        message.obj = handlerClass;
                        Utils.this.GetQueryLimitCountHandler.sendMessage(message);
                    }
                });
            }
        }).start();
    }

    public void ContactAdd(Context context, String str, String str2, String str3) throws RemoteException, OperationApplicationException {
        ArrayList<ContentProviderOperation> arrayList = new ArrayList<>();
        try {
            arrayList.add(ContentProviderOperation.newInsert(ContactsContract.RawContacts.CONTENT_URI).withValue("account_type", null).withValue("account_name", null).build());
            arrayList.add(ContentProviderOperation.newInsert(ContactsContract.Data.CONTENT_URI).withValueBackReference("raw_contact_id", 0).withValue("mimetype", "vnd.android.cursor.item/name").withValue("data1", str).build());
            arrayList.add(ContentProviderOperation.newInsert(ContactsContract.Data.CONTENT_URI).withValueBackReference("raw_contact_id", 0).withValue("mimetype", "vnd.android.cursor.item/phone_v2").withValue("data1", str2).withValue("data2", 2).build());
            arrayList.add(ContentProviderOperation.newInsert(ContactsContract.Data.CONTENT_URI).withValueBackReference("raw_contact_id", 0).withValue("mimetype", "vnd.android.cursor.item/email_v2").withValue("data1", str3).withValue("data2", 2).build());
            context.getContentResolver().applyBatch("com.android.contacts", arrayList);
            arrayList.clear();
        } catch (OperationApplicationException e) {
            e.printStackTrace();
        } catch (RemoteException e2) {
            e2.printStackTrace();
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:16:0x0043  */
    /* JADX WARN: Removed duplicated region for block: B:22:? A[RETURN, SYNTHETIC] */
    @android.annotation.SuppressLint({"MissingPermission"})
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void rejectCall(@android.support.annotation.NonNull android.content.Context r7) throws java.lang.NoSuchMethodException, java.lang.SecurityException {
        /*
            r6 = this;
            int r0 = android.os.Build.VERSION.SDK_INT
            r1 = 0
            r2 = 1
            r3 = 28
            if (r0 < r3) goto L19
            java.lang.String r0 = "telecom"
            java.lang.Object r0 = r7.getSystemService(r0)
            android.telecom.TelecomManager r0 = (android.telecom.TelecomManager) r0
            r0.endCall()     // Catch: java.lang.Exception -> L14
            goto L41
        L14:
            r0 = move-exception
            r0.printStackTrace()
            goto L40
        L19:
            java.lang.String r0 = "phone"
            java.lang.Object r0 = r7.getSystemService(r0)
            android.telephony.TelephonyManager r0 = (android.telephony.TelephonyManager) r0
            java.lang.Class r3 = r0.getClass()     // Catch: java.lang.Exception -> L3c
            java.lang.String r4 = "getITelephony"
            java.lang.Class[] r5 = new java.lang.Class[r1]     // Catch: java.lang.Exception -> L3c
            java.lang.reflect.Method r3 = r3.getDeclaredMethod(r4, r5)     // Catch: java.lang.Exception -> L3c
            r3.setAccessible(r2)     // Catch: java.lang.Exception -> L3c
            java.lang.Object[] r4 = new java.lang.Object[r1]     // Catch: java.lang.Exception -> L3c
            java.lang.Object r0 = r3.invoke(r0, r4)     // Catch: java.lang.Exception -> L3c
            com.android.internal.telephony.ITelephony r0 = (com.android.internal.telephony.ITelephony) r0     // Catch: java.lang.Exception -> L3c
            r0.endCall()     // Catch: java.lang.Exception -> L3c
            goto L41
        L3c:
            r0 = move-exception
            r0.printStackTrace()
        L40:
            r1 = 1
        L41:
            if (r1 == 0) goto L4c
            java.lang.String r0 = "Can not block calls in this Android version"
            android.widget.Toast r7 = android.widget.Toast.makeText(r7, r0, r2)
            r7.show()
        L4c:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.developer.faker.Utils.Utils.rejectCall(android.content.Context):void");
    }

    @SuppressLint({"MissingPermission"})
    public void acceptCall(@NonNull Context context) throws NoSuchMethodException, SecurityException {
        boolean z = false;
        try {
            if (Build.VERSION.SDK_INT >= 28) {
                ((TelecomManager) context.getSystemService("telecom")).acceptRingingCall();
            } else {
                TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService("phone");
                Method declaredMethod = telephonyManager.getClass().getDeclaredMethod("getITelephony", new Class[0]);
                declaredMethod.setAccessible(true);
                // ((ITelephony) declaredMethod.invoke(telephonyManager, new Object[0])).answerRingingCall();
                Object telephony = declaredMethod.invoke(telephonyManager, new Object[0]);
                Method answerCall = telephony.getClass().getMethod("answerRingingCall");
                answerCall.invoke(telephony);
            }
        } catch (Exception unused) {
            z = true;
        }
        if (z) {
            Toast.makeText(context, "Can not accept calls in this Android version", 1).show();
        }
    }

    public int RemoveCallLog(Context context, String str) throws IOException {
        try {
            return context.getContentResolver().delete(CallLog.Calls.CONTENT_URI, "number = ?", new String[]{str});
        } catch (Exception e) {
            UtilLogFile.getInstance(context).writeLog(e.toString());
            return 0;
        }
    }

    public String GetHourMinute(String str) throws ParseException {
        Date date;
        Calendar calendar = Calendar.getInstance();
        try {
            date = new SimpleDateFormat("yyyy.MM.dd.HH.mm.ss").parse(str);
        } catch (Exception e) {
            e.printStackTrace();
            date = null;
        }
        calendar.setTime(date);
        return new SimpleDateFormat("HH:mm").format(calendar.getTime());
    }

    public String GetAfterHourMinute(String str, int i) throws ParseException {
        Date date;
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd.HH.mm.ss");
        try {
            date = simpleDateFormat.parse(str);
        } catch (Exception e) {
            e.printStackTrace();
            date = null;
        }
        calendar.setTime(date);
        calendar.add(12, i);
        return simpleDateFormat.format(calendar.getTime());
    }

    public int GetDiffMinutes(String str, String str2) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd.HH.mm.ss");
        try {
            return (int) (((simpleDateFormat.parse(str2).getTime() - simpleDateFormat.parse(str).getTime()) / 1000) / 60);
        } catch (Exception unused) {
            return 0;
        }
    }

    @SuppressLint({"MissingPermission"})
    public static String getPhoneNumber(Context context) {
        return getCorrectPhoneNumber(((TelephonyManager) context.getSystemService("phone")).getLine1Number());
    }

    public static String getHypenPhoneNumber(String str) {
        if (str == null) {
            return "";
        }
        if (str.length() == 8) {
            return str.replaceFirst("^([0-9]{4})([0-9]{4})$", "$1-$2");
        }
        if (str.length() == 12) {
            return str.replaceFirst("(^[0-9]{4})([0-9]{4})([0-9]{4})$", "$1-$2-$3");
        }
        return str.replaceFirst("(^02|[0-9]{3})([0-9]{3,4})([0-9]{4})$", "$1-$2-$3");
    }

    public void GetContactFromPhoneNumber(final Context context, String str, final Handler handler) {
        getInstance();
        final String correctPhoneNumber = getCorrectPhoneNumber(str);
        new Thread(new Runnable() { // from class: com.developer.faker.Utils.Utils.6
            @Override // java.lang.Runnable
            public void run(){
                try {
                    ArrayList<PhoneInfo> contactsListRaw = UtilContact.getInstance(context).getContactsListRaw(false);
                    for (int i = 0; i < contactsListRaw.size(); i++) {
                        PhoneInfo phoneInfo = contactsListRaw.get(i);
                        Utils.getInstance();
                        String correctPhoneNumber2 = Utils.getCorrectPhoneNumber(phoneInfo.phoneNumber);
                        if (correctPhoneNumber2 != null && correctPhoneNumber2.equals(correctPhoneNumber)) {
                            Message message = new Message();
                            message.what = 10;
                            message.obj = phoneInfo.userName;
                            handler.sendMessage(message);
                        }
                    }
                } catch (Exception e) {
                    try {
                        UtilLogFile.getInstance(context).writeLog(e.toString());
                    } catch (IOException ioException) {
                        ioException.printStackTrace();
                    }
                }
                UtilContact.getInstance(context).QueryPhoneNumber(correctPhoneNumber, UtilSetting.getInstance(context).TODAY_SHOW, 1, UtilBlock.getInstance(context).IsBlockTodayCall ? UtilBlock.getInstance(context).nBlockLimitTodayCall : 0, handler);
            }
        }).start();
    }

    public void SendCallResult(final Context context, String str, final int i) {
        final String correctPhoneNumber = getCorrectPhoneNumber(str);
        new Thread(new Runnable() { // from class: com.developer.faker.Utils.Utils.7
            @Override // java.lang.Runnable
            public void run() {
                try {
                    String str2 = Utils.getServerUrl() + Const.API_CALL_RESULT;
                    HashMap map = new HashMap();
                    map.clear();
                    map.put("apptype", "3");
                    map.put("token", UtilAuth.getInstance(context).UserToken);
                    JSONObject jSONObject = new JSONObject();
                    jSONObject.put("phonenumber", correctPhoneNumber);
                    jSONObject.put("action", i);
                    Unirest.post(str2).headers((Map<String, String>) map).field("data", RC4.getInstance().encrypt(jSONObject.toString(), Const.AUTH_KEY)).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Utils.Utils.7.1
                        @Override // com.mashape.unirest.http.async.Callback
                        public void cancelled() {
                        }

                        @Override // com.mashape.unirest.http.async.Callback
                        public void completed(HttpResponse<String> httpResponse) {
                            Message message = new Message();
                            message.what = 1;
                            try {
                                message.obj = new JSONObject(httpResponse.getBody().toString());
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }

                        @Override // com.mashape.unirest.http.async.Callback
                        public void failed(UnirestException unirestException) {
                            unirestException.printStackTrace();
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    public Bitmap MakeWaterMark(Bitmap bitmap, String str, int i) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        Bitmap bitmapCreateBitmap = Bitmap.createBitmap(width, height, bitmap.getConfig());
        Canvas canvas = new Canvas(bitmapCreateBitmap);
        canvas.drawBitmap(bitmap, 0.0f, 0.0f, (Paint) null);
        Paint paint = new Paint();
        paint.setColor(-7829368);
        paint.setAlpha(i);
        paint.setTextSize(50);
        paint.setTextAlign(Paint.Align.CENTER);
        paint.setAntiAlias(true);
        paint.setUnderlineText(false);
        canvas.rotate(-15.0f);
        PointF pointF = new PointF(width / 2, height / 6);
        for (int i2 = 0; i2 < height; i2 += ItemTouchHelper.Callback.DEFAULT_SWIPE_ANIMATION_DURATION) {
            double d = 75.0f;
            double dCos = Math.cos(Math.toRadians(d));
            double d2 = ItemTouchHelper.Callback.DEFAULT_SWIPE_ANIMATION_DURATION;
            Double.isNaN(d2);
            double d3 = dCos * d2;
            double dSin = Math.sin(Math.toRadians(d));
            Double.isNaN(d2);
            double d4 = dSin * d2;
            canvas.drawText(str, pointF.x, pointF.y, paint);
            double d5 = pointF.x;
            Double.isNaN(d5);
            pointF.x = (float) (d5 - d3);
            double d6 = pointF.y;
            Double.isNaN(d6);
            pointF.y = (float) (d6 + d4);
        }
        canvas.rotate(15.0f);
        return bitmapCreateBitmap;
    }

    public String GetContactName(Context context, String str) {
        ArrayList<PhoneInfo> contactsListRaw = UtilContact.getInstance(context).getContactsListRaw(false);
        for (int i = 0; i < contactsListRaw.size(); i++) {
            PhoneInfo phoneInfo = contactsListRaw.get(i);
            if (phoneInfo.phoneNumber != null && phoneInfo.phoneNumber.equals(str)) {
                return phoneInfo.userName;
            }
        }
        return "";
    }
}