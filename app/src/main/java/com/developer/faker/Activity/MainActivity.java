package com.developer.faker.Activity;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.support.design.widget.NavigationView;
import android.support.v4.view.GravityCompat;
import android.support.v4.widget.DrawerLayout;
import android.support.v7.app.ActionBarDrawerToggle;
import android.support.v7.widget.Toolbar;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import com.developer.faker.Adapter.DrawerAdapter;
import com.developer.faker.BuildConfig;
import com.developer.faker.Data.DrawerData;
import com.developer.faker.R;
import com.developer.faker.Utils.BackPressHandler;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilLogFile;
import com.developer.faker.Utils.Utils;
import java.io.IOException;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class MainActivity extends BaseActivity implements NavigationView.OnNavigationItemSelectedListener {
    private DrawerLayout drawer;
    private DrawerAdapter drawerAdapter;
    private ListView drawerLV;
    private TextView titleTV;
    private ActionBarDrawerToggle toggle;
    private Toolbar toolbarTB;
    private ArrayList<DrawerData> drawerItems = new ArrayList<>();
    AdapterView.OnItemClickListener seekerDrawerListener = new AdapterView.OnItemClickListener() { // from class: com.developer.faker.Activity.MainActivity.1
        AnonymousClass1() {
        }

        @Override // android.widget.AdapterView.OnItemClickListener
        public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
            if (i == 0) {
                MainActivity.this.gotoMainFragment();
                MainActivity.this.titleTV.setText(R.string.title_phoneNumber);
            } else if (i == 1) {
                MainActivity.this.gotoBlockFragment();
                MainActivity.this.titleTV.setText("차단관리");
            } else if (i == 2) {
                MainActivity.this.gotoNoticeFragmentEx();
            } else if (i != 3) {
                if (i == 4) {
                    UtilAuth utilAuth = UtilAuth.getInstance(MainActivity.this.getBaseContext());
                    utilAuth.UserToken = null;
                    utilAuth.saveAuthInfo();
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) LoginActivity.class));
                    MainActivity.this.overridePendingTransition(R.anim.slidein, R.anim.slideout);
                    MainActivity.this.finish();
                }
                MainActivity.this.drawer.closeDrawers();
            } else {
                MainActivity.this.gotoSettingFragment();
                MainActivity.this.titleTV.setText("환경설정");
            }
            MainActivity.this.drawer.closeDrawers();
        }
    };
    private BackPressHandler backPressHandler = new BackPressHandler(this);

    /* renamed from: com.developer.faker.Activity.MainActivity$1 */
    class AnonymousClass1 implements AdapterView.OnItemClickListener {
        AnonymousClass1() {
        }

        @Override // android.widget.AdapterView.OnItemClickListener
        public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
            if (i == 0) {
                MainActivity.this.gotoMainFragment();
                MainActivity.this.titleTV.setText(R.string.title_phoneNumber);
            } else if (i == 1) {
                MainActivity.this.gotoBlockFragment();
                MainActivity.this.titleTV.setText("차단관리");
            } else if (i == 2) {
                MainActivity.this.gotoNoticeFragmentEx();
            } else if (i != 3) {
                if (i == 4) {
                    UtilAuth utilAuth = UtilAuth.getInstance(MainActivity.this.getBaseContext());
                    utilAuth.UserToken = null;
                    utilAuth.saveAuthInfo();
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) LoginActivity.class));
                    MainActivity.this.overridePendingTransition(R.anim.slidein, R.anim.slideout);
                    MainActivity.this.finish();
                }
                MainActivity.this.drawer.closeDrawers();
            } else {
                MainActivity.this.gotoSettingFragment();
                MainActivity.this.titleTV.setText("환경설정");
            }
            MainActivity.this.drawer.closeDrawers();
        }
    }

    public void gotoNoticeFragmentEx() {
        gotoNoticeFragment();
        this.titleTV.setText("공지사항");
    }

    private void initDrawer() throws Resources.NotFoundException {
        this.drawerItems.clear();
        Integer numValueOf = Integer.valueOf(R.drawable.icon_search);
        Integer[] numArr = {numValueOf, Integer.valueOf(R.drawable.icon_block), Integer.valueOf(R.drawable.icon_mail), numValueOf, Integer.valueOf(R.drawable.icon_logout)};
        String[] stringArray = getResources().getStringArray(R.array.drawer_items);
        for (int i = 0; i < stringArray.length; i++) {
            DrawerData drawerData = new DrawerData();
            drawerData.icon = numArr[i];
            drawerData.name = stringArray[i];
            if (i == stringArray.length - 2) {
                drawerData.cnt = "12";
            }
            this.drawerItems.add(drawerData);
        }
        this.drawerAdapter = new DrawerAdapter(this, 0, this.drawerItems);
        this.drawerLV.setAdapter((ListAdapter) this.drawerAdapter);
        this.drawerLV.setOnItemClickListener(this.seekerDrawerListener);
    }

    private void initFragment() {
        if (Global.Incoming_Call_Number != BuildConfig.FLAVOR) {
            gotoSearchFragment();
        } else if (Utils.getInstance().getNewNoticeState(getBaseContext())) {
            gotoNoticeFragment();
        } else {
            gotoMainFragment();
        }
    }

    private void initToolBar() {
        this.toolbarTB = (Toolbar) findViewById(R.id.toolbar);
        this.titleTV = (TextView) findViewById(R.id.titleTV);
        this.titleTV.setText(getString(R.string.tab_txt_name));
        setSupportActionBar(this.toolbarTB);
        getSupportActionBar().setTitle(BuildConfig.FLAVOR);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
    }

    @Override // com.developer.faker.Activity.BaseActivity, android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.support.v4.app.SupportActivity, android.app.Activity
    protected void onCreate(Bundle bundle) throws IOException {
        super.onCreate(bundle);
        setContentView(R.layout.activity_main);
        try {
            setSupportActionBar((Toolbar) findViewById(R.id.toolbar));
            this.inputMethodManager = (InputMethodManager) getSystemService("input_method");
            this.drawerLV = (ListView) findViewById(R.id.drawerLV);
            this.drawer = (DrawerLayout) findViewById(R.id.drawer_layout);
            if (Utils.isNullOrEmptyString(UtilAuth.getInstance(getBaseContext()).UserToken)) {
                startActivity(new Intent(this, (Class<?>) LoginActivity.class));
                overridePendingTransition(R.anim.slidein, R.anim.slideout);
                finish();
            }
            initToolBar();
            initDrawer();
            initFragment();
            this.toggle = new ActionBarDrawerToggle(this, this.drawer, null, R.string.navigation_drawer_open, R.string.navigation_drawer_close) { // from class: com.developer.faker.Activity.MainActivity.2
                AnonymousClass2(Activity this, DrawerLayout drawerLayout, Toolbar toolbar, int i, int i2) {
                    super(this, drawerLayout, toolbar, i, i2);
                }

                @Override // android.support.v7.app.ActionBarDrawerToggle, android.support.v4.widget.DrawerLayout.DrawerListener
                public void onDrawerStateChanged(int i) {
                    super.onDrawerStateChanged(i);
                    MainActivity.this.hideSoftKeyboard();
                }

                @Override // android.support.v7.app.ActionBarDrawerToggle, android.support.v4.widget.DrawerLayout.DrawerListener
                public void onDrawerOpened(View view) {
                    MainActivity.this.invalidateOptionsMenu();
                }

                @Override // android.support.v7.app.ActionBarDrawerToggle, android.support.v4.widget.DrawerLayout.DrawerListener
                public void onDrawerClosed(View view) {
                    MainActivity.this.invalidateOptionsMenu();
                }
            };
            this.toggle.syncState();
            this.drawer.addDrawerListener(this.toggle);
        } catch (Exception e) {
            UtilLogFile.getInstance(getBaseContext()).writeLog(e.toString());
        }
        UtilAuth utilAuth = UtilAuth.getInstance(getBaseContext());
        ((TextView) findViewById(R.id.txtCompany)).setText(utilAuth.UserCompany);
        ((TextView) findViewById(R.id.txtRemainDay)).setText("만료일: " + utilAuth.GetLicenseEndDate());
    }

    /* renamed from: com.developer.faker.Activity.MainActivity$2 */
    class AnonymousClass2 extends ActionBarDrawerToggle {
        AnonymousClass2(Activity this, DrawerLayout drawerLayout, Toolbar toolbar, int i, int i2) {
            super(this, drawerLayout, toolbar, i, i2);
        }

        @Override // android.support.v7.app.ActionBarDrawerToggle, android.support.v4.widget.DrawerLayout.DrawerListener
        public void onDrawerStateChanged(int i) {
            super.onDrawerStateChanged(i);
            MainActivity.this.hideSoftKeyboard();
        }

        @Override // android.support.v7.app.ActionBarDrawerToggle, android.support.v4.widget.DrawerLayout.DrawerListener
        public void onDrawerOpened(View view) {
            MainActivity.this.invalidateOptionsMenu();
        }

        @Override // android.support.v7.app.ActionBarDrawerToggle, android.support.v4.widget.DrawerLayout.DrawerListener
        public void onDrawerClosed(View view) {
            MainActivity.this.invalidateOptionsMenu();
        }
    }

    @Override // android.support.v4.app.FragmentActivity, android.app.Activity
    public void onBackPressed() {
        if (this.drawer.isDrawerOpen(GravityCompat.START)) {
            this.drawer.closeDrawer(GravityCompat.START);
            return;
        }
        if (Global.fragment_State == Const.FRAGMENT_STATE_MAIN) {
            this.backPressHandler.onBackPressed("'뒤로' 버튼을 한번 더 누르시면 로그아웃됩니다.", 2000);
            return;
        }
        if (Global.fragment_State == Const.FRAGMENT_STATE_SEARCH || Global.fragment_State == Const.FRAGMENT_STATE_WEB || Global.fragment_State == Const.FRAGMENT_STATE_NOTICE || Global.fragment_State == Const.FRAGMENT_STATE_BLOCK || Global.fragment_State == Const.FRAGMENT_STATE_SETTING) {
            TextView textView = this.titleTV;
            if (textView != null) {
                textView.setText(R.string.title_phoneNumber);
            }
            gotoMainFragment();
        }
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main, menu);
        this.toggle.setDrawerIndicatorEnabled(true);
        return true;
    }

    @Override // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        this.drawerAdapter.notifyDataSetChanged();
        this.drawer.openDrawer(3);
        return super.onOptionsItemSelected(menuItem);
    }

    @Override // android.support.design.widget.NavigationView.OnNavigationItemSelectedListener
    public boolean onNavigationItemSelected(MenuItem menuItem) {
        ((DrawerLayout) findViewById(R.id.drawer_layout)).closeDrawer(GravityCompat.START);
        return true;
    }
}
