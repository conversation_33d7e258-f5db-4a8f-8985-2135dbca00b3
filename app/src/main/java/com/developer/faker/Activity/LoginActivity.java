package com.developer.faker.Activity;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.design.widget.CoordinatorLayout;
import android.support.design.widget.Snackbar;
import android.support.v4.app.ActivityCompat;
import android.support.v4.content.ContextCompat;
import android.support.v4.view.PointerIconCompat;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import com.developer.faker.Data.UserInfo;
import com.developer.faker.R;
import com.developer.faker.Utils.BackPressHandler;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.RC4;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilLogFile;
import com.developer.faker.Utils.Utils;
// import com.mashape.relocation.cookie.ClientCookie;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.async.Callback;
import com.mashape.unirest.http.exceptions.UnirestException;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class LoginActivity extends BaseActivity implements ActivityCompat.OnRequestPermissionsResultCallback {
    CoordinatorLayout coordinatorLayout;
    Button m_BtnLogin;
    EditText m_EdtUserID;
    EditText m_EdtUserPass;
    private int PERMISSIONS_REQUEST_CODE = PointerIconCompat.TYPE_CONTEXT_MENU;
    private int OVERLAY_PERMISSION_REQ_CODE = PointerIconCompat.TYPE_HAND;
    private UpdateHandler updateHandler = new UpdateHandler();
    private UtilAuth utilAuth = null;
    private Handler ResultHandler = new Handler() { // from class: com.developer.faker.Activity.LoginActivity.1
        /* JADX WARN: Removed duplicated region for block: B:32:0x011d  */
        @Override // android.os.Handler
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public void handleMessage(android.os.Message r8) {
            /*
                Method dump skipped, instructions count: 398
                To view this dump change 'Code comments level' option to 'DEBUG'
            */
            throw new UnsupportedOperationException("Method not decompiled: com.developer.faker.Activity.LoginActivity.AnonymousClass1.handleMessage(android.os.Message):void");
        }
    };
    private String m_RequestURL = "";
    private Map<String, String> m_Header = new HashMap();
    private BackPressHandler backPressHandler = new BackPressHandler(this);

    @Override // com.developer.faker.Activity.BaseActivity, android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.support.v4.app.SupportActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.activity_login);
        this.utilAuth = UtilAuth.getInstance(getBaseContext());
        this.coordinatorLayout = (CoordinatorLayout) findViewById(R.id.coordinatorLayout);
        this.m_EdtUserID = (EditText) findViewById(R.id.edtLogID);
        this.m_EdtUserID.setFocusable(true);
        this.m_EdtUserID.setEnabled(false);
        this.m_EdtUserPass = (EditText) findViewById(R.id.edtLogPass);
        this.m_EdtUserPass.setEnabled(false);
        this.m_BtnLogin = (Button) findViewById(R.id.btnLogin);
        this.m_BtnLogin.setEnabled(false);
        RequestPermission();
        this.m_EdtUserID.setText(this.utilAuth.UserEmail);
        this.m_EdtUserPass.setText(this.utilAuth.UserPWD);
    }

    public void onClickBtnLogin(View view) throws IOException {
        if (this.m_EdtUserID.getText().toString().isEmpty()) {
            showToast("아이디를 입력하세요.");
        } else if (this.m_EdtUserPass.getText().toString().isEmpty()) {
            showToast("비번을 입력하세요.");
        } else {
            Login();
        }
    }

    private void Login() throws IOException {
        try {
            UserInfo userInfo = new UserInfo();
            userInfo.userID = this.m_EdtUserID.getText().toString();
            userInfo.userPass = this.m_EdtUserPass.getText().toString();
            userInfo.devToken = this.utilAuth.getDeviceToken();
            if (!Utils.isNullOrEmptyString(userInfo.userID) && !Utils.isNullOrEmptyString(userInfo.userPass)) {
                ShowProgress(getResources().getString(R.string.wait));
                LoginWithEmail(userInfo);
            }
        } catch (Exception e) {
            UtilLogFile.getInstance(getBaseContext()).writeLog(e.toString());
        }
    }

    private void LoginWithEmail(UserInfo userInfo) {
        String str = Utils.getServerUrl() + Const.API_USER_LOGIN;
        JSONObject jSONObject = new JSONObject();
        try {
            jSONObject.put("Name", userInfo.userID);
            jSONObject.put("Password", userInfo.userPass);
            jSONObject.put("PhoneNumber", Utils.getPhoneNumber(this));
            jSONObject.put("Version", 1029);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String str2 = str + "&data=" + Uri.encode(RC4.getInstance().encrypt(jSONObject.toString(), Const.AUTH_KEY));
        this.m_Header.put("apptype", "3");
        this.m_RequestURL = str2;
        new Thread(new Runnable() { // from class: com.developer.faker.Activity.LoginActivity.2
            @Override // java.lang.Runnable
            public void run() {
                Unirest.get(LoginActivity.this.m_RequestURL).headers(LoginActivity.this.m_Header).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Activity.LoginActivity.2.1
                    @Override // com.mashape.unirest.http.async.Callback
                    public void cancelled() {
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void completed(HttpResponse<String> httpResponse) {
                        Message message = new Message();
                        message.what = 0;
                        message.obj = new String(httpResponse.getBody().toString());
                        LoginActivity.this.ResultHandler.sendMessage(message);
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void failed(UnirestException unirestException) {
                        Message message = new Message();
                        message.what = 1;
                        message.obj = "서버에 접속할수 없습니다.";
                        LoginActivity.this.ResultHandler.sendMessage(message);
                    }
                });
            }
        }).start();
    }

    public void RequestPermission() {
        ArrayList<String> arrayList = new ArrayList();
        arrayList.add("android.permission.PROCESS_OUTGOING_CALLS");
        arrayList.add("android.permission.INTERNET");
        arrayList.add("android.permission.ACCESS_NETWORK_STATE");
        arrayList.add("android.permission.READ_CONTACTS");
        arrayList.add("android.permission.WRITE_CONTACTS");
        arrayList.add("android.permission.RECEIVE_SMS");
        arrayList.add("android.permission.READ_SMS");
        arrayList.add("android.permission.CALL_PHONE");
        arrayList.add("android.permission.READ_CALL_LOG");
        arrayList.add("android.permission.READ_PHONE_STATE");
        arrayList.add("android.permission.RECEIVE_BOOT_COMPLETED");
        if (Build.VERSION.SDK_INT >= 28) {
            arrayList.add("android.permission.ANSWER_PHONE_CALLS");
        }
        ArrayList arrayList2 = new ArrayList();
        for (String str : arrayList) {
            if (ContextCompat.checkSelfPermission(this, str) != 0) {
                arrayList2.add(str);
            }
        }
        if (!arrayList2.isEmpty()) {
            ActivityCompat.requestPermissions(this, (String[]) arrayList2.toArray(new String[0]), this.PERMISSIONS_REQUEST_CODE);
            return;
        }
        if (Build.VERSION.SDK_INT >= 23) {
            if (!Settings.canDrawOverlays(getApplicationContext())) {
                startActivityForResult(new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + getPackageName())), this.OVERLAY_PERMISSION_REQ_CODE);
                return;
            }
            this.m_BtnLogin.setEnabled(true);
            this.m_EdtUserID.setEnabled(true);
            this.m_EdtUserPass.setEnabled(true);
            CheckUpdate();
            return;
        }
        this.m_BtnLogin.setEnabled(true);
        this.m_EdtUserID.setEnabled(true);
        this.m_EdtUserPass.setEnabled(true);
        CheckUpdate();
    }

    @Override // android.support.v4.app.FragmentActivity, android.app.Activity, android.support.v4.app.ActivityCompat.OnRequestPermissionsResultCallback
    public void onRequestPermissionsResult(int i, @NonNull String[] strArr, @NonNull int[] iArr) {
        super.onRequestPermissionsResult(i, strArr, iArr);
        boolean z = false;
        if (iArr.length != 0) {
            int length = iArr.length;
            int i2 = 0;
            while (true) {
                if (i2 >= length) {
                    z = true;
                    break;
                } else if (iArr[i2] != 0) {
                    break;
                } else {
                    i2++;
                }
            }
        }
        if (!z) {
            Snackbar.make(this.coordinatorLayout, R.string.permissions_required, -2).setAction(R.string.blacklist_request_permissions, new View.OnClickListener() { // from class: com.developer.faker.Activity.LoginActivity.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    LoginActivity.this.RequestPermission();
                }
            }).show();
            return;
        }
        if (Build.VERSION.SDK_INT < 23 || Settings.canDrawOverlays(getApplicationContext())) {
            return;
        }
        startActivityForResult(new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + getPackageName())), this.OVERLAY_PERMISSION_REQ_CODE);
    }

    @Override // android.support.v4.app.FragmentActivity, android.app.Activity
    public void onBackPressed() {
        this.backPressHandler.onBackPressed("'뒤로' 버튼을 한번 더 누르시면 종료됩니다.", 2000);
    }

    @Override // android.support.v4.app.FragmentActivity, android.app.Activity
    protected void onActivityResult(int i, int i2, @Nullable Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (i != this.OVERLAY_PERMISSION_REQ_CODE || Build.VERSION.SDK_INT < 23) {
            return;
        }
        if (!Settings.canDrawOverlays(getApplicationContext())) {
            Snackbar.make(this.coordinatorLayout, R.string.permissions_required, -2).setAction(R.string.blacklist_request_permissions, new View.OnClickListener() { // from class: com.developer.faker.Activity.LoginActivity.4
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    LoginActivity.this.RequestPermission();
                }
            }).show();
            return;
        }
        this.m_BtnLogin.setEnabled(true);
        this.m_EdtUserID.setEnabled(true);
        this.m_EdtUserPass.setEnabled(true);
        CheckUpdate();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void autoStart() throws IOException {
        DismissProgress();
        startMyService();
        if (this.utilAuth.isHaveToken().booleanValue()) {
            Login();
        }
    }

    @Override // android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.app.Activity
    protected void onPostResume() {
        Global.Incoming_Call_Number = "";
        super.onPostResume();
    }

    private void CheckUpdate() {
        try {
            ShowProgress(getResources().getString(R.string.wait));
        } catch (IOException e) {
            e.printStackTrace();
        }
        final String str = Utils.getServerUrl() + Const.API_CHECK_UPDATE;
        new JSONObject();
        final HashMap map = new HashMap();
        new Thread(new Runnable() { // from class: com.developer.faker.Activity.LoginActivity.5
            @Override // java.lang.Runnable
            public void run() {
                Unirest.get(str).headers(map).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Activity.LoginActivity.5.1
                    @Override // com.mashape.unirest.http.async.Callback
                    public void cancelled() {
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void completed(HttpResponse<String> httpResponse) {
                        Message message = new Message();
                        message.what = 0;
                        message.obj = new String(httpResponse.getBody().toString());
                        LoginActivity.this.updateHandler.sendMessage(message);
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void failed(UnirestException unirestException) {
                        Message message = new Message();
                        message.what = 1;
                        message.obj = "서버에 접속할수 없습니다.";
                        LoginActivity.this.updateHandler.sendMessage(message);
                    }
                });
            }
        }).start();
    }

    public class UpdateHandler extends Handler {
        public boolean installApp(String str) {
            return true;
        }

        public UpdateHandler() {
        }

        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            int i = message.what;
            if (i == 0) {
                try {
                    if (new JSONObject(RC4.getInstance().decrypt(new JSONObject(message.obj.toString()).getString("data"), Const.AUTH_KEY)).getInt("version") <= 1029) {
                        LoginActivity.this.autoStart();
                    } else {
                        AlertDialog.Builder builder = new AlertDialog.Builder(LoginActivity.this);
                        builder.setCancelable(false);
                        builder.setMessage("업데이트 버전이 있습니다.\n 관리자에게 문의해주세요.");
                        builder.setPositiveButton("예", new DialogInterface.OnClickListener() { // from class: com.developer.faker.Activity.LoginActivity.UpdateHandler.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i2) {
                                LoginActivity.this.finish();
                            }
                        });
                        builder.show();
                    }
                    return;
                } catch (Exception unused) {
                    try {
                        LoginActivity.this.autoStart();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return;
                }
            }
            if (i == 1) {
                try {
                    LoginActivity.this.autoStart();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                return;
            }
            if (i != 100) {
                return;
            }
            Bundle data = message.getData();
            if (data.getInt("error") == 1) {
                if (installApp(data.getString("data"))) {
                    return;
                }
                LoginActivity.this.finish();
                return;
            }
            LoginActivity.this.finish();
        }

        private boolean haveApkFile(String str) {
            return new File(Environment.getExternalStorageDirectory().getPath() + "/" + str).exists();
        }
    }
}