<?xml version="1.0" encoding="utf-8"?>
<resources>
    <array name="drawer_items">
        <item>전화번호 검색</item>
        <item>콜폭방어</item>
        <item>공지사항</item>
        <item>환경설정</item>
        <item>로그아웃</item>
    </array>
    <attr format="reference" name="actionBarDivider">
    </attr>
    <attr format="reference" name="actionBarItemBackground">
    </attr>
    <attr format="reference" name="actionBarPopupTheme">
    </attr>
    <attr format="dimension" name="actionBarSize">
        <enum name="wrap_content" value="0"/>
    </attr>
    <attr format="reference" name="actionBarSplitStyle">
    </attr>
    <attr format="reference" name="actionBarStyle">
    </attr>
    <attr format="reference" name="actionBarTabBarStyle">
    </attr>
    <attr format="reference" name="actionBarTabStyle">
    </attr>
    <attr format="reference" name="actionBarTabTextStyle">
    </attr>
    <attr format="reference" name="actionBarTheme">
    </attr>
    <attr format="reference" name="actionBarWidgetTheme">
    </attr>
    <attr format="reference" name="actionButtonStyle">
    </attr>
    <attr format="reference" name="actionDropDownStyle">
    </attr>
    <attr format="reference" name="actionLayout">
    </attr>
    <attr format="reference" name="actionMenuTextAppearance">
    </attr>
    <attr format="reference|color" name="actionMenuTextColor">
    </attr>
    <attr format="reference" name="actionModeBackground">
    </attr>
    <attr format="reference" name="actionModeCloseButtonStyle">
    </attr>
    <attr format="reference" name="actionModeCloseDrawable">
    </attr>
    <attr format="reference" name="actionModeCopyDrawable">
    </attr>
    <attr format="reference" name="actionModeCutDrawable">
    </attr>
    <attr format="reference" name="actionModeFindDrawable">
    </attr>
    <attr format="reference" name="actionModePasteDrawable">
    </attr>
    <attr format="reference" name="actionModePopupWindowStyle">
    </attr>
    <attr format="reference" name="actionModeSelectAllDrawable">
    </attr>
    <attr format="reference" name="actionModeShareDrawable">
    </attr>
    <attr format="reference" name="actionModeSplitBackground">
    </attr>
    <attr format="reference" name="actionModeStyle">
    </attr>
    <attr format="reference" name="actionModeWebSearchDrawable">
    </attr>
    <attr format="reference" name="actionOverflowButtonStyle">
    </attr>
    <attr format="reference" name="actionOverflowMenuStyle">
    </attr>
    <attr format="string" name="actionProviderClass">
    </attr>
    <attr format="string" name="actionViewClass">
    </attr>
    <attr format="reference" name="activityChooserViewStyle">
    </attr>
    <attr format="reference" name="alertDialogButtonGroupStyle">
    </attr>
    <attr format="boolean" name="alertDialogCenterButtons">
    </attr>
    <attr format="reference" name="alertDialogStyle">
    </attr>
    <attr format="reference" name="alertDialogTheme">
    </attr>
    <attr format="boolean" name="allowStacking">
    </attr>
    <attr format="float" name="alpha">
    </attr>
    <attr name="alphabeticModifiers">
        <flag name="ALT" value="2"/>
        <flag name="CTRL" value="4096"/>
        <flag name="FUNCTION" value="8"/>
        <flag name="META" value="65536"/>
        <flag name="SHIFT" value="1"/>
        <flag name="SYM" value="4"/>
    </attr>
    <attr format="dimension" name="arrowHeadLength">
    </attr>
    <attr format="dimension" name="arrowShaftLength">
    </attr>
    <attr format="reference" name="autoCompleteTextViewStyle">
    </attr>
    <attr format="dimension" name="autoSizeMaxTextSize">
    </attr>
    <attr format="dimension" name="autoSizeMinTextSize">
    </attr>
    <attr format="reference" name="autoSizePresetSizes">
    </attr>
    <attr format="dimension" name="autoSizeStepGranularity">
    </attr>
    <attr name="autoSizeTextType">
        <enum name="none" value="0"/>
        <enum name="uniform" value="1"/>
    </attr>
    <attr format="reference" name="background">
    </attr>
    <attr format="reference|color" name="backgroundSplit">
    </attr>
    <attr format="reference|color" name="backgroundStacked">
    </attr>
    <attr format="color" name="backgroundTint">
    </attr>
    <attr name="backgroundTintMode">
        <enum name="add" value="16"/>
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="dimension" name="barLength">
    </attr>
    <attr format="boolean" name="behavior_autoHide">
    </attr>
    <attr format="boolean" name="behavior_fitToContents">
    </attr>
    <attr format="boolean" name="behavior_hideable">
    </attr>
    <attr format="dimension" name="behavior_overlapTop">
    </attr>
    <attr format="dimension" name="behavior_peekHeight">
        <enum name="auto" value="-1"/>
    </attr>
    <attr format="boolean" name="behavior_skipCollapsed">
    </attr>
    <attr format="dimension" name="borderWidth">
    </attr>
    <attr format="reference" name="borderlessButtonStyle">
    </attr>
    <attr format="reference" name="bottomAppBarStyle">
    </attr>
    <attr format="reference" name="bottomNavigationStyle">
    </attr>
    <attr format="reference" name="bottomSheetDialogTheme">
    </attr>
    <attr format="reference" name="bottomSheetStyle">
    </attr>
    <attr format="color" name="boxBackgroundColor">
    </attr>
    <attr name="boxBackgroundMode">
        <enum name="filled" value="1"/>
        <enum name="none" value="0"/>
        <enum name="outline" value="2"/>
    </attr>
    <attr format="dimension" name="boxCollapsedPaddingTop">
    </attr>
    <attr format="dimension" name="boxCornerRadiusBottomEnd">
    </attr>
    <attr format="dimension" name="boxCornerRadiusBottomStart">
    </attr>
    <attr format="dimension" name="boxCornerRadiusTopEnd">
    </attr>
    <attr format="dimension" name="boxCornerRadiusTopStart">
    </attr>
    <attr format="color" name="boxStrokeColor">
    </attr>
    <attr format="dimension" name="boxStrokeWidth">
    </attr>
    <attr format="reference" name="buttonBarButtonStyle">
    </attr>
    <attr format="reference" name="buttonBarNegativeButtonStyle">
    </attr>
    <attr format="reference" name="buttonBarNeutralButtonStyle">
    </attr>
    <attr format="reference" name="buttonBarPositiveButtonStyle">
    </attr>
    <attr format="reference" name="buttonBarStyle">
    </attr>
    <attr name="buttonGravity">
        <flag name="bottom" value="80"/>
        <flag name="top" value="48"/>
    </attr>
    <attr format="dimension" name="buttonIconDimen">
    </attr>
    <attr format="reference" name="buttonPanelSideLayout">
    </attr>
    <attr format="reference" name="buttonStyle">
    </attr>
    <attr format="reference" name="buttonStyleSmall">
    </attr>
    <attr format="color" name="buttonTint">
    </attr>
    <attr name="buttonTintMode">
        <enum name="add" value="16"/>
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="color" name="cardBackgroundColor">
    </attr>
    <attr format="dimension" name="cardCornerRadius">
    </attr>
    <attr format="dimension" name="cardElevation">
    </attr>
    <attr format="dimension" name="cardMaxElevation">
    </attr>
    <attr format="boolean" name="cardPreventCornerOverlap">
    </attr>
    <attr format="boolean" name="cardUseCompatPadding">
    </attr>
    <attr format="reference" name="cardViewStyle">
    </attr>
    <attr format="reference" name="checkboxStyle">
    </attr>
    <attr format="reference" name="checkedChip">
    </attr>
    <attr format="reference" name="checkedIcon">
    </attr>
    <attr format="boolean" name="checkedIconEnabled">
    </attr>
    <attr format="boolean" name="checkedIconVisible">
    </attr>
    <attr format="reference" name="checkedTextViewStyle">
    </attr>
    <attr format="color" name="chipBackgroundColor">
    </attr>
    <attr format="dimension" name="chipCornerRadius">
    </attr>
    <attr format="dimension" name="chipEndPadding">
    </attr>
    <attr format="reference" name="chipGroupStyle">
    </attr>
    <attr format="reference" name="chipIcon">
    </attr>
    <attr format="boolean" name="chipIconEnabled">
    </attr>
    <attr format="dimension" name="chipIconSize">
    </attr>
    <attr format="color" name="chipIconTint">
    </attr>
    <attr format="boolean" name="chipIconVisible">
    </attr>
    <attr format="dimension" name="chipMinHeight">
    </attr>
    <attr format="dimension" name="chipSpacing">
    </attr>
    <attr format="dimension" name="chipSpacingHorizontal">
    </attr>
    <attr format="dimension" name="chipSpacingVertical">
    </attr>
    <attr format="reference" name="chipStandaloneStyle">
    </attr>
    <attr format="dimension" name="chipStartPadding">
    </attr>
    <attr format="color" name="chipStrokeColor">
    </attr>
    <attr format="dimension" name="chipStrokeWidth">
    </attr>
    <attr format="reference" name="chipStyle">
    </attr>
    <attr format="reference" name="closeIcon">
    </attr>
    <attr format="boolean" name="closeIconEnabled">
    </attr>
    <attr format="dimension" name="closeIconEndPadding">
    </attr>
    <attr format="dimension" name="closeIconSize">
    </attr>
    <attr format="dimension" name="closeIconStartPadding">
    </attr>
    <attr format="color" name="closeIconTint">
    </attr>
    <attr format="boolean" name="closeIconVisible">
    </attr>
    <attr format="reference" name="closeItemLayout">
    </attr>
    <attr format="string" name="collapseContentDescription">
    </attr>
    <attr format="reference" name="collapseIcon">
    </attr>
    <attr name="collapsedTitleGravity">
        <flag name="bottom" value="80"/>
        <flag name="center" value="17"/>
        <flag name="center_horizontal" value="1"/>
        <flag name="center_vertical" value="16"/>
        <flag name="end" value="8388613"/>
        <flag name="fill_vertical" value="112"/>
        <flag name="left" value="3"/>
        <flag name="right" value="5"/>
        <flag name="start" value="8388611"/>
        <flag name="top" value="48"/>
    </attr>
    <attr format="reference" name="collapsedTitleTextAppearance">
    </attr>
    <attr format="color" name="color">
    </attr>
    <attr format="color" name="colorAccent">
    </attr>
    <attr format="color" name="colorBackgroundFloating">
    </attr>
    <attr format="color" name="colorButtonNormal">
    </attr>
    <attr format="color" name="colorControlActivated">
    </attr>
    <attr format="color" name="colorControlHighlight">
    </attr>
    <attr format="color" name="colorControlNormal">
    </attr>
    <attr format="reference|color" name="colorError">
    </attr>
    <attr format="color" name="colorPrimary">
    </attr>
    <attr format="color" name="colorPrimaryDark">
    </attr>
    <attr format="color" name="colorSecondary">
    </attr>
    <attr format="color" name="colorSwitchThumbNormal">
    </attr>
    <attr format="reference" name="commitIcon">
    </attr>
    <attr format="string" name="contentDescription">
    </attr>
    <attr format="dimension" name="contentInsetEnd">
    </attr>
    <attr format="dimension" name="contentInsetEndWithActions">
    </attr>
    <attr format="dimension" name="contentInsetLeft">
    </attr>
    <attr format="dimension" name="contentInsetRight">
    </attr>
    <attr format="dimension" name="contentInsetStart">
    </attr>
    <attr format="dimension" name="contentInsetStartWithNavigation">
    </attr>
    <attr format="dimension" name="contentPadding">
    </attr>
    <attr format="dimension" name="contentPaddingBottom">
    </attr>
    <attr format="dimension" name="contentPaddingLeft">
    </attr>
    <attr format="dimension" name="contentPaddingRight">
    </attr>
    <attr format="dimension" name="contentPaddingTop">
    </attr>
    <attr format="color" name="contentScrim">
    </attr>
    <attr format="reference" name="controlBackground">
    </attr>
    <attr format="reference" name="coordinatorLayoutStyle">
    </attr>
    <attr format="dimension" name="cornerRadius">
    </attr>
    <attr format="boolean" name="counterEnabled">
    </attr>
    <attr format="integer" name="counterMaxLength">
    </attr>
    <attr format="reference" name="counterOverflowTextAppearance">
    </attr>
    <attr format="reference" name="counterTextAppearance">
    </attr>
    <attr format="reference" name="customNavigationLayout">
    </attr>
    <attr format="string" name="defaultQueryHint">
    </attr>
    <attr format="dimension" name="dialogCornerRadius">
    </attr>
    <attr format="dimension" name="dialogPreferredPadding">
    </attr>
    <attr format="reference" name="dialogTheme">
    </attr>
    <attr name="displayOptions">
        <flag name="disableHome" value="32"/>
        <flag name="homeAsUp" value="4"/>
        <flag name="none" value="0"/>
        <flag name="showCustom" value="16"/>
        <flag name="showHome" value="2"/>
        <flag name="showTitle" value="8"/>
        <flag name="useLogo" value="1"/>
    </attr>
    <attr format="reference" name="divider">
    </attr>
    <attr format="reference" name="dividerHorizontal">
    </attr>
    <attr format="dimension" name="dividerPadding">
    </attr>
    <attr format="reference" name="dividerVertical">
    </attr>
    <attr format="dimension" name="drawableSize">
    </attr>
    <attr format="reference" name="drawerArrowStyle">
    </attr>
    <attr format="reference" name="dropDownListViewStyle">
    </attr>
    <attr format="dimension" name="dropdownListPreferredItemHeight">
    </attr>
    <attr format="reference" name="editTextBackground">
    </attr>
    <attr format="reference|color" name="editTextColor">
    </attr>
    <attr format="reference" name="editTextStyle">
    </attr>
    <attr format="dimension" name="elevation">
    </attr>
    <attr format="boolean" name="enforceMaterialTheme">
    </attr>
    <attr format="boolean" name="enforceTextAppearance">
    </attr>
    <attr format="boolean" name="errorEnabled">
    </attr>
    <attr format="reference" name="errorTextAppearance">
    </attr>
    <attr format="reference" name="expandActivityOverflowButtonDrawable">
    </attr>
    <attr format="boolean" name="expanded">
    </attr>
    <attr name="expandedTitleGravity">
        <flag name="bottom" value="80"/>
        <flag name="center" value="17"/>
        <flag name="center_horizontal" value="1"/>
        <flag name="center_vertical" value="16"/>
        <flag name="end" value="8388613"/>
        <flag name="fill_vertical" value="112"/>
        <flag name="left" value="3"/>
        <flag name="right" value="5"/>
        <flag name="start" value="8388611"/>
        <flag name="top" value="48"/>
    </attr>
    <attr format="dimension" name="expandedTitleMargin">
    </attr>
    <attr format="dimension" name="expandedTitleMarginBottom">
    </attr>
    <attr format="dimension" name="expandedTitleMarginEnd">
    </attr>
    <attr format="dimension" name="expandedTitleMarginStart">
    </attr>
    <attr format="dimension" name="expandedTitleMarginTop">
    </attr>
    <attr format="reference" name="expandedTitleTextAppearance">
    </attr>
    <attr name="fabAlignmentMode">
        <enum name="center" value="0"/>
        <enum name="end" value="1"/>
    </attr>
    <attr format="dimension" name="fabCradleMargin">
    </attr>
    <attr format="dimension" name="fabCradleRoundedCornerRadius">
    </attr>
    <attr format="dimension" name="fabCradleVerticalOffset">
    </attr>
    <attr format="dimension" name="fabCustomSize">
    </attr>
    <attr name="fabSize">
        <enum name="auto" value="-1"/>
        <enum name="mini" value="1"/>
        <enum name="normal" value="0"/>
    </attr>
    <attr format="boolean" name="fastScrollEnabled">
    </attr>
    <attr format="reference" name="fastScrollHorizontalThumbDrawable">
    </attr>
    <attr format="reference" name="fastScrollHorizontalTrackDrawable">
    </attr>
    <attr format="reference" name="fastScrollVerticalThumbDrawable">
    </attr>
    <attr format="reference" name="fastScrollVerticalTrackDrawable">
    </attr>
    <attr format="dimension" name="firstBaselineToTopHeight">
    </attr>
    <attr format="reference|string|integer|boolean|color|float|dimension|fraction" name="floatingActionButtonStyle">
    </attr>
    <attr format="reference" name="font">
    </attr>
    <attr format="string" name="fontFamily">
    </attr>
    <attr format="string" name="fontProviderAuthority">
    </attr>
    <attr format="reference" name="fontProviderCerts">
    </attr>
    <attr name="fontProviderFetchStrategy">
        <enum name="async" value="1"/>
        <enum name="blocking" value="0"/>
    </attr>
    <attr format="integer" name="fontProviderFetchTimeout">
        <enum name="forever" value="-1"/>
    </attr>
    <attr format="string" name="fontProviderPackage">
    </attr>
    <attr format="string" name="fontProviderQuery">
    </attr>
    <attr name="fontStyle">
        <enum name="italic" value="1"/>
        <enum name="normal" value="0"/>
    </attr>
    <attr format="string" name="fontVariationSettings">
    </attr>
    <attr format="integer" name="fontWeight">
    </attr>
    <attr format="boolean" name="foregroundInsidePadding">
    </attr>
    <attr format="dimension" name="gapBetweenBars">
    </attr>
    <attr format="reference" name="goIcon">
    </attr>
    <attr format="reference" name="headerLayout">
    </attr>
    <attr format="dimension" name="height">
    </attr>
    <attr format="string" name="helperText">
    </attr>
    <attr format="boolean" name="helperTextEnabled">
    </attr>
    <attr format="reference" name="helperTextTextAppearance">
    </attr>
    <attr format="reference" name="hideMotionSpec">
    </attr>
    <attr format="boolean" name="hideOnContentScroll">
    </attr>
    <attr format="boolean" name="hideOnScroll">
    </attr>
    <attr format="boolean" name="hintAnimationEnabled">
    </attr>
    <attr format="boolean" name="hintEnabled">
    </attr>
    <attr format="reference" name="hintTextAppearance">
    </attr>
    <attr format="reference" name="homeAsUpIndicator">
    </attr>
    <attr format="reference" name="homeLayout">
    </attr>
    <attr format="dimension" name="hoveredFocusedTranslationZ">
    </attr>
    <attr format="reference" name="icon">
    </attr>
    <attr format="dimension" name="iconEndPadding">
    </attr>
    <attr name="iconGravity">
        <flag name="start" value="1"/>
        <flag name="textStart" value="2"/>
    </attr>
    <attr format="dimension" name="iconPadding">
    </attr>
    <attr format="dimension" name="iconSize">
    </attr>
    <attr format="dimension" name="iconStartPadding">
    </attr>
    <attr format="color" name="iconTint">
    </attr>
    <attr name="iconTintMode">
        <enum name="add" value="16"/>
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="boolean" name="iconifiedByDefault">
    </attr>
    <attr format="reference" name="imageButtonStyle">
    </attr>
    <attr format="reference" name="indeterminateProgressStyle">
    </attr>
    <attr format="string" name="initialActivityCount">
    </attr>
    <attr format="reference|color" name="insetForeground">
    </attr>
    <attr format="boolean" name="isLightTheme">
    </attr>
    <attr format="reference" name="itemBackground">
    </attr>
    <attr format="dimension" name="itemHorizontalPadding">
    </attr>
    <attr format="boolean" name="itemHorizontalTranslationEnabled">
    </attr>
    <attr format="dimension" name="itemIconPadding">
    </attr>
    <attr format="dimension" name="itemIconSize">
    </attr>
    <attr format="color" name="itemIconTint">
    </attr>
    <attr format="dimension" name="itemPadding">
    </attr>
    <attr format="dimension" name="itemSpacing">
    </attr>
    <attr format="reference" name="itemTextAppearance">
    </attr>
    <attr format="reference" name="itemTextAppearanceActive">
    </attr>
    <attr format="reference" name="itemTextAppearanceInactive">
    </attr>
    <attr format="color" name="itemTextColor">
    </attr>
    <attr format="reference" name="keylines">
    </attr>
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1"/>
        <enum name="labeled" value="1"/>
        <enum name="selected" value="0"/>
        <enum name="unlabeled" value="2"/>
    </attr>
    <attr format="dimension" name="lastBaselineToBottomHeight">
    </attr>
    <attr format="reference" name="layout">
    </attr>
    <attr format="string" name="layoutManager">
    </attr>
    <attr format="reference" name="layout_anchor">
    </attr>
    <attr name="layout_anchorGravity">
        <flag name="bottom" value="80"/>
        <flag name="center" value="17"/>
        <flag name="center_horizontal" value="1"/>
        <flag name="center_vertical" value="16"/>
        <flag name="clip_horizontal" value="8"/>
        <flag name="clip_vertical" value="128"/>
        <flag name="end" value="8388613"/>
        <flag name="fill" value="119"/>
        <flag name="fill_horizontal" value="7"/>
        <flag name="fill_vertical" value="112"/>
        <flag name="left" value="3"/>
        <flag name="right" value="5"/>
        <flag name="start" value="8388611"/>
        <flag name="top" value="48"/>
    </attr>
    <attr format="string" name="layout_behavior">
    </attr>
    <attr name="layout_collapseMode">
        <enum name="none" value="0"/>
        <enum name="parallax" value="2"/>
        <enum name="pin" value="1"/>
    </attr>
    <attr format="float" name="layout_collapseParallaxMultiplier">
    </attr>
    <attr name="layout_dodgeInsetEdges">
        <flag name="all" value="119"/>
        <flag name="bottom" value="80"/>
        <flag name="end" value="8388613"/>
        <flag name="left" value="3"/>
        <flag name="none" value="0"/>
        <flag name="right" value="5"/>
        <flag name="start" value="8388611"/>
        <flag name="top" value="48"/>
    </attr>
    <attr name="layout_insetEdge">
        <enum name="bottom" value="80"/>
        <enum name="end" value="8388613"/>
        <enum name="left" value="3"/>
        <enum name="none" value="0"/>
        <enum name="right" value="5"/>
        <enum name="start" value="8388611"/>
        <enum name="top" value="48"/>
    </attr>
    <attr format="integer" name="layout_keyline">
    </attr>
    <attr name="layout_scrollFlags">
        <flag name="enterAlways" value="4"/>
        <flag name="enterAlwaysCollapsed" value="8"/>
        <flag name="exitUntilCollapsed" value="2"/>
        <flag name="scroll" value="1"/>
        <flag name="snap" value="16"/>
        <flag name="snapMargins" value="32"/>
    </attr>
    <attr format="reference" name="layout_scrollInterpolator">
    </attr>
    <attr format="boolean" name="liftOnScroll">
    </attr>
    <attr format="dimension" name="lineHeight">
    </attr>
    <attr format="dimension" name="lineSpacing">
    </attr>
    <attr format="reference" name="listChoiceBackgroundIndicator">
    </attr>
    <attr format="reference" name="listDividerAlertDialog">
    </attr>
    <attr format="reference" name="listItemLayout">
    </attr>
    <attr format="reference" name="listLayout">
    </attr>
    <attr format="reference" name="listMenuViewStyle">
    </attr>
    <attr format="reference" name="listPopupWindowStyle">
    </attr>
    <attr format="dimension" name="listPreferredItemHeight">
    </attr>
    <attr format="dimension" name="listPreferredItemHeightLarge">
    </attr>
    <attr format="dimension" name="listPreferredItemHeightSmall">
    </attr>
    <attr format="dimension" name="listPreferredItemPaddingLeft">
    </attr>
    <attr format="dimension" name="listPreferredItemPaddingRight">
    </attr>
    <attr format="reference" name="logo">
    </attr>
    <attr format="string" name="logoDescription">
    </attr>
    <attr format="reference" name="materialButtonStyle">
    </attr>
    <attr format="reference" name="materialCardViewStyle">
    </attr>
    <attr format="dimension" name="maxActionInlineWidth">
    </attr>
    <attr format="dimension" name="maxButtonHeight">
    </attr>
    <attr format="dimension" name="maxImageSize">
    </attr>
    <attr format="boolean" name="measureWithLargestChild">
    </attr>
    <attr format="reference" name="menu">
    </attr>
    <attr format="reference" name="multiChoiceItemLayout">
    </attr>
    <attr format="string" name="navigationContentDescription">
    </attr>
    <attr format="reference" name="navigationIcon">
    </attr>
    <attr name="navigationMode">
        <enum name="listMode" value="1"/>
        <enum name="normal" value="0"/>
        <enum name="tabMode" value="2"/>
    </attr>
    <attr format="reference" name="navigationViewStyle">
    </attr>
    <attr name="numericModifiers">
        <flag name="ALT" value="2"/>
        <flag name="CTRL" value="4096"/>
        <flag name="FUNCTION" value="8"/>
        <flag name="META" value="65536"/>
        <flag name="SHIFT" value="1"/>
        <flag name="SYM" value="4"/>
    </attr>
    <attr format="boolean" name="overlapAnchor">
    </attr>
    <attr format="dimension" name="paddingBottomNoButtons">
    </attr>
    <attr format="dimension" name="paddingEnd">
    </attr>
    <attr format="dimension" name="paddingStart">
    </attr>
    <attr format="dimension" name="paddingTopNoTitle">
    </attr>
    <attr format="reference" name="panelBackground">
    </attr>
    <attr format="reference" name="panelMenuListTheme">
    </attr>
    <attr format="dimension" name="panelMenuListWidth">
    </attr>
    <attr format="string" name="passwordToggleContentDescription">
    </attr>
    <attr format="reference" name="passwordToggleDrawable">
    </attr>
    <attr format="boolean" name="passwordToggleEnabled">
    </attr>
    <attr format="color" name="passwordToggleTint">
    </attr>
    <attr name="passwordToggleTintMode">
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="reference" name="popupMenuStyle">
    </attr>
    <attr format="reference" name="popupTheme">
    </attr>
    <attr format="reference" name="popupWindowStyle">
    </attr>
    <attr format="boolean" name="preserveIconSpacing">
    </attr>
    <attr format="dimension" name="pressedTranslationZ">
    </attr>
    <attr format="dimension" name="progressBarPadding">
    </attr>
    <attr format="reference" name="progressBarStyle">
    </attr>
    <attr format="reference" name="queryBackground">
    </attr>
    <attr format="string" name="queryHint">
    </attr>
    <attr format="reference" name="radioButtonStyle">
    </attr>
    <attr format="reference" name="ratingBarStyle">
    </attr>
    <attr format="reference" name="ratingBarStyleIndicator">
    </attr>
    <attr format="reference" name="ratingBarStyleSmall">
    </attr>
    <attr format="boolean" name="reverseLayout">
    </attr>
    <attr format="color" name="rippleColor">
    </attr>
    <attr format="integer" name="scrimAnimationDuration">
    </attr>
    <attr format="reference|color" name="scrimBackground">
    </attr>
    <attr format="dimension" name="scrimVisibleHeightTrigger">
    </attr>
    <attr format="reference" name="searchHintIcon">
    </attr>
    <attr format="reference" name="searchIcon">
    </attr>
    <attr format="reference" name="searchViewStyle">
    </attr>
    <attr format="reference" name="seekBarStyle">
    </attr>
    <attr format="reference" name="selectableItemBackground">
    </attr>
    <attr format="reference" name="selectableItemBackgroundBorderless">
    </attr>
    <attr name="showAsAction">
        <flag name="always" value="2"/>
        <flag name="collapseActionView" value="8"/>
        <flag name="ifRoom" value="1"/>
        <flag name="never" value="0"/>
        <flag name="withText" value="4"/>
    </attr>
    <attr name="showDividers">
        <flag name="beginning" value="1"/>
        <flag name="end" value="4"/>
        <flag name="middle" value="2"/>
        <flag name="none" value="0"/>
    </attr>
    <attr format="reference" name="showMotionSpec">
    </attr>
    <attr format="boolean" name="showText">
    </attr>
    <attr format="boolean" name="showTitle">
    </attr>
    <attr format="reference" name="singleChoiceItemLayout">
    </attr>
    <attr format="boolean" name="singleLine">
    </attr>
    <attr format="boolean" name="singleSelection">
    </attr>
    <attr format="reference" name="snackbarButtonStyle">
    </attr>
    <attr format="reference" name="snackbarStyle">
    </attr>
    <attr format="integer" name="spanCount">
    </attr>
    <attr format="boolean" name="spinBars">
    </attr>
    <attr format="reference" name="spinnerDropDownItemStyle">
    </attr>
    <attr format="reference" name="spinnerStyle">
    </attr>
    <attr format="boolean" name="splitTrack">
    </attr>
    <attr format="reference" name="srcCompat">
    </attr>
    <attr format="boolean" name="stackFromEnd">
    </attr>
    <attr format="boolean" name="state_above_anchor">
    </attr>
    <attr format="boolean" name="state_collapsed">
    </attr>
    <attr format="boolean" name="state_collapsible">
    </attr>
    <attr format="boolean" name="state_liftable">
    </attr>
    <attr format="boolean" name="state_lifted">
    </attr>
    <attr format="reference|color" name="statusBarBackground">
    </attr>
    <attr format="color" name="statusBarScrim">
    </attr>
    <attr format="color" name="strokeColor">
    </attr>
    <attr format="dimension" name="strokeWidth">
    </attr>
    <attr format="reference" name="subMenuArrow">
    </attr>
    <attr format="reference" name="submitBackground">
    </attr>
    <attr format="string" name="subtitle">
    </attr>
    <attr format="reference" name="subtitleTextAppearance">
    </attr>
    <attr format="color" name="subtitleTextColor">
    </attr>
    <attr format="reference" name="subtitleTextStyle">
    </attr>
    <attr format="reference" name="suggestionRowLayout">
    </attr>
    <attr format="dimension" name="switchMinWidth">
    </attr>
    <attr format="dimension" name="switchPadding">
    </attr>
    <attr format="reference" name="switchStyle">
    </attr>
    <attr format="reference" name="switchTextAppearance">
    </attr>
    <attr format="reference" name="tabBackground">
    </attr>
    <attr format="dimension" name="tabContentStart">
    </attr>
    <attr name="tabGravity">
        <enum name="center" value="1"/>
        <enum name="fill" value="0"/>
    </attr>
    <attr format="color" name="tabIconTint">
    </attr>
    <attr name="tabIconTintMode">
        <enum name="add" value="16"/>
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="reference" name="tabIndicator">
    </attr>
    <attr format="integer" name="tabIndicatorAnimationDuration">
    </attr>
    <attr format="color" name="tabIndicatorColor">
    </attr>
    <attr format="boolean" name="tabIndicatorFullWidth">
    </attr>
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0"/>
        <enum name="center" value="1"/>
        <enum name="stretch" value="3"/>
        <enum name="top" value="2"/>
    </attr>
    <attr format="dimension" name="tabIndicatorHeight">
    </attr>
    <attr format="boolean" name="tabInlineLabel">
    </attr>
    <attr format="dimension" name="tabMaxWidth">
    </attr>
    <attr format="dimension" name="tabMinWidth">
    </attr>
    <attr name="tabMode">
        <enum name="fixed" value="1"/>
        <enum name="scrollable" value="0"/>
    </attr>
    <attr format="dimension" name="tabPadding">
    </attr>
    <attr format="dimension" name="tabPaddingBottom">
    </attr>
    <attr format="dimension" name="tabPaddingEnd">
    </attr>
    <attr format="dimension" name="tabPaddingStart">
    </attr>
    <attr format="dimension" name="tabPaddingTop">
    </attr>
    <attr format="color" name="tabRippleColor">
    </attr>
    <attr format="color" name="tabSelectedTextColor">
    </attr>
    <attr format="reference" name="tabStyle">
    </attr>
    <attr format="reference" name="tabTextAppearance">
    </attr>
    <attr format="color" name="tabTextColor">
    </attr>
    <attr format="boolean" name="tabUnboundedRipple">
    </attr>
    <attr format="reference|boolean" name="textAllCaps">
    </attr>
    <attr format="reference" name="textAppearanceBody1">
    </attr>
    <attr format="reference" name="textAppearanceBody2">
    </attr>
    <attr format="reference" name="textAppearanceButton">
    </attr>
    <attr format="reference" name="textAppearanceCaption">
    </attr>
    <attr format="reference" name="textAppearanceHeadline1">
    </attr>
    <attr format="reference" name="textAppearanceHeadline2">
    </attr>
    <attr format="reference" name="textAppearanceHeadline3">
    </attr>
    <attr format="reference" name="textAppearanceHeadline4">
    </attr>
    <attr format="reference" name="textAppearanceHeadline5">
    </attr>
    <attr format="reference" name="textAppearanceHeadline6">
    </attr>
    <attr format="reference" name="textAppearanceLargePopupMenu">
    </attr>
    <attr format="reference" name="textAppearanceListItem">
    </attr>
    <attr format="reference" name="textAppearanceListItemSecondary">
    </attr>
    <attr format="reference" name="textAppearanceListItemSmall">
    </attr>
    <attr format="reference" name="textAppearanceOverline">
    </attr>
    <attr format="reference" name="textAppearancePopupMenuHeader">
    </attr>
    <attr format="reference" name="textAppearanceSearchResultSubtitle">
    </attr>
    <attr format="reference" name="textAppearanceSearchResultTitle">
    </attr>
    <attr format="reference" name="textAppearanceSmallPopupMenu">
    </attr>
    <attr format="reference" name="textAppearanceSubtitle1">
    </attr>
    <attr format="reference" name="textAppearanceSubtitle2">
    </attr>
    <attr format="reference|color" name="textColorAlertDialogListItem">
    </attr>
    <attr format="reference|color" name="textColorSearchUrl">
    </attr>
    <attr format="dimension" name="textEndPadding">
    </attr>
    <attr format="reference" name="textInputStyle">
    </attr>
    <attr format="dimension" name="textStartPadding">
    </attr>
    <attr format="reference" name="theme">
    </attr>
    <attr format="dimension" name="thickness">
    </attr>
    <attr format="dimension" name="thumbTextPadding">
    </attr>
    <attr format="color" name="thumbTint">
    </attr>
    <attr name="thumbTintMode">
        <enum name="add" value="16"/>
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="reference" name="tickMark">
    </attr>
    <attr format="color" name="tickMarkTint">
    </attr>
    <attr name="tickMarkTintMode">
        <enum name="add" value="16"/>
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="color" name="tint">
    </attr>
    <attr name="tintMode">
        <enum name="add" value="16"/>
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="string" name="title">
    </attr>
    <attr format="boolean" name="titleEnabled">
    </attr>
    <attr format="dimension" name="titleMargin">
    </attr>
    <attr format="dimension" name="titleMarginBottom">
    </attr>
    <attr format="dimension" name="titleMarginEnd">
    </attr>
    <attr format="dimension" name="titleMarginStart">
    </attr>
    <attr format="dimension" name="titleMarginTop">
    </attr>
    <attr format="dimension" name="titleMargins">
    </attr>
    <attr format="reference" name="titleTextAppearance">
    </attr>
    <attr format="color" name="titleTextColor">
    </attr>
    <attr format="reference" name="titleTextStyle">
    </attr>
    <attr format="reference" name="toolbarId">
    </attr>
    <attr format="reference" name="toolbarNavigationButtonStyle">
    </attr>
    <attr format="reference" name="toolbarStyle">
    </attr>
    <attr format="reference|color" name="tooltipForegroundColor">
    </attr>
    <attr format="reference" name="tooltipFrameBackground">
    </attr>
    <attr format="string" name="tooltipText">
    </attr>
    <attr format="reference" name="track">
    </attr>
    <attr format="color" name="trackTint">
    </attr>
    <attr name="trackTintMode">
        <enum name="add" value="16"/>
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="integer" name="ttcIndex">
    </attr>
    <attr format="boolean" name="useCompatPadding">
    </attr>
    <attr format="string" name="viewInflaterClass">
    </attr>
    <attr format="reference" name="voiceIcon">
    </attr>
    <attr format="boolean" name="windowActionBar">
    </attr>
    <attr format="boolean" name="windowActionBarOverlay">
    </attr>
    <attr format="boolean" name="windowActionModeOverlay">
    </attr>
    <attr format="dimension|fraction" name="windowFixedHeightMajor">
    </attr>
    <attr format="dimension|fraction" name="windowFixedHeightMinor">
    </attr>
    <attr format="dimension|fraction" name="windowFixedWidthMajor">
    </attr>
    <attr format="dimension|fraction" name="windowFixedWidthMinor">
    </attr>
    <attr format="dimension|fraction" name="windowMinWidthMajor">
    </attr>
    <attr format="dimension|fraction" name="windowMinWidthMinor">
    </attr>
    <attr format="boolean" name="windowNoTitle">
    </attr>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_allow_stacked_button_bar">false</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <bool name="mtrl_btn_textappearance_all_caps">true</bool>
    <color name="Gray">#808080</color>
    <color name="Gray_divider">#141921</color>
    <color name="LightGray">#d0d0d0</color>
    <color name="Login_Split">#eeeeee</color>
    <color name="SubTitleBack">#1a1c26</color>
    <color name="SubTitleBackLine">#242836</color>
    <color name="abc_input_method_navigation_guard">@android:color/black</color>
    <color name="abc_search_url_text_normal">#7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="back_color">#0a0d20</color>
    <color name="back_color1">#3a3f5d</color>
    <color name="back_color2">#303650</color>
    <color name="back_color3">#232637</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="black">#000000</color>
    <color name="blue_default">#1590fa</color>
    <color name="blue_light">#79bdfa</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color>
    <color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="cardview_dark_background">#ff424242</color>
    <color name="cardview_light_background">#ffffffff</color>
    <color name="cardview_shadow_end_color">#03000000</color>
    <color name="cardview_shadow_start_color">#37000000</color>
    <color name="colorAccent">#ff4081</color>
    <color name="colorPrimary">#2a3746</color>
    <color name="colorPrimaryDark">#303f9f</color>
    <color name="color_sync0">#8f7bdd</color>
    <color name="color_sync1">#c656a1</color>
    <color name="color_sync2">#636363</color>
    <color name="color_sync3">#e0376a</color>
    <color name="color_sync4">#f86591</color>
    <color name="color_sync5">#e03569</color>
    <color name="design_bottom_navigation_shadow_color">#14000000</color>
    <color name="design_default_color_primary">#3f51b5</color>
    <color name="design_default_color_primary_dark">#303f9f</color>
    <color name="design_fab_shadow_end_color">@android:color/transparent</color>
    <color name="design_fab_shadow_mid_color">#14000000</color>
    <color name="design_fab_shadow_start_color">#44000000</color>
    <color name="design_fab_stroke_end_inner_color">#0a000000</color>
    <color name="design_fab_stroke_end_outer_color">#0f000000</color>
    <color name="design_fab_stroke_top_inner_color">#1affffff</color>
    <color name="design_fab_stroke_top_outer_color">#2effffff</color>
    <color name="design_snackbar_background_color">#323232</color>
    <color name="dim_foreground_disabled_material_dark">#80bebebe</color>
    <color name="dim_foreground_disabled_material_light">#80323232</color>
    <color name="dim_foreground_material_dark">#ffbebebe</color>
    <color name="dim_foreground_material_light">#ff323232</color>
    <color name="divider_normal_color">#e8e8e8</color>
    <color name="error_color_material_dark">#ff7043</color>
    <color name="error_color_material_light">#ff5722</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="greenlight_alpha_color">#40c0ffc0</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="line_color">#2c2f40</color>
    <color name="line_color1">#5c5d6b</color>
    <color name="material_blue_grey_800">#ff37474f</color>
    <color name="material_blue_grey_900">#ff263238</color>
    <color name="material_blue_grey_950">#ff21272b</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff009688</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_300">#ffe0e0e0</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="menu_item_back_color">#2a4057</color>
    <color name="menu_item_divider_color">#5095bf</color>
    <color name="menu_item_sel_color">#65737c</color>
    <color name="mtrl_btn_bg_color_disabled">#1f000000</color>
    <color name="mtrl_btn_text_color_disabled">#61000000</color>
    <color name="mtrl_btn_transparent_bg_color">#00ffffff</color>
    <color name="mtrl_scrim_color">#52000000</color>
    <color name="mtrl_textinput_default_box_stroke_color">#6b000000</color>
    <color name="mtrl_textinput_disabled_color">#1f000000</color>
    <color name="mtrl_textinput_filled_box_default_background_color">#0a000000</color>
    <color name="mtrl_textinput_hovered_box_stroke_color">#de000000</color>
    <color name="normal_text_color">#ffffff</color>
    <color name="normal_text_color2">#e2e2e2</color>
    <color name="normal_text_color3">#878787</color>
    <color name="normal_text_color4">#303030</color>
    <color name="normal_text_color5">#9d9d9d</color>
    <color name="normal_text_color6">#9d9d9d</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="notification_material_background_media_default_color">#ff424242</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="red">#ff0000</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6ffffff</color>
    <color name="white">#ffffff</color>
    <color name="white_alpha_color">#20ffffff</color>
    <dimen name="abc_action_bar_content_inset_material">16dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen>
    <dimen name="abc_action_bar_default_height_material">56dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">0dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">0dp</dimen>
    <dimen name="abc_action_bar_elevation_material">4dp</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48dp</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen>
    <dimen name="abc_action_button_min_height_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36dp</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48dp</dimen>
    <dimen name="abc_alert_dialog_button_dimen">48dp</dimen>
    <dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen>
    <dimen name="abc_button_inset_vertical_material">6dp</dimen>
    <dimen name="abc_button_padding_horizontal_material">8dp</dimen>
    <dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen>
    <dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen>
    <dimen name="abc_config_prefDialogWidth">320dp</dimen>
    <dimen name="abc_control_corner_material">2dp</dimen>
    <dimen name="abc_control_inset_material">4dp</dimen>
    <dimen name="abc_control_padding_material">4dp</dimen>
    <dimen name="abc_dialog_corner_radius_material">2dp</dimen>
    <dimen name="abc_dialog_fixed_height_major">80%</dimen>
    <dimen name="abc_dialog_fixed_height_minor">100%</dimen>
    <dimen name="abc_dialog_fixed_width_major">320dp</dimen>
    <dimen name="abc_dialog_fixed_width_minor">320dp</dimen>
    <dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen>
    <dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen>
    <dimen name="abc_dialog_min_width_major">65%</dimen>
    <dimen name="abc_dialog_min_width_minor">95%</dimen>
    <dimen name="abc_dialog_padding_material">24dp</dimen>
    <dimen name="abc_dialog_padding_top_material">18dp</dimen>
    <dimen name="abc_dialog_title_divider_material">8dp</dimen>
    <dimen name="abc_disabled_alpha_material_dark">0.3</dimen>
    <dimen name="abc_disabled_alpha_material_light">0.26</dimen>
    <dimen name="abc_dropdownitem_icon_width">32dp</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8dp</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8dp</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7dp</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen>
    <dimen name="abc_edit_text_inset_top_material">10dp</dimen>
    <dimen name="abc_floating_window_z">16dp</dimen>
    <dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen>
    <dimen name="abc_panel_menu_list_width">296dp</dimen>
    <dimen name="abc_progress_bar_height_material">4dp</dimen>
    <dimen name="abc_search_view_preferred_height">48dp</dimen>
    <dimen name="abc_search_view_preferred_width">320dp</dimen>
    <dimen name="abc_seekbar_track_background_height_material">2dp</dimen>
    <dimen name="abc_seekbar_track_progress_height_material">2dp</dimen>
    <dimen name="abc_select_dialog_padding_start_material">20dp</dimen>
    <dimen name="abc_switch_padding">0px</dimen>
    <dimen name="abc_text_size_body_1_material">14sp</dimen>
    <dimen name="abc_text_size_body_2_material">14sp</dimen>
    <dimen name="abc_text_size_button_material">14sp</dimen>
    <dimen name="abc_text_size_caption_material">12sp</dimen>
    <dimen name="abc_text_size_display_1_material">34sp</dimen>
    <dimen name="abc_text_size_display_2_material">45sp</dimen>
    <dimen name="abc_text_size_display_3_material">56sp</dimen>
    <dimen name="abc_text_size_display_4_material">112sp</dimen>
    <dimen name="abc_text_size_headline_material">24sp</dimen>
    <dimen name="abc_text_size_large_material">22sp</dimen>
    <dimen name="abc_text_size_medium_material">18sp</dimen>
    <dimen name="abc_text_size_menu_header_material">14sp</dimen>
    <dimen name="abc_text_size_menu_material">16sp</dimen>
    <dimen name="abc_text_size_small_material">14sp</dimen>
    <dimen name="abc_text_size_subhead_material">16sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material">20sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="appbar_padding">16dp</dimen>
    <dimen name="appbar_padding_top">8dp</dimen>
    <dimen name="cardview_compat_inset_shadow">1dp</dimen>
    <dimen name="cardview_default_elevation">2dp</dimen>
    <dimen name="cardview_default_radius">2dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <dimen name="design_appbar_elevation">4dp</dimen>
    <dimen name="design_bottom_navigation_active_item_max_width">168dp</dimen>
    <dimen name="design_bottom_navigation_active_item_min_width">96dp</dimen>
    <dimen name="design_bottom_navigation_active_text_size">14sp</dimen>
    <dimen name="design_bottom_navigation_elevation">8dp</dimen>
    <dimen name="design_bottom_navigation_height">56dp</dimen>
    <dimen name="design_bottom_navigation_icon_size">24dp</dimen>
    <dimen name="design_bottom_navigation_item_max_width">96dp</dimen>
    <dimen name="design_bottom_navigation_item_min_width">56dp</dimen>
    <dimen name="design_bottom_navigation_margin">8dp</dimen>
    <dimen name="design_bottom_navigation_shadow_height">1dp</dimen>
    <dimen name="design_bottom_navigation_text_size">12sp</dimen>
    <dimen name="design_bottom_sheet_modal_elevation">16dp</dimen>
    <dimen name="design_bottom_sheet_peek_height_min">64dp</dimen>
    <dimen name="design_fab_border_width">0.5dp</dimen>
    <dimen name="design_fab_elevation">6dp</dimen>
    <dimen name="design_fab_image_size">24dp</dimen>
    <dimen name="design_fab_size_mini">40dp</dimen>
    <dimen name="design_fab_size_normal">56dp</dimen>
    <dimen name="design_fab_translation_z_hovered_focused">6dp</dimen>
    <dimen name="design_fab_translation_z_pressed">6dp</dimen>
    <dimen name="design_navigation_elevation">16dp</dimen>
    <dimen name="design_navigation_icon_padding">32dp</dimen>
    <dimen name="design_navigation_icon_size">24dp</dimen>
    <dimen name="design_navigation_item_horizontal_padding">16dp</dimen>
    <dimen name="design_navigation_item_icon_padding">32dp</dimen>
    <dimen name="design_navigation_max_width">280dp</dimen>
    <dimen name="design_navigation_padding_bottom">8dp</dimen>
    <dimen name="design_navigation_separator_vertical_padding">8dp</dimen>
    <dimen name="design_snackbar_action_inline_max_width">128dp</dimen>
    <dimen name="design_snackbar_background_corner_radius">0dp</dimen>
    <dimen name="design_snackbar_elevation">6dp</dimen>
    <dimen name="design_snackbar_extra_spacing_horizontal">0dp</dimen>
    <dimen name="design_snackbar_max_width">-1px</dimen>
    <dimen name="design_snackbar_min_width">-1px</dimen>
    <dimen name="design_snackbar_padding_horizontal">12dp</dimen>
    <dimen name="design_snackbar_padding_vertical">14dp</dimen>
    <dimen name="design_snackbar_padding_vertical_2lines">24dp</dimen>
    <dimen name="design_snackbar_text_size">14sp</dimen>
    <dimen name="design_tab_max_width">264dp</dimen>
    <dimen name="design_tab_scrollable_min_width">72dp</dimen>
    <dimen name="design_tab_text_size">14sp</dimen>
    <dimen name="design_tab_text_size_2line">12sp</dimen>
    <dimen name="design_textinput_caption_translate_y">5dp</dimen>
    <dimen name="disabled_alpha_material_dark">0.3</dimen>
    <dimen name="disabled_alpha_material_light">0.26</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="fastscroll_default_thickness">8dp</dimen>
    <dimen name="fastscroll_margin">0dp</dimen>
    <dimen name="fastscroll_minimum_range">50dp</dimen>
    <dimen name="highlight_alpha_material_colored">0.26</dimen>
    <dimen name="highlight_alpha_material_dark">0.2</dimen>
    <dimen name="highlight_alpha_material_light">0.12</dimen>
    <dimen name="hint_alpha_material_dark">0.5</dimen>
    <dimen name="hint_alpha_material_light">0.38</dimen>
    <dimen name="hint_pressed_alpha_material_dark">0.7</dimen>
    <dimen name="hint_pressed_alpha_material_light">0.54</dimen>
    <dimen name="item_touch_helper_max_drag_scroll_per_frame">20dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_max_velocity">800dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_velocity">120dp</dimen>
    <dimen name="mtrl_bottomappbar_fabOffsetEndMode">60dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_margin">5dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_vertical_offset">0dp</dimen>
    <dimen name="mtrl_bottomappbar_height">56dp</dimen>
    <dimen name="mtrl_btn_corner_radius">4dp</dimen>
    <dimen name="mtrl_btn_dialog_btn_min_width">64dp</dimen>
    <dimen name="mtrl_btn_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_btn_disabled_z">0dp</dimen>
    <dimen name="mtrl_btn_elevation">2dp</dimen>
    <dimen name="mtrl_btn_focused_z">2dp</dimen>
    <dimen name="mtrl_btn_hovered_z">2dp</dimen>
    <dimen name="mtrl_btn_icon_btn_padding_left">12dp</dimen>
    <dimen name="mtrl_btn_icon_padding">8dp</dimen>
    <dimen name="mtrl_btn_inset">6dp</dimen>
    <dimen name="mtrl_btn_letter_spacing">0.07</dimen>
    <dimen name="mtrl_btn_padding_bottom">4dp</dimen>
    <dimen name="mtrl_btn_padding_left">16dp</dimen>
    <dimen name="mtrl_btn_padding_right">16dp</dimen>
    <dimen name="mtrl_btn_padding_top">4dp</dimen>
    <dimen name="mtrl_btn_pressed_z">6dp</dimen>
    <dimen name="mtrl_btn_stroke_size">1dp</dimen>
    <dimen name="mtrl_btn_text_btn_icon_padding">4dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_left">8dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_right">8dp</dimen>
    <dimen name="mtrl_btn_text_size">14sp</dimen>
    <dimen name="mtrl_btn_z">0dp</dimen>
    <dimen name="mtrl_card_elevation">1dp</dimen>
    <dimen name="mtrl_card_spacing">8dp</dimen>
    <dimen name="mtrl_chip_pressed_translation_z">3dp</dimen>
    <dimen name="mtrl_chip_text_size">14sp</dimen>
    <dimen name="mtrl_fab_elevation">6dp</dimen>
    <dimen name="mtrl_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_fab_translation_z_pressed">6dp</dimen>
    <dimen name="mtrl_navigation_elevation">0dp</dimen>
    <dimen name="mtrl_navigation_item_horizontal_padding">22dp</dimen>
    <dimen name="mtrl_navigation_item_icon_padding">14dp</dimen>
    <dimen name="mtrl_snackbar_background_corner_radius">4dp</dimen>
    <dimen name="mtrl_snackbar_margin">8dp</dimen>
    <dimen name="mtrl_textinput_box_bottom_offset">3dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_medium">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_small">0dp</dimen>
    <dimen name="mtrl_textinput_box_label_cutout_padding">4dp</dimen>
    <dimen name="mtrl_textinput_box_padding_end">12dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_default">1dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_focused">2dp</dimen>
    <dimen name="mtrl_textinput_outline_box_expanded_padding">16dp</dimen>
    <dimen name="mtrl_toolbar_default_height">56dp</dimen>
    <dimen name="nav_header_height">200dp</dimen>
    <dimen name="nav_header_vertical_spacing">16dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">4dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="subtitle_corner_radius">2dp</dimen>
    <dimen name="subtitle_outline_width">2dp</dimen>
    <dimen name="subtitle_shadow_offset">2dp</dimen>
    <dimen name="subtitle_shadow_radius">2dp</dimen>
    <dimen name="tooltip_corner_radius">2dp</dimen>
    <dimen name="tooltip_horizontal_padding">16dp</dimen>
    <dimen name="tooltip_margin">8dp</dimen>
    <dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen>
    <dimen name="tooltip_precise_anchor_threshold">96dp</dimen>
    <dimen name="tooltip_vertical_padding">6.5dp</dimen>
    <dimen name="tooltip_y_offset_non_touch">0dp</dimen>
    <dimen name="tooltip_y_offset_touch">16dp</dimen>
    <drawable name="ic_menu_camera">@android:drawable/ic_menu_camera</drawable>
    <drawable name="ic_menu_gallery">@android:drawable/ic_menu_gallery</drawable>
    <drawable name="ic_menu_manage">@android:drawable/ic_menu_manage</drawable>
    <drawable name="ic_menu_send">@android:drawable/ic_menu_send</drawable>
    <drawable name="ic_menu_share">@android:drawable/ic_menu_share</drawable>
    <drawable name="ic_menu_slideshow">@android:drawable/ic_menu_slideshow</drawable>
    <drawable name="notification_template_icon_bg">#3333b5e5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="action_bar_activity_content" type="id"/>
    <item name="action_bar_spinner" type="id"/>
    <item name="action_menu_divider" type="id"/>
    <item name="action_menu_presenter" type="id"/>
    <item name="ghost_view" type="id"/>
    <item name="home" type="id"/>
    <item name="item_touch_helper_previous_elevation" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="mtrl_child_content_container" type="id"/>
    <item name="mtrl_internal_children_alpha_tag" type="id"/>
    <item name="parent_matrix" type="id"/>
    <item name="progress_circular" type="id"/>
    <item name="progress_horizontal" type="id"/>
    <item name="save_image_matrix" type="id"/>
    <item name="save_non_transition_alpha" type="id"/>
    <item name="save_scale_type" type="id"/>
    <item name="snackbar_action" type="id"/>
    <item name="snackbar_text" type="id"/>
    <item name="split_action_bar" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="textinput_counter" type="id"/>
    <item name="textinput_error" type="id"/>
    <item name="textinput_helper_text" type="id"/>
    <item name="title" type="id"/>
    <item name="transition_current_scene" type="id"/>
    <item name="transition_layout_save" type="id"/>
    <item name="transition_position" type="id"/>
    <item name="transition_scene_layoutid_cache" type="id"/>
    <item name="transition_transform" type="id"/>
    <item name="up" type="id"/>
    <item name="view_offset_helper" type="id"/>
    <integer name="abc_config_activityDefaultDur">220</integer>
    <integer name="abc_config_activityShortDur">150</integer>
    <integer name="app_bar_elevation_anim_duration">150</integer>
    <integer name="bottom_sheet_slide_duration">150</integer>
    <integer name="cancel_button_image_alpha">127</integer>
    <integer name="config_tooltipAnimTime">150</integer>
    <integer name="design_snackbar_text_max_lines">2</integer>
    <integer name="design_tab_indicator_anim_duration_ms">300</integer>
    <integer name="hide_password_duration">320</integer>
    <integer name="mtrl_btn_anim_delay_ms">100</integer>
    <integer name="mtrl_btn_anim_duration_ms">100</integer>
    <integer name="mtrl_chip_anim_duration">100</integer>
    <integer name="mtrl_tab_indicator_anim_duration_ms">250</integer>
    <integer name="show_password_duration">200</integer>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_font_family_body_1_material">sans-serif</string>
    <string name="abc_font_family_body_2_material">sans-serif-medium</string>
    <string name="abc_font_family_button_material">sans-serif-medium</string>
    <string name="abc_font_family_caption_material">sans-serif</string>
    <string name="abc_font_family_display_1_material">sans-serif</string>
    <string name="abc_font_family_display_2_material">sans-serif</string>
    <string name="abc_font_family_display_3_material">sans-serif</string>
    <string name="abc_font_family_display_4_material">sans-serif-light</string>
    <string name="abc_font_family_headline_material">sans-serif</string>
    <string name="abc_font_family_menu_material">sans-serif</string>
    <string name="abc_font_family_subhead_material">sans-serif</string>
    <string name="abc_font_family_title_material">sans-serif-medium</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="action_settings">Settings</string>
    <string name="app_name">FAKER</string>
    <string name="appbar_scrolling_view_behavior">android.support.design.widget.AppBarLayout$ScrollingViewBehavior</string>
    <string name="blacklist_request_permissions">Request permissions</string>
    <string name="bottom_sheet_behavior">android.support.design.widget.BottomSheetBehavior</string>
    <string name="cancel">취소</string>
    <string name="character_counter_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d / %2$d</string>
    <string name="empty_data">등록된 정보없음</string>
    <string name="fab_transformation_scrim_behavior">android.support.design.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">android.support.design.transformation.FabTransformationSheetBehavior</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="hide_bottom_view_on_scroll_behavior">android.support.design.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="noCallHistory">콜/SMS이력 없음</string>
    <string name="noData">-</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="permissions_required">퍼미션을 모두 허용해주셔야 이 앱을 이용하실수 있습니다.</string>
    <string name="phonesearch_item">전화번호 검색</string>
    <string name="popup_position">팝업창 위치</string>
    <string name="question_item">문의사항</string>
    <string name="report_item">공지사항</string>
    <string name="search_hint">전화번호를 입력하세요</string>
    <string name="search_menu_title">Search</string>
    <string name="service_fail">데이터 신호 약함</string>
    <string name="setting">설정</string>
    <string name="setting_IPlabel">IP주소</string>
    <string name="setting_Portlabel">Port주소</string>
    <string name="setting_title">서버설정</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="tab_txt_name">FAKER</string>
    <string name="tab_txt_number">전화번호: 010-2344-4517</string>
    <string name="text_cancel">취소하기</string>
    <string name="text_ok">확인</string>
    <string name="title_phoneNumber">전화번호검색</string>
    <string name="today_call_request">오늘 전화문의 보기</string>
    <string name="user_id_hint">아이디</string>
    <string name="user_login">로그인</string>
    <string name="user_login_title">로그인정보</string>
    <string name="user_pass_hint">패스워드</string>
    <string name="wait">잠시만 기다려주세요...</string>
    <style name="AlertDialog.AppCompat" parent="@style/Base.AlertDialog.AppCompat">
    </style>
    <style name="AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat.Light">
    </style>
    <style name="Animation.AppCompat.Dialog" parent="@style/Base.Animation.AppCompat.Dialog">
    </style>
    <style name="Animation.AppCompat.DropDownUp" parent="@style/Base.Animation.AppCompat.DropDownUp">
    </style>
    <style name="Animation.AppCompat.Tooltip" parent="@style/Base.Animation.AppCompat.Tooltip">
    </style>
    <style name="Animation.Design.BottomSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
    </style>
    <style name="AppTheme" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
    </style>
    <style name="AppTheme.AppBarOverlay" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="AppTheme.NoActionBar" parent="@style/AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="AppTheme.PopupOverlay" parent="@style/ThemeOverlay.AppCompat.Light">
    </style>
    <style name="Base.AlertDialog.AppCompat" parent="@android:style/Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat">
    </style>
    <style name="Base.Animation.AppCompat.Dialog" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.CardView" parent="@android:style/Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">false</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlight</item>
        <item name="android:textColorHint">?android:attr/textColorHint</item>
        <item name="android:textColorLink">?android:attr/textColorLink</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">0x0</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorHint">?android:attr/textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">18sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textColor">?attr/actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="@style/TextAppearance.AppCompat.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?attr/colorAccent</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@style/TextAppearance.AppCompat.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/TextAppearance.AppCompat.Menu">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@android:style/TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V7.Theme.AppCompat.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="@style/Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents">
    </style>
    <style name="Base.Theme.MaterialComponents.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Dialog">
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="@style/Theme.MaterialComponents">
    </style>
    <style name="Base.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Theme.MaterialComponents.Light">
    </style>
    <style name="Base.ThemeOverlay.AppCompat" parent="@style/Platform.ThemeOverlay.AppCompat">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V7.ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog">
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert">
    </style>
    <style name="Base.V14.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge" parent="@style/Platform.MaterialComponents">
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="@style/Platform.MaterialComponents.Dialog">
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge" parent="@style/Platform.MaterialComponents.Light">
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="@style/Platform.MaterialComponents.Light.Dialog">
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" parent="@style/ThemeOverlay.AppCompat.Dialog">
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="@style/Platform.AppCompat">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="viewInflaterClass">android.support.v7.app.AppCompatViewInflater</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">0x20</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="@style/Platform.AppCompat.Light">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">true</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="viewInflaterClass">android.support.v7.app.AppCompatViewInflater</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">0x20</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">0x20</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.AutoCompleteTextView">
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="@android:style/Widget.EditText">
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="@android:style/Widget">
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="buttonGravity">top</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="titleMargin">4dp</item>
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="android:gravity">0x10</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="background">@null</item>
        <item name="backgroundSplit">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="dividerPadding">8dp</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:ellipsize">4</item>
        <item name="android:maxWidth">180dp</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:gravity">0x1</item>
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:minWidth">80dp</item>
        <item name="android:layout_weight">1</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="@style/RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:gravity">0x11</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:scaleType">5</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">0x11</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="dividerPadding">6dp</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.V7.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:gravity">0x11</item>
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:minWidth">88dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
        <item name="android:background">@drawable/abc_btn_colored_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:button">?android:attr/listChoiceIndicatorMultiple</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.CompoundButton.RadioButton">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:button">?android:attr/listChoiceIndicatorSingle</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="@android:style/Widget.CompoundButton">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="drawableSize">24dp</item>
        <item name="gapBetweenBars">3dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="arrowHeadLength">8dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:gravity">0x10</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="@style/Base.V7.Widget.AppCompat.EditText">
    </style>
    <style name="Base.Widget.AppCompat.ImageButton" parent="@android:style/Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="@android:style/Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownWidth">-2</item>
        <item name="android:dropDownHorizontalOffset">0dp</item>
        <item name="android:dropDownVerticalOffset">0dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.ListView">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="@android:style/Widget.ListView.Menu">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:divider">?attr/dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="@android:style/Widget.PopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Holo.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Holo.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.RatingBar">
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="@android:style/Widget.RatingBar">
        <item name="android:maxHeight">36dp</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:thumb">@null</item>
        <item name="android:isIndicator">true</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="@android:style/Widget.RatingBar">
        <item name="android:maxHeight">16dp</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16dp</item>
        <item name="android:thumb">@null</item>
        <item name="android:isIndicator">true</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="@android:style/Widget">
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView">
        <item name="defaultQueryHint">@string/abc_search_hint</item>
        <item name="queryBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="submitBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="@android:style/Widget">
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:focusable">true</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="@style/Platform.Widget.AppCompat.Spinner">
        <item name="android:gravity">0x800013</item>
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:clickable">true</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownWidth">-2</item>
        <item name="android:dropDownHorizontalOffset">0dp</item>
        <item name="android:dropDownVerticalOffset">0dp</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="@style/Base.V7.Widget.AppCompat.Toolbar">
    </style>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:scaleType">5</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.Design.TabLayout" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="tabIconTint">@null</item>
        <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
        <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?attr/colorAccent</item>
        <item name="tabIndicatorGravity">bottom</item>
        <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
        <item name="tabPaddingEnd">12dp</item>
        <item name="tabPaddingStart">12dp</item>
        <item name="tabRippleColor">?attr/colorControlHighlight</item>
        <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
        <item name="tabUnboundedRipple">false</item>
    </style>
    <style name="Base.Widget.MaterialComponents.Chip" parent="@android:style/Widget">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:text">@null</item>
        <item name="android:checkable">false</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
        <item name="checkedIconVisible">true</item>
        <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
        <item name="chipCornerRadius">16dp</item>
        <item name="chipEndPadding">6dp</item>
        <item name="chipIcon">@null</item>
        <item name="chipIconSize">24dp</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipStartPadding">4dp</item>
        <item name="chipStrokeColor">#00000000</item>
        <item name="chipStrokeWidth">0dp</item>
        <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>
        <item name="closeIconEndPadding">2dp</item>
        <item name="closeIconSize">18dp</item>
        <item name="closeIconStartPadding">2dp</item>
        <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>
        <item name="closeIconVisible">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="rippleColor">@color/mtrl_chip_ripple_color</item>
        <item name="textEndPadding">6dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="@style/Widget.AppCompat.EditText">
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingRight">12dp</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout" parent="@style/Widget.Design.TextInputLayout">
        <item name="boxBackgroundColor">@null</item>
        <item name="boxBackgroundMode">outline</item>
        <item name="boxCollapsedPaddingTop">0dp</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusBottomStart">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusTopEnd">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusTopStart">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxStrokeColor">?attr/colorControlActivated</item>
    </style>
    <style name="CardView" parent="@style/Base.CardView">
    </style>
    <style name="CardView.Dark" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="Platform.AppCompat" parent="@android:style/Theme.Holo">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="android:actionModeCutDrawable">?attr/actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?attr/actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?attr/actionModePasteDrawable</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:actionModeSelectAllDrawable">?attr/actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="@android:style/Theme.Holo.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="android:actionModeCutDrawable">?attr/actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?attr/actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?attr/actionModePasteDrawable</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:actionModeSelectAllDrawable">?attr/actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="Platform.MaterialComponents" parent="@style/Theme.AppCompat">
    </style>
    <style name="Platform.MaterialComponents.Dialog" parent="@style/Theme.AppCompat.Dialog">
    </style>
    <style name="Platform.MaterialComponents.Light" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Platform.MaterialComponents.Light.Dialog" parent="@style/Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat" parent="">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="@android:style/Widget.Holo.Spinner">
    </style>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="@style/Base.DialogWindowTitle.AppCompat">
        <item name="android:textAlignment">5</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="@android:style/Widget">
        <item name="android:layout_gravity">0x800013</item>
        <item name="android:paddingEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="@android:style/Widget">
        <item name="android:layout_marginEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="@android:style/Widget">
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="@android:style/Widget">
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="@android:style/Widget">
        <item name="android:textAlignment">6</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="@android:style/Widget">
        <item name="android:layout_marginStart">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="@android:style/Widget">
        <item name="android:textAlignment">5</item>
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="@android:style/Widget">
        <item name="android:textAlignment">5</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="@android:style/Widget">
        <item name="android:paddingStart">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingEnd">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="@android:style/Widget">
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="@android:style/Widget">
        <item name="android:layout_toStartOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="@android:style/Widget">
        <item name="android:layout_alignParentEnd">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="@style/Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toStartOf">@android:id/icon2</item>
        <item name="android:layout_toEndOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="@android:style/Widget">
        <item name="android:layout_marginStart">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="@android:style/Widget">
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingStart">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="@style/Base.TextAppearance.AppCompat">
    </style>
    <style name="TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat.Body1">
    </style>
    <style name="TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat.Body2">
    </style>
    <style name="TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat.Button">
    </style>
    <style name="TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat.Display1">
    </style>
    <style name="TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat.Display2">
    </style>
    <style name="TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat.Display3">
    </style>
    <style name="TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat.Display4">
    </style>
    <style name="TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat.Headline">
    </style>
    <style name="TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat.Large">
    </style>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="@style/TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="@style/TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Large">
    </style>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Small">
    </style>
    <style name="TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat.Medium">
    </style>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat.Menu">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat.Small">
    </style>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat.Subhead">
    </style>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat.Title">
    </style>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Tooltip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Colored">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="@style/Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="@style/Base.TextAppearance.AppCompat.Widget.Switch">
    </style>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem">
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent">
    </style>
    <style name="TextAppearance.Compat.Notification.Info" parent="@style/TextAppearance.Compat.Notification">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media" parent="@style/TextAppearance.Compat.Notification.Info">
    </style>
    <style name="TextAppearance.Compat.Notification.Line2" parent="@style/TextAppearance.Compat.Notification.Info">
    </style>
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="@style/TextAppearance.Compat.Notification.Info.Media">
    </style>
    <style name="TextAppearance.Compat.Notification.Media" parent="@style/TextAppearance.Compat.Notification">
    </style>
    <style name="TextAppearance.Compat.Notification.Time" parent="@style/TextAppearance.Compat.Notification">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media" parent="@style/TextAppearance.Compat.Notification.Time">
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title">
    </style>
    <style name="TextAppearance.Compat.Notification.Title.Media" parent="@style/TextAppearance.Compat.Notification.Title">
    </style>
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Counter" parent="@style/TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.Design.Counter.Overflow" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.Error" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.HelperText" parent="@style/TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.Design.Hint" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">?attr/colorControlActivated</item>
    </style>
    <style name="TextAppearance.Design.Snackbar.Message" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/design_snackbar_text_size</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Tab" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/design_tab_text_size</item>
        <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body1" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Caption" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Chip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/mtrl_chip_text_size</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="@style/TextAppearance.AppCompat.Display4">
        <item name="android:textSize">96sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">60sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">48sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">34sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Overline" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Tab" parent="@style/TextAppearance.Design.Tab">
        <item name="android:textColor">@color/mtrl_tabs_icon_color_selector</item>
    </style>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.AppCompat" parent="@style/Base.Theme.AppCompat">
    </style>
    <style name="Theme.AppCompat.CompactMenu" parent="@style/Base.Theme.AppCompat.CompactMenu">
    </style>
    <style name="Theme.AppCompat.DayNight" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="@style/Theme.AppCompat.Light.DarkActionBar">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="@style/Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="@style/Theme.AppCompat.Light.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="@style/Theme.AppCompat.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="@style/Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="@style/Theme.AppCompat.Light.NoActionBar">
    </style>
    <style name="Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat.Dialog">
    </style>
    <style name="Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light" parent="@style/Base.Theme.AppCompat.Light">
    </style>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light.DarkActionBar">
    </style>
    <style name="Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light.NoActionBar" parent="@style/Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar" parent="@style/Theme.AppCompat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design" parent="@style/Theme.AppCompat">
    </style>
    <style name="Theme.Design.BottomSheetDialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Theme.Design.Light.BottomSheetDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light.NoActionBar" parent="@style/Theme.Design.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design.NoActionBar" parent="@style/Theme.Design">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents" parent="@style/Base.Theme.MaterialComponents">
    </style>
    <style name="Theme.MaterialComponents.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Bridge" parent="@style/Base.Theme.MaterialComponents.Bridge">
    </style>
    <style name="Theme.MaterialComponents.CompactMenu" parent="@style/Base.Theme.MaterialComponents.CompactMenu">
    </style>
    <style name="Theme.MaterialComponents.Dialog" parent="@style/Base.Theme.MaterialComponents.Dialog">
    </style>
    <style name="Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog.Alert">
    </style>
    <style name="Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog.MinWidth">
    </style>
    <style name="Theme.MaterialComponents.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.DialogWhenLarge">
    </style>
    <style name="Theme.MaterialComponents.Light" parent="@style/Base.Theme.MaterialComponents.Light">
    </style>
    <style name="Theme.MaterialComponents.Light.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar">
    </style>
    <style name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Alert">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.Light.DialogWhenLarge">
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar" parent="@style/Theme.MaterialComponents.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar" parent="@style/Theme.MaterialComponents">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="@style/Base.ThemeOverlay.AppCompat">
    </style>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.ActionBar">
    </style>
    <style name="ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
    </style>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.AppCompat.Light" parent="@style/Base.ThemeOverlay.AppCompat.Light">
    </style>
    <style name="ThemeOverlay.MaterialComponents" parent="@style/ThemeOverlay.AppCompat">
    </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar" parent="@style/ThemeOverlay.AppCompat.ActionBar">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark" parent="@style/ThemeOverlay.AppCompat.Dark">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark.ActionBar" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Light" parent="@style/ThemeOverlay.AppCompat.Light">
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText" parent="">
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="ToolbarColoredBackArrow" parent="@style/AppTheme">
        <item name="android:textColorSecondary">@color/white</item>
        <item name="android:actionMenuTextColor">@color/white</item>
    </style>
    <style name="Widget.AppCompat.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton" parent="@style/Base.Widget.AppCompat.ActionButton">
    </style>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton.CloseMode">
    </style>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton.Overflow">
    </style>
    <style name="Widget.AppCompat.ActionMode" parent="@style/Base.Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView" parent="@style/Base.Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button" parent="@style/Base.Widget.AppCompat.Button">
    </style>
    <style name="Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button.Borderless">
    </style>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless.Colored">
    </style>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog">
    </style>
    <style name="Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button.Colored">
    </style>
    <style name="Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button.Small">
    </style>
    <style name="Widget.AppCompat.ButtonBar" parent="@style/Base.Widget.AppCompat.ButtonBar">
    </style>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar.AlertDialog">
    </style>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="@style/Base.Widget.AppCompat.CompoundButton.CheckBox">
    </style>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="@style/Base.Widget.AppCompat.CompoundButton.RadioButton">
    </style>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="@style/Base.Widget.AppCompat.CompoundButton.Switch">
    </style>
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text">
    </style>
    <style name="Widget.AppCompat.EditText" parent="@style/Base.Widget.AppCompat.EditText">
    </style>
    <style name="Widget.AppCompat.ImageButton" parent="@style/Base.Widget.AppCompat.ImageButton">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton" parent="@style/Widget.AppCompat.ActionButton">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="@style/Widget.AppCompat.ActionButton.CloseMode">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="@style/Widget.AppCompat.ActionButton.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="@style/Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="@style/Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="@style/Widget.AppCompat.DropDownItem.Spinner">
    </style>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="@style/Widget.AppCompat.ListView.DropDown">
    </style>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
    </style>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView" parent="@style/Widget.AppCompat.SearchView">
    </style>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown.ActionBar">
    </style>
    <style name="Widget.AppCompat.ListMenuView" parent="@style/Base.Widget.AppCompat.ListMenuView">
    </style>
    <style name="Widget.AppCompat.ListPopupWindow" parent="@style/Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView" parent="@style/Base.Widget.AppCompat.ListView">
    </style>
    <style name="Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView.DropDown">
    </style>
    <style name="Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView.Menu">
    </style>
    <style name="Widget.AppCompat.PopupMenu" parent="@style/Base.Widget.AppCompat.PopupMenu">
    </style>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.PopupWindow" parent="@style/Base.Widget.AppCompat.PopupWindow">
    </style>
    <style name="Widget.AppCompat.ProgressBar" parent="@style/Base.Widget.AppCompat.ProgressBar">
    </style>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="@style/Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style>
    <style name="Widget.AppCompat.RatingBar" parent="@style/Base.Widget.AppCompat.RatingBar">
    </style>
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="@style/Base.Widget.AppCompat.RatingBar.Indicator">
    </style>
    <style name="Widget.AppCompat.RatingBar.Small" parent="@style/Base.Widget.AppCompat.RatingBar.Small">
    </style>
    <style name="Widget.AppCompat.SearchView" parent="@style/Base.Widget.AppCompat.SearchView">
    </style>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView.ActionBar">
    </style>
    <style name="Widget.AppCompat.SeekBar" parent="@style/Base.Widget.AppCompat.SeekBar">
    </style>
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar.Discrete">
    </style>
    <style name="Widget.AppCompat.Spinner" parent="@style/Base.Widget.AppCompat.Spinner">
    </style>
    <style name="Widget.AppCompat.Spinner.DropDown" parent="@style/Widget.AppCompat.Spinner">
    </style>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown">
    </style>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner.Underlined">
    </style>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="@style/Base.Widget.AppCompat.TextView.SpinnerItem">
    </style>
    <style name="Widget.AppCompat.Toolbar" parent="@style/Base.Widget.AppCompat.Toolbar">
    </style>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/Base.Widget.AppCompat.Toolbar.Button.Navigation">
    </style>
    <style name="Widget.Compat.NotificationActionContainer" parent="">
    </style>
    <style name="Widget.Compat.NotificationActionText" parent="">
    </style>
    <style name="Widget.Design.AppBarLayout" parent="@android:style/Widget">
        <item name="android:background">?attr/colorPrimary</item>
    </style>
    <style name="Widget.Design.BottomNavigationView" parent="">
        <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
        <item name="itemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="itemHorizontalTranslationEnabled">true</item>
        <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
        <item name="labelVisibilityMode">auto</item>
    </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="@android:style/Widget">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_skipCollapsed">false</item>
    </style>
    <style name="Widget.Design.CollapsingToolbar" parent="@android:style/Widget">
        <item name="expandedTitleMargin">32dp</item>
        <item name="statusBarScrim">?attr/colorPrimaryDark</item>
    </style>
    <style name="Widget.Design.FloatingActionButton" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_fab_background</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="backgroundTint">?attr/colorAccent</item>
        <item name="borderWidth">@dimen/design_fab_border_width</item>
        <item name="elevation">@dimen/design_fab_elevation</item>
        <item name="fabSize">auto</item>
        <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
        <item name="maxImageSize">@dimen/design_fab_image_size</item>
        <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
        <item name="rippleColor">?attr/colorControlHighlight</item>
        <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    </style>
    <style name="Widget.Design.NavigationView" parent="">
        <item name="android:background">?android:attr/windowBackground</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
        <item name="elevation">@dimen/design_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
    </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout" parent="">
        <item name="insetForeground">#0000</item>
    </style>
    <style name="Widget.Design.Snackbar" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_snackbar_background</item>
        <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
        <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
        <item name="elevation">@dimen/design_snackbar_elevation</item>
        <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
    </style>
    <style name="Widget.Design.TabLayout" parent="@style/Base.Widget.Design.TabLayout">
        <item name="tabGravity">fill</item>
        <item name="tabIndicatorFullWidth">true</item>
        <item name="tabMode">fixed</item>
    </style>
    <style name="Widget.Design.TextInputLayout" parent="@android:style/Widget">
        <item name="boxBackgroundMode">none</item>
        <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
        <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
        <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
        <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
        <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
        <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
        <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
        <item name="passwordToggleTint">@color/design_tint_password_toggle</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="backgroundTint">@android:color/white</item>
        <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar.Colored" parent="@style/Widget.MaterialComponents.BottomAppBar">
        <item name="backgroundTint">?attr/colorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView" parent="@style/Widget.Design.BottomNavigationView">
        <item name="android:background">@android:color/white</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="itemIconTint">@color/mtrl_bottom_nav_item_tint</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_bottom_nav_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.Colored" parent="@style/Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="itemIconTint">@color/mtrl_bottom_nav_colored_item_tint</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_bottom_nav_colored_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal" parent="@style/Widget.Design.BottomSheet.Modal">
    </style>
    <style name="Widget.MaterialComponents.Button" parent="@style/Widget.AppCompat.Button">
        <item name="android:textAppearance">?attr/textAppearanceButton</item>
        <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
        <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
        <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
        <item name="cornerRadius">@dimen/mtrl_btn_corner_radius</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.Icon" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton.Icon" parent="@style/Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
        <item name="backgroundTint">@color/mtrl_btn_transparent_bg_color</item>
        <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog">
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton">
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton" parent="@style/Widget.MaterialComponents.Button">
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.CardView" parent="@style/CardView">
        <item name="cardBackgroundColor">?attr/colorBackgroundFloating</item>
        <item name="cardElevation">@dimen/mtrl_card_elevation</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Action" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Choice" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Entry" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Filter" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.ChipGroup" parent="@android:style/Widget">
        <item name="chipSpacing">4dp</item>
        <item name="singleLine">false</item>
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.FloatingActionButton" parent="@style/Widget.Design.FloatingActionButton">
        <item name="elevation">@dimen/mtrl_fab_elevation</item>
        <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
        <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
        <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
        <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationView" parent="@style/Widget.Design.NavigationView">
        <item name="elevation">@dimen/mtrl_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar" parent="@style/Widget.Design.Snackbar">
        <item name="android:background">@drawable/mtrl_snackbar_background</item>
        <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar.FullWidth" parent="@style/Widget.Design.Snackbar">
    </style>
    <style name="Widget.MaterialComponents.TabLayout" parent="@style/Widget.Design.TabLayout">
        <item name="android:background">@android:color/white</item>
        <item name="enforceTextAppearance">true</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?attr/colorAccent</item>
        <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
        <item name="tabTextAppearance">?attr/textAppearanceButton</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabUnboundedRipple">true</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout.Colored" parent="@style/Widget.MaterialComponents.TabLayout">
        <item name="android:background">?attr/colorAccent</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector_colored</item>
        <item name="tabIndicatorColor">@android:color/white</item>
        <item name="tabRippleColor">@color/mtrl_tabs_colored_ripple_color</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector_colored</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText">
        <item name="android:paddingTop">20dp</item>
        <item name="android:paddingBottom">16dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.FilledBox">
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText">
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox</item>
        <item name="boxBackgroundColor">@color/mtrl_textinput_filled_box_default_background_color</item>
        <item name="boxBackgroundMode">filled</item>
        <item name="boxCollapsedPaddingTop">12dp</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/mtrl_textinput_box_corner_radius_small</item>
        <item name="boxCornerRadiusBottomStart">@dimen/mtrl_textinput_box_corner_radius_small</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
        <item name="boxCollapsedPaddingTop">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox</item>
        <item name="boxCollapsedPaddingTop">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="subtitleTextColor">?android:attr/textColorSecondary</item>
        <item name="titleTextAppearance">?attr/textAppearanceHeadline6</item>
        <item name="titleTextColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Widget.Support.CoordinatorLayout" parent="@android:style/Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <style name="courseDuration" parent="">
        <item name="android:textSize">@dimen/_16sp</item>
        <item name="android:textColor">#eeeeee</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseEmployee" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#000000</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseMemo" parent="">
        <item name="android:textSize">@dimen/_14sp</item>
        <item name="android:textColor">#000000</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseMoney" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textColor">#274ac8</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseRemainTime" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#bf6c26</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseTitle" parent="">
        <item name="android:textSize">@dimen/_22sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textColor">#bf6c26</item>
        <item name="android:layout_marginRight">@dimen/_5dp</item>
    </style>
    <style name="login_edit" parent="">
        <item name="android:textSize">@dimen/_16sp</item>
        <item name="android:textColor">@color/normal_text_color2</item>
        <item name="android:textColorHint">@color/normal_text_color</item>
        <item name="android:background">@drawable/signin_line</item>
        <item name="android:layout_width">-1</item>
        <item name="android:layout_height">-1</item>
        <item name="android:layout_marginStart">@dimen/_20dp</item>
        <item name="android:layout_marginEnd">@dimen/_20dp</item>
        <item name="singleLine">true</item>
    </style>
    <style name="popupTheme" parent="@style/Theme.AppCompat.DayNight.Dialog">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="settingEdit" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#dddddd</item>
    </style>
    <style name="settingLabel" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#dddddd</item>
        <item name="android:gravity">0x11</item>
    </style>
    <style name="settingSpinner" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#dddddd</item>
        <item name="background">@drawable/img_alpha25</item>
    </style>
    <style name="social_button" parent="">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">-1</item>
        <item name="android:layout_margin">@dimen/_3dp</item>
        <item name="android:layout_weight">1</item>
    </style>
    <style name="toolbarWhiteText" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">0x11</item>
        <item name="android:layout_width">-2</item>
        <item name="android:layout_height">-2</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <declare-styleable name="ActionBar">
        <!-- The type of navigation to use. -->
        <attr name="navigationMode">
            <!-- Normal static title text -->
            <enum name="normal" value="0"/>
            <!-- The action bar will use a selection list for navigation. -->
            <enum name="listMode" value="1"/>
            <!-- The action bar will use a series of horizontal tabs for navigation. -->
            <enum name="tabMode" value="2"/>
        </attr>
        <!-- Options affecting how the action bar is displayed. -->
        <attr name="displayOptions">
            <flag name="none" value="0"/>
            <flag name="useLogo" value="0x1"/>
            <flag name="showHome" value="0x2"/>
            <flag name="homeAsUp" value="0x4"/>
            <flag name="showTitle" value="0x8"/>
            <flag name="showCustom" value="0x10"/>
            <flag name="disableHome" value="0x20"/>
        </attr>
        <!-- Specifies title text used for navigationMode="normal" -->
        <attr name="title"/>
        <!-- Specifies subtitle text used for navigationMode="normal" -->
        <attr format="string" name="subtitle"/>
        <!-- Specifies a style to use for title text. -->
        <attr format="reference" name="titleTextStyle"/>
        <!-- Specifies a style to use for subtitle text. -->
        <attr format="reference" name="subtitleTextStyle"/>
        <!-- Specifies the drawable used for the application icon. -->
        <attr format="reference" name="icon"/>
        <!-- Specifies the drawable used for the application logo. -->
        <attr format="reference" name="logo"/>
        <!-- Specifies the drawable used for item dividers. -->
        <attr format="reference" name="divider"/>
        <!-- Specifies a background drawable for the action bar. -->
        <attr format="reference" name="background"/>
        <!-- Specifies a background drawable for a second stacked row of the action bar. -->
        <attr format="reference|color" name="backgroundStacked"/>
        <!-- Specifies a background drawable for the bottom component of a split action bar. -->
        <attr format="reference|color" name="backgroundSplit"/>
        <!-- Specifies a layout for custom navigation. Overrides navigationMode. -->
        <attr format="reference" name="customNavigationLayout"/>
        <!-- Specifies a fixed height. -->
        <attr name="height"/>
        <!-- Specifies a layout to use for the "home" section of the action bar. -->
        <attr format="reference" name="homeLayout"/>
        <!-- Specifies a style resource to use for an embedded progress bar. -->
        <attr format="reference" name="progressBarStyle"/>
        <!-- Specifies a style resource to use for an indeterminate progress spinner. -->
        <attr format="reference" name="indeterminateProgressStyle"/>
        <!-- Specifies the horizontal padding on either end for an embedded progress bar. -->
        <attr format="dimension" name="progressBarPadding"/>
        <!-- Up navigation glyph -->
        <attr name="homeAsUpIndicator"/>
        <!-- Specifies padding that should be applied to the left and right sides of
             system-provided items in the bar. -->
        <attr format="dimension" name="itemPadding"/>
        <!-- Set true to hide the action bar on a vertical nested scroll of content. -->
        <attr format="boolean" name="hideOnContentScroll"/>
        <!-- Minimum inset for content views within a bar. Navigation buttons and
             menu views are excepted. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetStart"/>
        <!-- Minimum inset for content views within a bar. Navigation buttons and
             menu views are excepted. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetEnd"/>
        <!-- Minimum inset for content views within a bar. Navigation buttons and
             menu views are excepted. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetLeft"/>
        <!-- Minimum inset for content views within a bar. Navigation buttons and
             menu views are excepted. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetRight"/>
        <!-- Minimum inset for content views within a bar when a navigation button
             is present, such as the Up button. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetStartWithNavigation"/>
        <!-- Minimum inset for content views within a bar when actions from a menu
             are present. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetEndWithActions"/>
        <!-- Elevation for the action bar itself -->
        <attr format="dimension" name="elevation"/>
        <!-- Reference to a theme that should be used to inflate popups
             shown by widgets in the action bar. -->
        <attr format="reference" name="popupTheme"/>
    </declare-styleable>
    <declare-styleable name="ActionBarLayout">
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuItemView">
        <attr name="android:minWidth"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuView">
        <!-- Size of padding on either end of a divider. -->
    </declare-styleable>
    <declare-styleable name="ActionMode">
        <!-- Specifies a style to use for title text. -->
        <attr name="titleTextStyle"/>
        <!-- Specifies a style to use for subtitle text. -->
        <attr name="subtitleTextStyle"/>
        <!-- Specifies a background for the action mode bar. -->
        <attr name="background"/>
        <!-- Specifies a background for the split action mode bar. -->
        <attr name="backgroundSplit"/>
        <!-- Specifies a fixed height for the action mode bar. -->
        <attr name="height"/>
        <!-- Specifies a layout to use for the "close" item at the starting edge. -->
        <attr format="reference" name="closeItemLayout"/>
    </declare-styleable>
    <declare-styleable name="ActivityChooserView">
        <!-- The maximal number of items initially shown in the activity list. -->
        <attr format="string" name="initialActivityCount"/>
        <!-- The drawable to show in the button for expanding the activities overflow popup.
             <strong>Note:</strong> Clients would like to set this drawable
             as a clue about the action the chosen activity will perform. For
             example, if share activity is to be chosen the drawable should
             give a clue that sharing is to be performed.
         -->
        <attr format="reference" name="expandActivityOverflowButtonDrawable"/>
    </declare-styleable>
    <declare-styleable name="AlertDialog">
        <attr name="android:layout"/>
        <attr format="reference" name="buttonPanelSideLayout"/>
        <attr format="reference" name="listLayout"/>
        <attr format="reference" name="multiChoiceItemLayout"/>
        <attr format="reference" name="singleChoiceItemLayout"/>
        <attr format="reference" name="listItemLayout"/>
        <attr format="boolean" name="showTitle"/>
        <attr format="dimension" name="buttonIconDimen"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableCompat">
        <!-- Indicates whether the drawable should be initially visible. -->
        <attr name="android:visible"/>
        <!-- If true, allows the drawable's padding to change based on the
             current state that is selected.  If false, the padding will
             stay the same (based on the maximum padding of all the states).
             Enabling this feature requires that the owner of the drawable
             deal with performing layout when the state changes, which is
             often not supported. -->
        <attr name="android:variablePadding"/>
        <!-- If true, the drawable's reported internal size will remain
             constant as the state changes; the size is the maximum of all
             of the states.  If false, the size will vary based on the
             current state. -->
        <attr name="android:constantSize"/>
        <!-- Enables or disables dithering of the bitmap if the bitmap does not have the
             same pixel configuration as the screen (for instance: a ARGB 8888 bitmap with
             an RGB 565 screen). -->
        <attr name="android:dither"/>
        <!-- Amount of time (in milliseconds) to fade in a new state drawable. -->
        <attr name="android:enterFadeDuration"/>
        <!-- Amount of time (in milliseconds) to fade out an old state drawable. -->
        <attr name="android:exitFadeDuration"/>
        <!-- Indicates if the drawable needs to be mirrored when its layout direction is
             RTL (right-to-left). -->
        <!--<attr name="autoMirrored"/>-->
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableItem">
        <!-- Reference to a drawable resource to use for the frame.  If not
             given, the drawable must be defined by the first child tag. -->
        <attr name="android:drawable"/>
        <!-- Keyframe identifier for use in specifying transitions. -->
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableTransition">
        <!-- Keyframe identifier for the starting state. -->
        <attr name="android:fromId"/>
        <!-- Keyframe identifier for the ending state. -->
        <attr name="android:toId"/>
        <!-- Reference to a animation drawable resource to use for the frame.  If not
             given, the animation drawable must be defined by the first child tag. -->
        <attr name="android:drawable"/>
        <!-- Whether this transition is reversible. -->
        <attr name="android:reversible"/>
    </declare-styleable>
    <declare-styleable name="AppBarLayout"><attr name="elevation"/><attr name="android:background"/><attr format="boolean" name="expanded"/><attr name="android:keyboardNavigationCluster"/><attr name="android:touchscreenBlocksFocus"/><attr format="boolean" name="liftOnScroll"/></declare-styleable>
    <declare-styleable name="AppBarLayoutStates"><attr format="boolean" name="state_collapsed"/><attr format="boolean" name="state_collapsible"/><attr format="boolean" name="state_lifted"/><attr format="boolean" name="state_liftable"/></declare-styleable>
    <declare-styleable name="AppBarLayout_Layout"><attr name="layout_scrollFlags">
      
      <flag name="scroll" value="0x1"/>

      
      <flag name="exitUntilCollapsed" value="0x2"/>

      
      <flag name="enterAlways" value="0x4"/>

      
      <flag name="enterAlwaysCollapsed" value="0x8"/>

      
      <flag name="snap" value="0x10"/>

      
      <flag name="snapMargins" value="0x20"/>
    </attr><attr format="reference" name="layout_scrollInterpolator"/></declare-styleable>
    <declare-styleable name="AppCompatImageView">
        <attr name="android:src"/>
        <!-- Sets a drawable as the content of this ImageView. Allows the use of vector drawable
             when running on older versions of the platform. -->
        <attr format="reference" name="srcCompat"/>

        <!-- Tint to apply to the image source. -->
        <attr format="color" name="tint"/>

        <!-- Blending mode used to apply the image source tint. -->
        <attr name="tintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatSeekBar">
        <attr name="android:thumb"/>
        <!-- Drawable displayed at each progress position on a seekbar. -->
        <attr format="reference" name="tickMark"/>
        <!-- Tint to apply to the tick mark drawable. -->
        <attr format="color" name="tickMarkTint"/>
        <!-- Blending mode used to apply the tick mark tint. -->
        <attr name="tickMarkTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatTextHelper">
        <attr name="android:drawableLeft"/>
        <attr name="android:drawableTop"/>
        <attr name="android:drawableRight"/>
        <attr name="android:drawableBottom"/>
        <attr name="android:drawableStart"/>
        <attr name="android:drawableEnd"/>
        <attr name="android:textAppearance"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTextView">
        <!-- Present the text in ALL CAPS. This may use a small-caps form when available. -->
        <attr format="reference|boolean" name="textAllCaps"/>
        <attr name="android:textAppearance"/>
        <!-- Specify the type of auto-size. Note that this feature is not supported by EditText,
        works only for TextView. -->
        <attr format="enum" name="autoSizeTextType">
            <!-- No auto-sizing (default). -->
            <enum name="none" value="0"/>
            <!-- Uniform horizontal and vertical text size scaling to fit within the
            container. -->
            <enum name="uniform" value="1"/>
        </attr>
        <!-- Specify the auto-size step size if <code>autoSizeTextType</code> is set to
        <code>uniform</code>. The default is 1px. Overwrites
        <code>autoSizePresetSizes</code> if set. -->
        <attr format="dimension" name="autoSizeStepGranularity"/>
        <!-- Resource array of dimensions to be used in conjunction with
        <code>autoSizeTextType</code> set to <code>uniform</code>. Overrides
        <code>autoSizeStepGranularity</code> if set. -->
        <attr format="reference" name="autoSizePresetSizes"/>
        <!-- The minimum text size constraint to be used when auto-sizing text. -->
        <attr format="dimension" name="autoSizeMinTextSize"/>
        <!-- The maximum text size constraint to be used when auto-sizing text. -->
        <attr format="dimension" name="autoSizeMaxTextSize"/>
        <!-- The attribute for the font family. -->
        <attr format="string" name="fontFamily"/>
        <!-- Explicit height between lines of text. If set, this will override the values set
             for lineSpacingExtra and lineSpacingMultiplier. -->
        <attr format="dimension" name="lineHeight"/>
        <!-- Distance from the top of the TextView to the first text baseline. If set, this
             overrides the value set for paddingTop. -->
        <attr format="dimension" name="firstBaselineToTopHeight"/>
        <!-- Distance from the bottom of the TextView to the last text baseline. If set, this
             overrides the value set for paddingBottom. -->
        <attr format="dimension" name="lastBaselineToBottomHeight"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTheme">

        <!-- ============= -->
        <!-- Window styles -->
        <!-- ============= -->
        <eat-comment/>

        <!-- Flag indicating whether this window should have an Action Bar
             in place of the usual title bar. -->
        <attr format="boolean" name="windowActionBar"/>

        <!-- Flag indicating whether there should be no title on this window. -->
        <attr format="boolean" name="windowNoTitle"/>

        <!-- Flag indicating whether this window's Action Bar should overlay
             application content. Does nothing if the window would not
             have an Action Bar. -->
        <attr format="boolean" name="windowActionBarOverlay"/>

        <!-- Flag indicating whether action modes should overlay window content
             when there is not reserved space for their UI (such as an Action Bar). -->
        <attr format="boolean" name="windowActionModeOverlay"/>

        <!-- A fixed width for the window along the major axis of the screen,
             that is, when in landscape. Can be either an absolute dimension
             or a fraction of the screen size in that dimension. -->
        <attr format="dimension|fraction" name="windowFixedWidthMajor"/>
        <!-- A fixed height for the window along the minor axis of the screen,
             that is, when in landscape. Can be either an absolute dimension
             or a fraction of the screen size in that dimension. -->
        <attr format="dimension|fraction" name="windowFixedHeightMinor"/>

        <!-- A fixed width for the window along the minor axis of the screen,
             that is, when in portrait. Can be either an absolute dimension
             or a fraction of the screen size in that dimension. -->
        <attr format="dimension|fraction" name="windowFixedWidthMinor"/>
        <!-- A fixed height for the window along the major axis of the screen,
             that is, when in portrait. Can be either an absolute dimension
             or a fraction of the screen size in that dimension. -->
        <attr format="dimension|fraction" name="windowFixedHeightMajor"/>

        <!-- The minimum width the window is allowed to be, along the major
             axis of the screen.  That is, when in landscape.  Can be either
             an absolute dimension or a fraction of the screen size in that
             dimension. -->
        <attr format="dimension|fraction" name="windowMinWidthMajor"/>
        <!-- The minimum width the window is allowed to be, along the minor
             axis of the screen.  That is, when in portrait.  Can be either
             an absolute dimension or a fraction of the screen size in that
             dimension. -->
        <attr format="dimension|fraction" name="windowMinWidthMinor"/>

        <attr name="android:windowIsFloating"/>
        <attr name="android:windowAnimationStyle"/>

        <!-- =================== -->
        <!-- Action bar styles   -->
        <!-- =================== -->
        <eat-comment/>
        <!-- Default style for tabs within an action bar -->
        <attr format="reference" name="actionBarTabStyle"/>
        <attr format="reference" name="actionBarTabBarStyle"/>
        <attr format="reference" name="actionBarTabTextStyle"/>
        <attr format="reference" name="actionOverflowButtonStyle"/>
        <attr format="reference" name="actionOverflowMenuStyle"/>
        <!-- Reference to a theme that should be used to inflate popups
             shown by widgets in the action bar. -->
        <attr format="reference" name="actionBarPopupTheme"/>
        <!-- Reference to a style for the Action Bar -->
        <attr format="reference" name="actionBarStyle"/>
        <!-- Reference to a style for the split Action Bar. This style
             controls the split component that holds the menu/action
             buttons. actionBarStyle is still used for the primary
             bar. -->
        <attr format="reference" name="actionBarSplitStyle"/>
        <!-- Reference to a theme that should be used to inflate the
             action bar. This will be inherited by any widget inflated
             into the action bar. -->
        <attr format="reference" name="actionBarTheme"/>
        <!-- Reference to a theme that should be used to inflate widgets
             and layouts destined for the action bar. Most of the time
             this will be a reference to the current theme, but when
             the action bar has a significantly different contrast
             profile than the rest of the activity the difference
             can become important. If this is set to @null the current
             theme will be used.-->
        <attr format="reference" name="actionBarWidgetTheme"/>
        <!-- Size of the Action Bar, including the contextual
             bar used to present Action Modes. -->
        <attr format="dimension" name="actionBarSize">
            <enum name="wrap_content" value="0"/>
        </attr>
        <!-- Custom divider drawable to use for elements in the action bar. -->
        <attr format="reference" name="actionBarDivider"/>
        <!-- Custom item state list drawable background for action bar items. -->
        <attr format="reference" name="actionBarItemBackground"/>
        <!-- TextAppearance style that will be applied to text that
             appears within action menu items. -->
        <attr format="reference" name="actionMenuTextAppearance"/>
        <!-- Color for text that appears within action menu items. -->
        <!-- Color for text that appears within action menu items. -->
        <attr format="color|reference" name="actionMenuTextColor"/>


        <!-- =================== -->
        <!-- Action mode styles  -->
        <!-- =================== -->
        <eat-comment/>
        <attr format="reference" name="actionModeStyle"/>
        <attr format="reference" name="actionModeCloseButtonStyle"/>
        <!-- Background drawable to use for action mode UI -->
        <attr format="reference" name="actionModeBackground"/>
        <!-- Background drawable to use for action mode UI in the lower split bar -->
        <attr format="reference" name="actionModeSplitBackground"/>
        <!-- Drawable to use for the close action mode button -->
        <attr format="reference" name="actionModeCloseDrawable"/>
        <!-- Drawable to use for the Cut action button in Contextual Action Bar -->
        <attr format="reference" name="actionModeCutDrawable"/>
        <!-- Drawable to use for the Copy action button in Contextual Action Bar -->
        <attr format="reference" name="actionModeCopyDrawable"/>
        <!-- Drawable to use for the Paste action button in Contextual Action Bar -->
        <attr format="reference" name="actionModePasteDrawable"/>
        <!-- Drawable to use for the Select all action button in Contextual Action Bar -->
        <attr format="reference" name="actionModeSelectAllDrawable"/>
        <!-- Drawable to use for the Share action button in WebView selection action modes -->
        <attr format="reference" name="actionModeShareDrawable"/>
        <!-- Drawable to use for the Find action button in WebView selection action modes -->
        <attr format="reference" name="actionModeFindDrawable"/>
        <!-- Drawable to use for the Web Search action button in WebView selection action modes -->
        <attr format="reference" name="actionModeWebSearchDrawable"/>

        <!-- PopupWindow style to use for action modes when showing as a window overlay. -->
        <attr format="reference" name="actionModePopupWindowStyle"/>


        <!-- =================== -->
        <!-- Text styles -->
        <!-- =================== -->
        <eat-comment/>
        <!-- Text color, typeface, size, and style for the text inside of a popup menu. -->
        <attr format="reference" name="textAppearanceLargePopupMenu"/>
        <!-- Text color, typeface, size, and style for small text inside of a popup menu. -->
        <attr format="reference" name="textAppearanceSmallPopupMenu"/>
        <!-- Text color, typeface, size, and style for header text inside of a popup menu. -->
        <attr format="reference" name="textAppearancePopupMenuHeader"/>


        <!-- =================== -->
        <!-- Dialog styles -->
        <!-- =================== -->
        <eat-comment/>

        <!-- Theme to use for dialogs spawned from this theme. -->
        <attr format="reference" name="dialogTheme"/>
        <!-- Preferred padding for dialog content. -->
        <attr format="dimension" name="dialogPreferredPadding"/>
        <!-- The list divider used in alert dialogs. -->
        <attr format="reference" name="listDividerAlertDialog"/>
        <!-- Preferred corner radius of dialogs. -->
        <attr format="dimension" name="dialogCornerRadius"/>

        <!-- =================== -->
        <!-- Other widget styles -->
        <!-- =================== -->
        <eat-comment/>

        <!-- Default ActionBar dropdown style. -->
        <attr format="reference" name="actionDropDownStyle"/>
        <!-- The preferred item height for dropdown lists. -->
        <attr format="dimension" name="dropdownListPreferredItemHeight"/>
        <!-- Default Spinner style. -->
        <attr format="reference" name="spinnerDropDownItemStyle"/>
        <!-- Specifies a drawable to use for the 'home as up' indicator. -->
        <attr format="reference" name="homeAsUpIndicator"/>

        <!-- Default action button style. -->
        <attr format="reference" name="actionButtonStyle"/>

        <!-- Style for button bars -->
        <attr format="reference" name="buttonBarStyle"/>
        <!-- Style for buttons within button bars -->
        <attr format="reference" name="buttonBarButtonStyle"/>
        <!-- A style that may be applied to buttons or other selectable items
             that should react to pressed and focus states, but that do not
             have a clear visual border along the edges. -->
        <attr format="reference" name="selectableItemBackground"/>
        <!-- Background drawable for borderless standalone items that need focus/pressed states. -->
        <attr format="reference" name="selectableItemBackgroundBorderless"/>
        <!-- Style for buttons without an explicit border, often used in groups. -->
        <attr format="reference" name="borderlessButtonStyle"/>
        <!-- A drawable that may be used as a vertical divider between visual elements. -->
        <attr format="reference" name="dividerVertical"/>
        <!-- A drawable that may be used as a horizontal divider between visual elements. -->
        <attr format="reference" name="dividerHorizontal"/>
        <!-- Default ActivityChooserView style. -->
        <attr format="reference" name="activityChooserViewStyle"/>

        <!-- Default Toolbar style. -->
        <attr format="reference" name="toolbarStyle"/>
        <!-- Default Toolar NavigationButtonStyle -->
        <attr format="reference" name="toolbarNavigationButtonStyle"/>

        <!-- Default PopupMenu style. -->
        <attr format="reference" name="popupMenuStyle"/>
        <!-- Default PopupWindow style. -->
        <attr format="reference" name="popupWindowStyle"/>

        <!-- EditText text foreground color. -->
        <attr format="reference|color" name="editTextColor"/>
        <!-- EditText background drawable. -->
        <attr format="reference" name="editTextBackground"/>

        <!-- ImageButton background drawable. -->
        <attr format="reference" name="imageButtonStyle"/>

        <!-- ============================ -->
        <!-- SearchView styles and assets -->
        <!-- ============================ -->
        <eat-comment/>
        <!-- Text color, typeface, size, and style for system search result title. Defaults to primary inverse text color. -->
        <attr format="reference" name="textAppearanceSearchResultTitle"/>
        <!-- Text color, typeface, size, and style for system search result subtitle. Defaults to primary inverse text color. -->
        <attr format="reference" name="textAppearanceSearchResultSubtitle"/>
        <!-- Text color for urls in search suggestions, used by things like global search -->
        <attr format="reference|color" name="textColorSearchUrl"/>
        <!-- Style for the search query widget. -->
        <attr format="reference" name="searchViewStyle"/>

        <!-- =========== -->
        <!-- List styles -->
        <!-- =========== -->
        <eat-comment/>

        <!-- The preferred list item height. -->
        <attr format="dimension" name="listPreferredItemHeight"/>
        <!-- A smaller, sleeker list item height. -->
        <attr format="dimension" name="listPreferredItemHeightSmall"/>
        <!-- A larger, more robust list item height. -->
        <attr format="dimension" name="listPreferredItemHeightLarge"/>

        <!-- The preferred padding along the left edge of list items. -->
        <attr format="dimension" name="listPreferredItemPaddingLeft"/>
        <!-- The preferred padding along the right edge of list items. -->
        <attr format="dimension" name="listPreferredItemPaddingRight"/>

        <!-- ListPopupWindow compatibility -->
        <attr format="reference" name="dropDownListViewStyle"/>
        <attr format="reference" name="listPopupWindowStyle"/>

        <!-- The preferred TextAppearance for the primary text of list items. -->
        <attr format="reference" name="textAppearanceListItem"/>
        <!-- The preferred TextAppearance for the secondary text of list items. -->
        <attr format="reference" name="textAppearanceListItemSecondary"/>
        <!-- The preferred TextAppearance for the primary text of small list items. -->
        <attr format="reference" name="textAppearanceListItemSmall"/>

        <!-- ============ -->
        <!-- Panel styles -->
        <!-- ============ -->
        <eat-comment/>

        <!-- The background of a panel when it is inset from the left and right edges of the screen. -->
        <attr format="reference" name="panelBackground"/>
        <!-- Default Panel Menu width. -->
        <attr format="dimension" name="panelMenuListWidth"/>
        <!-- Default Panel Menu style. -->
        <attr format="reference" name="panelMenuListTheme"/>
        <!-- Drawable used as a background for selected list items. -->
        <attr format="reference" name="listChoiceBackgroundIndicator"/>

        <!-- ============= -->
        <!-- Color palette -->
        <!-- ============= -->
        <eat-comment/>

        <!-- The primary branding color for the app. By default, this is the color applied to the
             action bar background. -->
        <attr format="color" name="colorPrimary"/>

        <!-- Dark variant of the primary branding color. By default, this is the color applied to
             the status bar (via statusBarColor) and navigation bar (via navigationBarColor). -->
        <attr format="color" name="colorPrimaryDark"/>

        <!-- Bright complement to the primary branding color. By default, this is the color applied
             to framework controls (via colorControlActivated). -->
        <attr format="color" name="colorAccent"/>

        <!-- The color applied to framework controls in their normal state. -->
        <attr format="color" name="colorControlNormal"/>

        <!-- The color applied to framework controls in their activated (ex. checked) state. -->
        <attr format="color" name="colorControlActivated"/>

        <!-- The color applied to framework control highlights (ex. ripples, list selectors). -->
        <attr format="color" name="colorControlHighlight"/>

        <!-- The color applied to framework buttons in their normal state. -->
        <attr format="color" name="colorButtonNormal"/>

        <!-- The color applied to framework switch thumbs in their normal state. -->
        <attr format="color" name="colorSwitchThumbNormal"/>

        <!-- The background used by framework controls. -->
        <attr format="reference" name="controlBackground"/>

        <!-- Default color of background imagery for floating components, ex. dialogs, popups, and cards. -->
        <attr format="color" name="colorBackgroundFloating"/>

        <!-- ============ -->
        <!-- Alert Dialog styles -->
        <!-- ============ -->
        <eat-comment/>
        <attr format="reference" name="alertDialogStyle"/>
        <attr format="reference" name="alertDialogButtonGroupStyle"/>
        <attr format="boolean" name="alertDialogCenterButtons"/>
        <!-- Theme to use for alert dialogs spawned from this theme. -->
        <attr format="reference" name="alertDialogTheme"/>

        <!-- Color of list item text in alert dialogs. -->
        <attr format="reference|color" name="textColorAlertDialogListItem"/>

        <!-- Style for the "positive" buttons within button bars -->
        <attr format="reference" name="buttonBarPositiveButtonStyle"/>

        <!-- Style for the "negative" buttons within button bars -->
        <attr format="reference" name="buttonBarNegativeButtonStyle"/>

        <!-- Style for the "neutral" buttons within button bars -->
        <attr format="reference" name="buttonBarNeutralButtonStyle"/>

        <!-- ===================== -->
        <!-- Default widget styles -->
        <!-- ===================== -->
        <eat-comment/>

        <!-- Default AutoCompleteTextView style. -->
        <attr format="reference" name="autoCompleteTextViewStyle"/>
        <!-- Normal Button style. -->
        <attr format="reference" name="buttonStyle"/>
        <!-- Small Button style. -->
        <attr format="reference" name="buttonStyleSmall"/>
        <!-- Default Checkbox style. -->
        <attr format="reference" name="checkboxStyle"/>
        <!-- Default CheckedTextView style. -->
        <attr format="reference" name="checkedTextViewStyle"/>
        <!-- Default EditText style. -->
        <attr format="reference" name="editTextStyle"/>
        <!-- Default RadioButton style. -->
        <attr format="reference" name="radioButtonStyle"/>
        <!-- Default RatingBar style. -->
        <attr format="reference" name="ratingBarStyle"/>
        <!-- Indicator RatingBar style. -->
        <attr format="reference" name="ratingBarStyleIndicator"/>
        <!-- Small indicator RatingBar style. -->
        <attr format="reference" name="ratingBarStyleSmall"/>
        <!-- Default SeekBar style. -->
        <attr format="reference" name="seekBarStyle"/>
        <!-- Default Spinner style. -->
        <attr format="reference" name="spinnerStyle"/>
        <!-- Default style for the Switch widget. -->
        <attr format="reference" name="switchStyle"/>

        <!-- Default menu-style ListView style. -->
        <attr format="reference" name="listMenuViewStyle"/>

        <!-- ===================== -->
        <!-- Tooltip styles -->
        <!-- ===================== -->
        <eat-comment/>

        <!-- Background to use for tooltips -->
        <attr format="reference" name="tooltipFrameBackground"/>
        <!-- Foreground color to use for tooltips -->
        <attr format="reference|color" name="tooltipForegroundColor"/>

        <!-- Color used for error states and things that need to be drawn to
             the user's attention. -->
        <attr format="reference|color" name="colorError"/>

        <attr format="string" name="viewInflaterClass"/>
    </declare-styleable>
    <declare-styleable name="BottomAppBar"><attr name="backgroundTint"/><attr name="fabAlignmentMode">
      
      <enum name="center" value="0"/>
      
      <enum name="end" value="1"/>
    </attr><attr format="dimension" name="fabCradleMargin"/><attr format="dimension" name="fabCradleRoundedCornerRadius"/><attr format="dimension" name="fabCradleVerticalOffset"/><attr format="boolean" name="hideOnScroll"/></declare-styleable>
    <declare-styleable name="BottomNavigationView"><attr name="menu"/><attr name="labelVisibilityMode">
      
      <enum name="auto" value="-1"/>
      
      <enum name="selected" value="0"/>
      
      <enum name="labeled" value="1"/>
      
      <enum name="unlabeled" value="2"/>
    </attr><attr name="itemBackground"/><attr format="dimension" name="itemIconSize"/><attr name="itemIconTint"/><attr format="reference" name="itemTextAppearanceInactive"/><attr format="reference" name="itemTextAppearanceActive"/><attr name="itemTextColor"/><attr format="boolean" name="itemHorizontalTranslationEnabled"/><attr name="elevation"/></declare-styleable>
    <declare-styleable name="BottomSheetBehavior_Layout"><attr format="dimension" name="behavior_peekHeight">
      
      <enum name="auto" value="-1"/>
    </attr><attr format="boolean" name="behavior_hideable"/><attr format="boolean" name="behavior_skipCollapsed"/><attr format="boolean" name="behavior_fitToContents"/></declare-styleable>
    <declare-styleable name="ButtonBarLayout">
        <!-- Whether to automatically stack the buttons when there is not
             enough space to lay them out side-by-side. -->
        <attr format="boolean" name="allowStacking"/>
    </declare-styleable>
    <declare-styleable name="CardView">
        <!-- Background color for CardView. -->
        <attr format="color" name="cardBackgroundColor"/>
        <!-- Corner radius for CardView. -->
        <attr format="dimension" name="cardCornerRadius"/>
        <!-- Elevation for CardView. -->
        <attr format="dimension" name="cardElevation"/>
        <!-- Maximum Elevation for CardView. -->
        <attr format="dimension" name="cardMaxElevation"/>
        <!-- Add padding in API v21+ as well to have the same measurements with previous versions. -->
        <attr format="boolean" name="cardUseCompatPadding"/>
        <!-- Add padding to CardView on v20 and before to prevent intersections between the Card content and rounded corners. -->
        <attr format="boolean" name="cardPreventCornerOverlap"/>
        <!-- Inner padding between the edges of the Card and children of the CardView. -->
        <attr format="dimension" name="contentPadding"/>
        <!-- Inner padding between the left edge of the Card and children of the CardView. -->
        <attr format="dimension" name="contentPaddingLeft"/>
        <!-- Inner padding between the right edge of the Card and children of the CardView. -->
        <attr format="dimension" name="contentPaddingRight"/>
        <!-- Inner padding between the top edge of the Card and children of the CardView. -->
        <attr format="dimension" name="contentPaddingTop"/>
        <!-- Inner padding between the bottom edge of the Card and children of the CardView. -->
        <attr format="dimension" name="contentPaddingBottom"/>
        <!-- Workaround to read user defined minimum width -->
        <attr name="android:minWidth"/>
        <!-- Workaround to read user defined minimum height -->
        <attr name="android:minHeight"/>
    </declare-styleable>
    <declare-styleable name="Chip"><attr format="color" name="chipBackgroundColor"/><attr format="dimension" name="chipMinHeight"/><attr format="dimension" name="chipCornerRadius"/><attr format="color" name="chipStrokeColor"/><attr format="dimension" name="chipStrokeWidth"/><attr name="rippleColor"/><attr name="android:text"/><attr name="android:textAppearance"/><attr name="android:ellipsize"/><attr name="android:maxWidth"/><attr format="boolean" name="chipIconVisible"/><attr format="boolean" name="chipIconEnabled"/><attr format="reference" name="chipIcon"/><attr format="color" name="chipIconTint"/><attr format="dimension" name="chipIconSize"/><attr format="boolean" name="closeIconVisible"/><attr format="boolean" name="closeIconEnabled"/><attr format="reference" name="closeIcon"/><attr format="color" name="closeIconTint"/><attr format="dimension" name="closeIconSize"/><attr name="android:checkable"/><attr format="boolean" name="checkedIconVisible"/><attr format="boolean" name="checkedIconEnabled"/><attr format="reference" name="checkedIcon"/><attr name="showMotionSpec"/><attr name="hideMotionSpec"/><attr format="dimension" name="chipStartPadding"/><attr format="dimension" name="iconStartPadding"/><attr format="dimension" name="iconEndPadding"/><attr format="dimension" name="textStartPadding"/><attr format="dimension" name="textEndPadding"/><attr format="dimension" name="closeIconStartPadding"/><attr format="dimension" name="closeIconEndPadding"/><attr format="dimension" name="chipEndPadding"/></declare-styleable>
    <declare-styleable name="ChipGroup"><attr format="dimension" name="chipSpacing"/><attr format="dimension" name="chipSpacingHorizontal"/><attr format="dimension" name="chipSpacingVertical"/><attr format="boolean" name="singleLine"/><attr format="boolean" name="singleSelection"/><attr format="reference" name="checkedChip"/></declare-styleable>
    <declare-styleable name="CollapsingToolbarLayout"><attr format="dimension" name="expandedTitleMargin"/><attr format="dimension" name="expandedTitleMarginStart"/><attr format="dimension" name="expandedTitleMarginTop"/><attr format="dimension" name="expandedTitleMarginEnd"/><attr format="dimension" name="expandedTitleMarginBottom"/><attr format="reference" name="expandedTitleTextAppearance"/><attr format="reference" name="collapsedTitleTextAppearance"/><attr format="color" name="contentScrim"/><attr format="color" name="statusBarScrim"/><attr format="reference" name="toolbarId"/><attr format="dimension" name="scrimVisibleHeightTrigger"/><attr format="integer" name="scrimAnimationDuration"/><attr name="collapsedTitleGravity">
      
      <flag name="top" value="0x30"/>
      
      <flag name="bottom" value="0x50"/>
      
      <flag name="left" value="0x03"/>
      
      <flag name="right" value="0x05"/>
      
      <flag name="center_vertical" value="0x10"/>
      
      <flag name="fill_vertical" value="0x70"/>
      
      <flag name="center_horizontal" value="0x01"/>
      
      <flag name="center" value="0x11"/>
      
      <flag name="start" value="0x00800003"/>
      
      <flag name="end" value="0x00800005"/>
    </attr><attr name="expandedTitleGravity">
      
      <flag name="top" value="0x30"/>
      
      <flag name="bottom" value="0x50"/>
      
      <flag name="left" value="0x03"/>
      
      <flag name="right" value="0x05"/>
      
      <flag name="center_vertical" value="0x10"/>
      
      <flag name="fill_vertical" value="0x70"/>
      
      <flag name="center_horizontal" value="0x01"/>
      
      <flag name="center" value="0x11"/>
      
      <flag name="start" value="0x00800003"/>
      
      <flag name="end" value="0x00800005"/>
    </attr><attr format="boolean" name="titleEnabled"/><attr name="title"/></declare-styleable>
    <declare-styleable name="CollapsingToolbarLayout_Layout"><attr name="layout_collapseMode">
      
      <enum name="none" value="0"/>
      
      <enum name="pin" value="1"/>
      
      <enum name="parallax" value="2"/>
    </attr><attr format="float" name="layout_collapseParallaxMultiplier"/></declare-styleable>
    <declare-styleable name="ColorStateListItem">
        <!-- Base color for this state. -->
        <attr name="android:color"/>
        <!-- Alpha multiplier applied to the base color. -->
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
    </declare-styleable>
    <declare-styleable name="CompoundButton">
        <attr name="android:button"/>
        <!-- Tint to apply to the button drawable. -->
        <attr format="color" name="buttonTint"/>

        <!-- Blending mode used to apply the button tint. -->
        <attr name="buttonTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout">
        <!-- A reference to an array of integers representing the
             locations of horizontal keylines in dp from the starting edge.
             Child views can refer to these keylines for alignment using
             layout_keyline="index" where index is a 0-based index into
             this array. -->
        <attr format="reference" name="keylines"/>
        <!-- Drawable to display behind the status bar when the view is set to draw behind it. -->
        <attr format="color|reference" name="statusBarBackground"/>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout_Layout">
        <attr name="android:layout_gravity"/>
        <!-- The class name of a Behavior class defining special runtime behavior
             for this child view. -->
        <attr format="string" name="layout_behavior"/>
        <!-- The id of an anchor view that this view should position relative to. -->
        <attr format="reference" name="layout_anchor"/>
        <!-- The index of a keyline this view should position relative to.
             android:layout_gravity will affect how the view aligns to the
             specified keyline. -->
        <attr format="integer" name="layout_keyline"/>

        <!-- Specifies how an object should position relative to an anchor, on both the X and Y axes,
             within its parent's bounds.  -->
        <attr name="layout_anchorGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
            <!-- Push object to the left of its container, not changing its size. -->
            <flag name="left" value="0x03"/>
            <!-- Push object to the right of its container, not changing its size. -->
            <flag name="right" value="0x05"/>
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Grow the vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill_vertical" value="0x70"/>
            <!-- Place object in the horizontal center of its container, not changing its size. -->
            <flag name="center_horizontal" value="0x01"/>
            <!-- Grow the horizontal size of the object if needed so it completely fills its container. -->
            <flag name="fill_horizontal" value="0x07"/>
            <!-- Place the object in the center of its container in both the vertical and horizontal axis, not changing its size. -->
            <flag name="center" value="0x11"/>
            <!-- Grow the horizontal and vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill" value="0x77"/>
            <!-- Additional option that can be set to have the top and/or bottom edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the vertical gravity: a top gravity will clip the bottom
                 edge, a bottom gravity will clip the top edge, and neither will clip both edges. -->
            <flag name="clip_vertical" value="0x80"/>
            <!-- Additional option that can be set to have the left and/or right edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the horizontal gravity: a left gravity will clip the right
                 edge, a right gravity will clip the left edge, and neither will clip both edges. -->
            <flag name="clip_horizontal" value="0x08"/>
            <!-- Push object to the beginning of its container, not changing its size. -->
            <flag name="start" value="0x00800003"/>
            <!-- Push object to the end of its container, not changing its size. -->
            <flag name="end" value="0x00800005"/>
        </attr>

        <!-- Specifies how this view insets the CoordinatorLayout and make some other views
             dodge it. -->
        <attr format="enum" name="layout_insetEdge">
            <!-- Don't inset. -->
            <enum name="none" value="0x0"/>
            <!-- Inset the top edge. -->
            <enum name="top" value="0x30"/>
            <!-- Inset the bottom edge. -->
            <enum name="bottom" value="0x50"/>
            <!-- Inset the left edge. -->
            <enum name="left" value="0x03"/>
            <!-- Inset the right edge. -->
            <enum name="right" value="0x05"/>
            <!-- Inset the start edge. -->
            <enum name="start" value="0x00800003"/>
            <!-- Inset the end edge. -->
            <enum name="end" value="0x00800005"/>
        </attr>
        <!-- Specifies how this view dodges the inset edges of the CoordinatorLayout. -->
        <attr name="layout_dodgeInsetEdges">
            <!-- Don't dodge any edges -->
            <flag name="none" value="0x0"/>
            <!-- Dodge the top inset edge. -->
            <flag name="top" value="0x30"/>
            <!-- Dodge the bottom inset edge. -->
            <flag name="bottom" value="0x50"/>
            <!-- Dodge the left inset edge. -->
            <flag name="left" value="0x03"/>
            <!-- Dodge the right inset edge. -->
            <flag name="right" value="0x05"/>
            <!-- Dodge the start inset edge. -->
            <flag name="start" value="0x00800003"/>
            <!-- Dodge the end inset edge. -->
            <flag name="end" value="0x00800005"/>
            <!-- Dodge all the inset edges. -->
            <flag name="all" value="0x77"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="DesignTheme"><attr name="bottomSheetDialogTheme"/><attr name="bottomSheetStyle"/></declare-styleable>
    <declare-styleable name="DrawerArrowToggle">
        <!-- The drawing color for the bars -->
        <attr format="color" name="color"/>
        <!-- Whether bars should rotate or not during transition -->
        <attr format="boolean" name="spinBars"/>
        <!-- The total size of the drawable -->
        <attr format="dimension" name="drawableSize"/>
        <!-- The max gap between the bars when they are parallel to each other -->
        <attr format="dimension" name="gapBetweenBars"/>
        <!-- The length of the arrow head when formed to make an arrow -->
        <attr format="dimension" name="arrowHeadLength"/>
        <!-- The length of the shaft when formed to make an arrow -->
        <attr format="dimension" name="arrowShaftLength"/>
        <!-- The length of the bars when they are parallel to each other -->
        <attr format="dimension" name="barLength"/>
        <!-- The thickness (stroke size) for the bar paint -->
        <attr format="dimension" name="thickness"/>
    </declare-styleable>
    <declare-styleable name="FloatingActionButton"><attr name="backgroundTint"/><attr name="backgroundTintMode"/><attr name="rippleColor"/><attr name="fabSize">
      
      <enum name="auto" value="-1"/>
      
      <enum name="normal" value="0"/>
      
      <enum name="mini" value="1"/>
    </attr><attr format="dimension" name="fabCustomSize"/><attr name="elevation"/><attr format="dimension" name="hoveredFocusedTranslationZ"/><attr format="dimension" name="pressedTranslationZ"/><attr format="dimension" name="borderWidth"/><attr format="boolean" name="useCompatPadding"/><attr format="dimension" name="maxImageSize"/><attr name="showMotionSpec"/><attr name="hideMotionSpec"/></declare-styleable>
    <declare-styleable name="FloatingActionButton_Behavior_Layout"><attr format="boolean" name="behavior_autoHide"/></declare-styleable>
    <declare-styleable name="FlowLayout"><attr format="dimension" name="itemSpacing"/><attr format="dimension" name="lineSpacing"/></declare-styleable>
    <declare-styleable name="FontFamily">
        <!-- The authority of the Font Provider to be used for the request. -->
        <attr format="string" name="fontProviderAuthority"/>
        <!-- The package for the Font Provider to be used for the request. This is used to verify
        the identity of the provider. -->
        <attr format="string" name="fontProviderPackage"/>
        <!-- The query to be sent over to the provider. Refer to your font provider's documentation
        on the format of this string. -->
        <attr format="string" name="fontProviderQuery"/>
        <!-- The sets of hashes for the certificates the provider should be signed with. This is
        used to verify the identity of the provider, and is only required if the provider is not
        part of the system image. This value may point to one list or a list of lists, where each
        individual list represents one collection of signature hashes. Refer to your font provider's
        documentation for these values. -->
        <attr format="reference" name="fontProviderCerts"/>
        <!-- The strategy to be used when fetching font data from a font provider in XML layouts.
        This attribute is ignored when the resource is loaded from code, as it is equivalent to the
        choice of API between {@link
    androidx.core.content.res.ResourcesCompat#getFont(Context, int)} (blocking) and
        {@link
    androidx.core.content.res.ResourcesCompat#getFont(Context, int, FontCallback, Handler)}
        (async). -->
        <attr name="fontProviderFetchStrategy">
            <!-- The blocking font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, request the
              font from the provider and wait until it is finished.  You can change the length of
              the timeout by modifying fontProviderFetchTimeout.  If the timeout happens, the
              default typeface will be used instead. -->
            <enum name="blocking" value="0"/>
            <!-- The async font fetch works as follows.
              First, check the local cache, then if the requeted font is not cached, trigger a
              request the font and continue with layout inflation. Once the font fetch succeeds, the
              target text view will be refreshed with the downloaded font data. The
              fontProviderFetchTimeout will be ignored if async loading is specified. -->
            <enum name="async" value="1"/>
        </attr>
        <!-- The length of the timeout during fetching. -->
        <attr format="integer" name="fontProviderFetchTimeout">
            <!-- A special value for the timeout. In this case, the blocking font fetching will not
              timeout and wait until a reply is received from the font provider. -->
            <enum name="forever" value="-1"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        <!-- The style of the given font file. This will be used when the font is being loaded into
         the font stack and will override any style information in the font's header tables. If
         unspecified, the value in the font's header tables will be used. -->
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        <!-- The reference to the font file to be used. This should be a file in the res/font folder
         and should therefore have an R reference value. E.g. @font/myfont -->
        <attr format="reference" name="font"/>
        <!-- The weight of the given font file. This will be used when the font is being loaded into
         the font stack and will override any weight information in the font's header tables. Must
         be a positive number, a multiple of 100, and between 100 and 900, inclusive. The most
         common values are 400 for regular weight and 700 for bold weight. If unspecified, the value
         in the font's header tables will be used. -->
        <attr format="integer" name="fontWeight"/>
        <!-- The variation settings to be applied to the font. The string should be in the following
         format: "'tag1' value1, 'tag2' value2, ...". If the default variation settings should be
         used, or the font used does not support variation settings, this attribute needs not be
         specified. -->
        <attr format="string" name="fontVariationSettings"/>
        <!-- The index of the font in the tcc font file. If the font file referenced is not in the
        tcc format, this attribute needs not be specified. -->
        <attr format="integer" name="ttcIndex"/>
        <!-- References to the framework attrs -->
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="ForegroundLinearLayout"><attr name="android:foreground"/><attr name="android:foregroundGravity"/><attr format="boolean" name="foregroundInsidePadding"/></declare-styleable>
    <declare-styleable name="GradientColor">
        <!-- Start color of the gradient. -->
        <attr name="android:startColor"/>
        <!-- Optional center color. -->
        <attr name="android:centerColor"/>
        <!-- End color of the gradient. -->
        <attr name="android:endColor"/>
        <!-- Type of gradient. The default type is linear. -->
        <attr name="android:type"/>

        <!-- Only applied to RadialGradient-->
        <!-- Radius of the gradient, used only with radial gradient. -->
        <attr name="android:gradientRadius"/>

        <!-- Only applied to SweepGradient / RadialGradient-->
        <!-- X coordinate of the center of the gradient within the path. -->
        <attr name="android:centerX"/>
        <!-- Y coordinate of the center of the gradient within the path. -->
        <attr name="android:centerY"/>

        <!-- LinearGradient specific -->
        <!-- X coordinate of the start point origin of the gradient.
             Defined in same coordinates as the path itself -->
        <attr name="android:startX"/>
        <!-- Y coordinate of the start point of the gradient within the shape.
             Defined in same coordinates as the path itself -->
        <attr name="android:startY"/>
        <!-- X coordinate of the end point origin of the gradient.
             Defined in same coordinates as the path itself -->
        <attr name="android:endX"/>
        <!-- Y coordinate of the end point of the gradient within the shape.
             Defined in same coordinates as the path itself -->
        <attr name="android:endY"/>

        <!-- Defines the tile mode of the gradient. SweepGradient doesn't support tiling. -->
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        <!-- The offset (or ratio) of this current color item inside the gradient.
             The value is only meaningful when it is between 0 and 1. -->
        <attr name="android:offset"/>
        <!-- The current color for the offset inside the gradient. -->
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat">
        <!-- Should the layout be a column or a row?  Use "horizontal"
             for a row, "vertical" for a column.  The default is
             horizontal. -->
        <attr name="android:orientation"/>
        <attr name="android:gravity"/>
        <!-- When set to false, prevents the layout from aligning its children's
             baselines. This attribute is particularly useful when the children
             use different values for gravity. The default value is true. -->
        <attr name="android:baselineAligned"/>
        <!-- When a linear layout is part of another layout that is baseline
          aligned, it can specify which of its children to baseline align to
          (that is, which child TextView).-->
        <attr name="android:baselineAlignedChildIndex"/>
        <!-- Defines the maximum weight sum. If unspecified, the sum is computed
             by adding the layout_weight of all of the children. This can be
             used for instance to give a single child 50% of the total available
             space by giving it a layout_weight of 0.5 and setting the weightSum
             to 1.0. -->
        <attr name="android:weightSum"/>
        <!-- When set to true, all children with a weight will be considered having
             the minimum size of the largest child. If false, all children are
             measured normally. -->
        <attr format="boolean" name="measureWithLargestChild"/>
        <!-- Drawable to use as a vertical divider between buttons. -->
        <attr name="divider"/>
        <!-- Setting for which dividers to show. -->
        <attr name="showDividers">
            <flag name="none" value="0"/>
            <flag name="beginning" value="1"/>
            <flag name="middle" value="2"/>
            <flag name="end" value="4"/>
        </attr>
        <!-- Size of padding on either end of a divider. -->
        <attr format="dimension" name="dividerPadding"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat_Layout">
        <attr name="android:layout_width"/>
        <attr name="android:layout_height"/>
        <attr name="android:layout_weight"/>
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ListPopupWindow">
        <!-- Amount of pixels by which the drop down should be offset vertically. -->
        <attr name="android:dropDownVerticalOffset"/>
        <!-- Amount of pixels by which the drop down should be offset horizontally. -->
        <attr name="android:dropDownHorizontalOffset"/>
    </declare-styleable>
    <declare-styleable name="MaterialButton"><attr name="android:insetLeft"/><attr name="android:insetRight"/><attr name="android:insetTop"/><attr name="android:insetBottom"/><attr name="backgroundTint"/><attr name="backgroundTintMode"/><attr format="reference" name="icon"/><attr format="dimension" name="iconSize"/><attr format="dimension" name="iconPadding"/><attr name="iconGravity">
      
      <flag name="start" value="0x1"/>
      
      <flag name="textStart" value="0x2"/>
    </attr><attr format="color" name="iconTint"/><attr name="iconTintMode"/><attr name="strokeColor"/><attr name="strokeWidth"/><attr format="dimension" name="cornerRadius"/><attr name="rippleColor"/></declare-styleable>
    <declare-styleable name="MaterialCardView"><attr name="strokeColor"/><attr name="strokeWidth"/></declare-styleable>
    <declare-styleable name="MaterialComponentsTheme"><attr name="colorAccent"/><attr name="colorPrimary"/><attr name="colorPrimaryDark"/><attr name="colorSecondary"/><attr name="scrimBackground"/><attr name="colorBackgroundFloating"/><attr name="bottomSheetDialogTheme"/><attr name="bottomSheetStyle"/><attr name="materialButtonStyle"/><attr name="chipGroupStyle"/><attr name="chipStyle"/><attr name="chipStandaloneStyle"/><attr name="editTextStyle"/><attr name="floatingActionButtonStyle"/><attr name="materialCardViewStyle"/><attr name="navigationViewStyle"/><attr name="tabStyle"/><attr name="textInputStyle"/><attr name="snackbarButtonStyle"/><attr name="textAppearanceHeadline1"/><attr name="textAppearanceHeadline2"/><attr name="textAppearanceHeadline3"/><attr name="textAppearanceHeadline4"/><attr name="textAppearanceHeadline5"/><attr name="textAppearanceHeadline6"/><attr name="textAppearanceSubtitle1"/><attr name="textAppearanceSubtitle2"/><attr name="textAppearanceBody1"/><attr name="textAppearanceBody2"/><attr name="textAppearanceCaption"/><attr name="textAppearanceButton"/><attr name="textAppearanceOverline"/></declare-styleable>
    <declare-styleable name="MenuGroup">

        <!-- The ID of the group. -->
        <attr name="android:id"/>

        <!-- The category applied to all items within this group.
             (This will be or'ed with the orderInCategory attribute.) -->
        <attr name="android:menuCategory"/>

        <!-- The order within the category applied to all items within this group.
             (This will be or'ed with the category attribute.) -->
        <attr name="android:orderInCategory"/>

        <!-- Whether the items are capable of displaying a check mark. -->
        <attr name="android:checkableBehavior"/>

        <!-- Whether the items are shown/visible. -->
        <attr name="android:visible"/>

        <!-- Whether the items are enabled. -->
        <attr name="android:enabled"/>

    </declare-styleable>
    <declare-styleable name="MenuItem">

        <!-- The ID of the item. -->
        <attr name="android:id"/>

        <!-- The category applied to the item.
             (This will be or'ed with the orderInCategory attribute.) -->
        <attr name="android:menuCategory"/>

        <!-- The order within the category applied to the item.
             (This will be or'ed with the category attribute.) -->
        <attr name="android:orderInCategory"/>

        <!-- The title associated with the item. -->
        <attr name="android:title"/>

        <!-- The condensed title associated with the item.  This is used in situations where the
             normal title may be too long to be displayed. -->
        <attr name="android:titleCondensed"/>

        <!-- The icon associated with this item.  This icon will not always be shown, so
             the title should be sufficient in describing this item. -->
        <attr name="android:icon"/>

        <!-- The alphabetic shortcut key.  This is the shortcut when using a keyboard
             with alphabetic keys. -->
        <attr name="android:alphabeticShortcut"/>

        <!-- The alphabetic modifier key. This is the modifier when using a keyboard
            with alphabetic keys. The values should be kept in sync with KeyEvent -->
        <attr name="alphabeticModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        <!-- The numeric shortcut key.  This is the shortcut when using a numeric (e.g., 12-key)
             keyboard. -->
        <attr name="android:numericShortcut"/>

        <!-- The numeric modifier key. This is the modifier when using a numeric (e.g., 12-key)
            keyboard. The values should be kept in sync with KeyEvent -->
        <attr name="numericModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        <!-- Whether the item is capable of displaying a check mark. -->
        <attr name="android:checkable"/>

        <!-- Whether the item is checked.  Note that you must first have enabled checking with
             the checkable attribute or else the check mark will not appear. -->
        <attr name="android:checked"/>

        <!-- Whether the item is shown/visible. -->
        <attr name="android:visible"/>

        <!-- Whether the item is enabled. -->
        <attr name="android:enabled"/>

        <!-- Name of a method on the Context used to inflate the menu that will be
             called when the item is clicked. -->
        <attr name="android:onClick"/>

        <!-- How this item should display in the Action Bar, if present. -->
        <attr name="showAsAction">
            <!-- Never show this item in an action bar, show it in the overflow menu instead.
                 Mutually exclusive with "ifRoom" and "always". -->
            <flag name="never" value="0"/>
            <!-- Show this item in an action bar if there is room for it as determined
                 by the system. Favor this option over "always" where possible.
                 Mutually exclusive with "never" and "always". -->
            <flag name="ifRoom" value="1"/>
            <!-- Always show this item in an actionbar, even if it would override
                 the system's limits of how much stuff to put there. This may make
                 your action bar look bad on some screens. In most cases you should
                 use "ifRoom" instead. Mutually exclusive with "ifRoom" and "never". -->
            <flag name="always" value="2"/>
            <!-- When this item is shown as an action in the action bar, show a text
                 label with it even if it has an icon representation. -->
            <flag name="withText" value="4"/>
            <!-- This item's action view collapses to a normal menu
                 item. When expanded, the action view takes over a
                 larger segment of its container. -->
            <flag name="collapseActionView" value="8"/>
        </attr>

        <!-- An optional layout to be used as an action view.
             See {@link android.view.MenuItem#setActionView(android.view.View)}
             for more info. -->
        <attr format="reference" name="actionLayout"/>

        <!-- The name of an optional View class to instantiate and use as an
             action view. See {@link android.view.MenuItem#setActionView(android.view.View)}
             for more info. -->
        <attr format="string" name="actionViewClass"/>

        <!-- The name of an optional ActionProvider class to instantiate an action view
             and perform operations such as default action for that menu item.
             See {@link android.view.MenuItem#setActionProvider(android.view.ActionProvider)}
             for more info. -->
        <attr format="string" name="actionProviderClass"/>

        <!-- The content description associated with the item. -->
        <attr format="string" name="contentDescription"/>

        <!-- The tooltip text associated with the item. -->
        <attr format="string" name="tooltipText"/>

        <!-- Tint to apply to the icon. -->
        <attr format="color" name="iconTint"/>

        <!-- Blending mode used to apply the icon tint. -->
        <attr name="iconTintMode">
            <!-- The tint is drawn on top of the icon.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the icon. The icon’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the icon, but with the icon’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the icon with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>

    </declare-styleable>
    <declare-styleable name="MenuView">
        <!-- Default appearance of menu item text. -->
        <attr name="android:itemTextAppearance"/>
        <!-- Default horizontal divider between rows of menu items. -->
        <attr name="android:horizontalDivider"/>
        <!-- Default vertical divider between menu items. -->
        <attr name="android:verticalDivider"/>
        <!-- Default background for the menu header. -->
        <attr name="android:headerBackground"/>
        <!-- Default background for each menu item. -->
        <attr name="android:itemBackground"/>
        <!-- Default animations for the menu. -->
        <attr name="android:windowAnimationStyle"/>
        <!-- Default disabled icon alpha for each menu item that shows an icon. -->
        <attr name="android:itemIconDisabledAlpha"/>
        <!-- Whether space should be reserved in layout when an icon is missing. -->
        <attr format="boolean" name="preserveIconSpacing"/>
        <!-- Drawable for the arrow icon indicating a particular item is a submenu. -->
        <attr format="reference" name="subMenuArrow"/>
    </declare-styleable>
    <declare-styleable name="NavigationView"><attr name="android:background"/><attr name="android:fitsSystemWindows"/><attr name="android:maxWidth"/><attr name="elevation"/><attr format="reference" name="menu"/><attr format="color" name="itemIconTint"/><attr format="color" name="itemTextColor"/><attr format="reference" name="itemBackground"/><attr format="reference" name="itemTextAppearance"/><attr format="reference" name="headerLayout"/><attr format="dimension" name="itemHorizontalPadding"/><attr format="dimension" name="itemIconPadding"/></declare-styleable>
    <declare-styleable name="PopupWindow">
        <!-- Whether the popup window should overlap its anchor view. -->
        <attr format="boolean" name="overlapAnchor"/>
        <attr name="android:popupBackground"/>
        <attr name="android:popupAnimationStyle"/>
    </declare-styleable>
    <declare-styleable name="PopupWindowBackgroundState">
        <!-- State identifier indicating the popup will be above the anchor. -->
        <attr format="boolean" name="state_above_anchor"/>
    </declare-styleable>
    <declare-styleable name="RecycleListView">
        <!-- Bottom padding to use when no buttons are present. -->
        <attr format="dimension" name="paddingBottomNoButtons"/>
        <!-- Top padding to use when no title is present. -->
        <attr format="dimension" name="paddingTopNoTitle"/>
    </declare-styleable>
    <declare-styleable name="RecyclerView">
        <!-- Class name of the Layout Manager to be used.
        <p/>
        The class must extandroidx.recyclerview.widget.RecyclerViewView$LayoutManager
        and have either a default constructor or constructor with the signature
        (android.content.Context, android.util.AttributeSet, int, int).
         <p/>
         If the name starts with a '.', application package is prefixed.
         Else, if the name contains a '.', the classname is assumed to be a full class name.
         Else, the recycler view package naandroidx.appcompat.widgetdget) is prefixed. -->
        <attr format="string" name="layoutManager"/>

        <!-- ============================= -->
        <!-- Attributes for Layout Manager -->
        <!-- ============================= -->
        <eat-comment/>

        <attr name="android:orientation"/>
        <attr name="android:descendantFocusability"/>
        <attr format="integer" name="spanCount"/>
        <attr format="boolean" name="reverseLayout"/>
        <attr format="boolean" name="stackFromEnd"/>
        <attr format="boolean" name="fastScrollEnabled"/>
        <attr format="reference" name="fastScrollVerticalThumbDrawable"/>
        <attr format="reference" name="fastScrollVerticalTrackDrawable"/>
        <attr format="reference" name="fastScrollHorizontalThumbDrawable"/>
        <attr format="reference" name="fastScrollHorizontalTrackDrawable"/>
    </declare-styleable>
    <declare-styleable name="ScrimInsetsFrameLayout"><attr format="color|reference" name="insetForeground"/></declare-styleable>
    <declare-styleable name="ScrollingViewBehavior_Layout"><attr format="dimension" name="behavior_overlapTop"/></declare-styleable>
    <declare-styleable name="SearchView">
        <!-- The layout to use for the search view. -->
        <attr format="reference" name="layout"/>
        <!-- The default state of the SearchView. If true, it will be iconified when not in
             use and expanded when clicked. -->
        <attr format="boolean" name="iconifiedByDefault"/>
        <!-- An optional maximum width of the SearchView. -->
        <attr name="android:maxWidth"/>
        <!-- An optional user-defined query hint string to be displayed in the empty query field. -->
        <attr format="string" name="queryHint"/>
        <!-- Default query hint used when {@code queryHint} is undefined and
             the search view's {@code SearchableInfo} does not provide a hint. -->
        <attr format="string" name="defaultQueryHint"/>
        <!-- The IME options to set on the query text field. -->
        <attr name="android:imeOptions"/>
        <!-- The input type to set on the query text field. -->
        <attr name="android:inputType"/>
        <!-- Close button icon -->
        <attr format="reference" name="closeIcon"/>
        <!-- Go button icon -->
        <attr format="reference" name="goIcon"/>
        <!-- Search icon -->
        <attr format="reference" name="searchIcon"/>
        <!-- Search icon displayed as a text field hint -->
        <attr format="reference" name="searchHintIcon"/>
        <!-- Voice button icon -->
        <attr format="reference" name="voiceIcon"/>
        <!-- Commit icon shown in the query suggestion row -->
        <attr format="reference" name="commitIcon"/>
        <!-- Layout for query suggestion rows -->
        <attr format="reference" name="suggestionRowLayout"/>
        <!-- Background for the section containing the search query -->
        <attr format="reference" name="queryBackground"/>
        <!-- Background for the section containing the action (e.g. voice search) -->
        <attr format="reference" name="submitBackground"/>
        <attr name="android:focusable"/>
    </declare-styleable>
    <declare-styleable name="Snackbar"><attr format="reference" name="snackbarStyle"/><attr format="reference" name="snackbarButtonStyle"/></declare-styleable>
    <declare-styleable name="SnackbarLayout"><attr name="android:maxWidth"/><attr name="elevation"/><attr format="dimension" name="maxActionInlineWidth"/></declare-styleable>
    <declare-styleable name="Spinner">
        <!-- The prompt to display when the spinner's dialog is shown. -->
        <attr name="android:prompt"/>
        <!-- Theme to use for the drop-down or dialog popup window. -->
        <attr name="popupTheme"/>
        <!-- Background drawable to use for the dropdown in spinnerMode="dropdown". -->
        <attr name="android:popupBackground"/>
        <!-- Width of the dropdown in spinnerMode="dropdown". -->
        <attr name="android:dropDownWidth"/>
        <!-- Reference to an array resource that will populate the Spinner. -->
        <attr name="android:entries"/>
    </declare-styleable>
    <declare-styleable name="StateListDrawable">
        <!-- Indicates whether the drawable should be initially visible. -->
        <attr name="android:visible"/>
        <!-- If true, allows the drawable's padding to change based on the
             current state that is selected.  If false, the padding will
             stay the same (based on the maximum padding of all the states).
             Enabling this feature requires that the owner of the drawable
             deal with performing layout when the state changes, which is
             often not supported. -->
        <attr name="android:variablePadding"/>
        <!-- If true, the drawable's reported internal size will remain
             constant as the state changes; the size is the maximum of all
             of the states.  If false, the size will vary based on the
             current state. -->
        <attr name="android:constantSize"/>
        <!-- Enables or disables dithering of the bitmap if the bitmap does not have the
             same pixel configuration as the screen (for instance: a ARGB 8888 bitmap with
             an RGB 565 screen). -->
        <attr name="android:dither"/>
        <!-- Amount of time (in milliseconds) to fade in a new state drawable. -->
        <attr name="android:enterFadeDuration"/>
        <!-- Amount of time (in milliseconds) to fade out an old state drawable. -->
        <attr name="android:exitFadeDuration"/>
        <!-- Indicates if the drawable needs to be mirrored when its layout direction is
             RTL (right-to-left). -->
        <!--<attr name="autoMirrored"/>-->
    </declare-styleable>
    <declare-styleable name="StateListDrawableItem">
        <!-- Reference to a drawable resource to use for the state. If not
             given, the drawable must be defined by the first child tag. -->
        <attr name="android:drawable"/>
    </declare-styleable>
    <declare-styleable name="SwitchCompat">
        <!-- Drawable to use as the "thumb" that switches back and forth. -->
        <attr name="android:thumb"/>
        <!-- Tint to apply to the thumb drawable. -->
        <attr format="color" name="thumbTint"/>
        <!-- Blending mode used to apply the thumb tint. -->
        <attr name="thumbTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        <!-- Drawable to use as the "track" that the switch thumb slides within. -->
        <attr format="reference" name="track"/>
        <!-- Tint to apply to the track. -->
        <attr format="color" name="trackTint"/>
        <!-- Blending mode used to apply the track tint. -->
        <attr name="trackTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        <!-- Text to use when the switch is in the checked/"on" state. -->
        <attr name="android:textOn"/>
        <!-- Text to use when the switch is in the unchecked/"off" state. -->
        <attr name="android:textOff"/>
        <!-- Amount of padding on either side of text within the switch thumb. -->
        <attr format="dimension" name="thumbTextPadding"/>
        <!-- TextAppearance style for text displayed on the switch thumb. -->
        <attr format="reference" name="switchTextAppearance"/>
        <!-- Minimum width for the switch component -->
        <attr format="dimension" name="switchMinWidth"/>
        <!-- Minimum space between the switch and caption text -->
        <attr format="dimension" name="switchPadding"/>
        <!-- Whether to split the track and leave a gap for the thumb drawable. -->
        <attr format="boolean" name="splitTrack"/>
        <!-- Whether to draw on/off text. -->
        <attr format="boolean" name="showText"/>
    </declare-styleable>
    <declare-styleable name="TabItem"><attr name="android:text"/><attr name="android:icon"/><attr name="android:layout"/></declare-styleable>
    <declare-styleable name="TabLayout"><attr format="color" name="tabIndicatorColor"/><attr format="dimension" name="tabIndicatorHeight"/><attr format="dimension" name="tabContentStart"/><attr format="reference" name="tabBackground"/><attr format="reference" name="tabIndicator"/><attr name="tabIndicatorGravity">
      
      <enum name="bottom" value="0"/>
      
      <enum name="center" value="1"/>
      
      <enum name="top" value="2"/>
      
      <enum name="stretch" value="3"/>
    </attr><attr format="integer" name="tabIndicatorAnimationDuration"/><attr format="boolean" name="tabIndicatorFullWidth"/><attr name="tabMode">
      <enum name="scrollable" value="0"/>
      <enum name="fixed" value="1"/>
    </attr><attr name="tabGravity">
      <enum name="fill" value="0"/>
      <enum name="center" value="1"/>
    </attr><attr format="boolean" name="tabInlineLabel"/><attr format="dimension" name="tabMinWidth"/><attr format="dimension" name="tabMaxWidth"/><attr format="reference" name="tabTextAppearance"/><attr format="color" name="tabTextColor"/><attr format="color" name="tabSelectedTextColor"/><attr format="dimension" name="tabPaddingStart"/><attr format="dimension" name="tabPaddingTop"/><attr format="dimension" name="tabPaddingEnd"/><attr format="dimension" name="tabPaddingBottom"/><attr format="dimension" name="tabPadding"/><attr format="color" name="tabIconTint"/><attr name="tabIconTintMode">
      <enum name="src_over" value="3"/>
      <enum name="src_in" value="5"/>
      <enum name="src_atop" value="9"/>
      <enum name="multiply" value="14"/>
      <enum name="screen" value="15"/>
      <enum name="add" value="16"/>
    </attr><attr format="color" name="tabRippleColor"/><attr format="boolean" name="tabUnboundedRipple"/></declare-styleable>
    <declare-styleable name="TextAppearance">
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
        <attr name="android:textColorHint"/>
        <attr name="android:textColorLink"/>
        <attr name="android:textStyle"/>
        <attr name="android:typeface"/>
        <attr name="android:fontFamily"/>
        <attr name="fontFamily"/>
        <attr name="textAllCaps"/>
        <attr name="android:shadowColor"/>
        <attr name="android:shadowDy"/>
        <attr name="android:shadowDx"/>
        <attr name="android:shadowRadius"/>
    </declare-styleable>
    <declare-styleable name="TextInputLayout"><attr name="android:textColorHint"/><attr name="android:hint"/><attr format="boolean" name="hintEnabled"/><attr format="boolean" name="hintAnimationEnabled"/><attr format="reference" name="hintTextAppearance"/><attr format="string" name="helperText"/><attr format="boolean" name="helperTextEnabled"/><attr format="reference" name="helperTextTextAppearance"/><attr format="boolean" name="errorEnabled"/><attr format="reference" name="errorTextAppearance"/><attr format="boolean" name="counterEnabled"/><attr format="integer" name="counterMaxLength"/><attr format="reference" name="counterTextAppearance"/><attr format="reference" name="counterOverflowTextAppearance"/><attr format="boolean" name="passwordToggleEnabled"/><attr format="reference" name="passwordToggleDrawable"/><attr format="string" name="passwordToggleContentDescription"/><attr format="color" name="passwordToggleTint"/><attr name="passwordToggleTintMode">
      
      <enum name="src_over" value="3"/>
      
      <enum name="src_in" value="5"/>
      
      <enum name="src_atop" value="9"/>
      
      <enum name="multiply" value="14"/>
      
      <enum name="screen" value="15"/>
    </attr><attr name="boxBackgroundMode">
      
      <enum name="none" value="0"/>
      
      <enum name="filled" value="1"/>
      
      <enum name="outline" value="2"/>
    </attr><attr format="dimension" name="boxCollapsedPaddingTop"/><attr format="dimension" name="boxCornerRadiusTopStart"/><attr format="dimension" name="boxCornerRadiusTopEnd"/><attr format="dimension" name="boxCornerRadiusBottomStart"/><attr format="dimension" name="boxCornerRadiusBottomEnd"/><attr format="color" name="boxStrokeColor"/><attr format="color" name="boxBackgroundColor"/><attr format="dimension" name="boxStrokeWidth"/></declare-styleable>
    <declare-styleable name="ThemeEnforcement"><attr format="boolean" name="enforceMaterialTheme"/><attr format="boolean" name="enforceTextAppearance"/><attr name="android:textAppearance"/></declare-styleable>
    <declare-styleable name="Toolbar">
        <attr format="reference" name="titleTextAppearance"/>
        <attr format="reference" name="subtitleTextAppearance"/>
        <attr name="title"/>
        <attr name="subtitle"/>
        <attr name="android:gravity"/>
        <!--  Specifies extra space on the left, start, right and end sides
              of the toolbar's title. Margin values should be positive. -->
        <attr format="dimension" name="titleMargin"/>
        <!--  Specifies extra space on the start side of the toolbar's title.
              If both this attribute and titleMargin are specified, then this
              attribute takes precedence. Margin values should be positive. -->
        <attr format="dimension" name="titleMarginStart"/>
        <!--  Specifies extra space on the end side of the toolbar's title.
              If both this attribute and titleMargin are specified, then this
              attribute takes precedence. Margin values should be positive. -->
        <attr format="dimension" name="titleMarginEnd"/>
        <!--  Specifies extra space on the top side of the toolbar's title.
              If both this attribute and titleMargin are specified, then this
              attribute takes precedence. Margin values should be positive. -->
        <attr format="dimension" name="titleMarginTop"/>
        <!--  Specifies extra space on the bottom side of the toolbar's title.
              If both this attribute and titleMargin are specified, then this
              attribute takes precedence. Margin values should be positive. -->
        <attr format="dimension" name="titleMarginBottom"/>
        <!-- {@deprecated Use titleMargin} -->
        <attr format="dimension" name="titleMargins"/>
        <attr name="contentInsetStart"/>
        <attr name="contentInsetEnd"/>
        <attr name="contentInsetLeft"/>
        <attr name="contentInsetRight"/>
        <attr name="contentInsetStartWithNavigation"/>
        <attr name="contentInsetEndWithActions"/>
        <attr format="dimension" name="maxButtonHeight"/>
        <attr name="buttonGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
        </attr>
        <!-- Icon drawable to use for the collapse button. -->
        <attr format="reference" name="collapseIcon"/>
        <!-- Text to set as the content description for the collapse button. -->
        <attr format="string" name="collapseContentDescription"/>
        <!-- Reference to a theme that should be used to inflate popups
             shown by widgets in the toolbar. -->
        <attr name="popupTheme"/>
        <!-- Icon drawable to use for the navigation button located at
             the start of the toolbar. -->
        <attr format="reference" name="navigationIcon"/>
        <!-- Text to set as the content description for the navigation button
             located at the start of the toolbar. -->
        <attr format="string" name="navigationContentDescription"/>
        <!-- Drawable to set as the logo that appears at the starting side of
             the Toolbar, just after the navigation button. -->
        <attr name="logo"/>
        <!-- A content description string to describe the appearance of the
             associated logo image. -->
        <attr format="string" name="logoDescription"/>
        <!-- A color to apply to the title string. -->
        <attr format="color" name="titleTextColor"/>
        <!-- A color to apply to the subtitle string. -->
        <attr format="color" name="subtitleTextColor"/>
        <attr name="android:minHeight"/>
    </declare-styleable>
    <declare-styleable name="View">
        <!-- Sets the padding, in pixels, of the start edge; see {@link android.R.attr#padding}. -->
        <attr format="dimension" name="paddingStart"/>
        <!-- Sets the padding, in pixels, of the end edge; see {@link android.R.attr#padding}. -->
        <attr format="dimension" name="paddingEnd"/>
        <!-- Boolean that controls whether a view can take focus.  By default the user can not
             move focus to a view; by setting this attribute to true the view is
             allowed to take focus.  This value does not impact the behavior of
             directly calling {@link android.view.View#requestFocus}, which will
             always request focus regardless of this view.  It only impacts where
             focus navigation will try to move focus. -->
        <attr name="android:focusable"/>
        <!-- Deprecated. -->
        <attr format="reference" name="theme"/>
        <!-- Specifies a theme override for a view. When a theme override is set, the
             view will be inflated using a {@link android.content.Context} themed with
             the specified resource. -->
        <attr name="android:theme"/>
    </declare-styleable>
    <declare-styleable name="ViewBackgroundHelper">
        <attr name="android:background"/>
        <!-- Tint to apply to the background. -->
        <attr format="color" name="backgroundTint"/>

        <!-- Blending mode used to apply the background tint. -->
        <attr name="backgroundTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ViewStubCompat">
        <!-- Supply an identifier for the layout resource to inflate when the ViewStub
             becomes visible or when forced to do so. The layout resource must be a
             valid reference to a layout. -->
        <attr name="android:layout"/>
        <!-- Overrides the id of the inflated View with this value. -->
        <attr name="android:inflatedId"/>
        <attr name="android:id"/>
    </declare-styleable>
</resources>