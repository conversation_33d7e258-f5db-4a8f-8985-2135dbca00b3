{"logs": [{"outputFile": "/Users/<USER>/paker/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-xlarge-v4/values-xlarge-v4.xml", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-xlarge/dimens.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,173,230", "endColumns": "58,58,56,56", "endOffsets": "109,168,225,282"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "197,256,315,372", "endColumns": "58,58,56,56", "endOffsets": "251,310,367,424"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-xlarge-v4/values-xlarge-v4.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,126", "endColumns": "70,70", "endOffsets": "121,192"}}]}]}