{"logs": [{"outputFile": "/Users/<USER>/paker/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-v16/values-v16.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/res/values-v16/values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v16/values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}]}]}