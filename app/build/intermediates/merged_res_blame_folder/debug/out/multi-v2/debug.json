{"logs": [{"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-bs_values-bs.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-bs/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,223,300,356,430,522,575,626,687,750,816,880,951,1014,1079,1148,1209,1270,1326,1399,1474,1542,1617,1701,1774,1880,1947,2002", "endColumns": "88,78,76,55,73,91,52,50,60,62,65,63,70,62,64,68,60,60,55,72,74,67,74,83,72,105,66,54,70", "endOffsets": "139,218,295,351,425,517,570,621,682,745,811,875,946,1009,1074,1143,1204,1265,1321,1394,1469,1537,1612,1696,1769,1875,1942,1997,2068"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v23_values-v23.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/e476e0dd04b5343b91450d154c8a112f/cardview-v7-28.0.0/res/values-v23/values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "2646", "endLines": "39", "endColumns": "12", "endOffsets": "2791"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v23/values-v23.xml", "from": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1277,2079,2206,2311,2426,2533", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1272,2074,2201,2306,2421,2528,2641"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-sw_values-sw.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-sw/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,196,274,334,409,496,549,602,663,726,790,854,925,988,1053,1118,1179,1241,1293,1364,1443,1512,1589,1669,1744,1839,1906,1959", "endColumns": "72,67,77,59,74,86,52,52,60,62,63,63,70,62,64,64,60,61,51,70,78,68,76,79,74,94,66,52,70", "endOffsets": "123,191,269,329,404,491,544,597,658,721,785,849,920,983,1048,1113,1174,1236,1288,1359,1438,1507,1584,1664,1739,1834,1901,1954,2025"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ta_values-ta.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ta/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,207,292,351,428,524,573,620,690,765,831,896,973,1045,1119,1184,1256,1324,1376,1448,1522,1589,1669,1742,1819,1909,1979,2030", "endColumns": "83,67,84,58,76,95,48,46,69,74,65,64,76,71,73,64,71,67,51,71,73,66,79,72,76,89,69,50,70", "endOffsets": "134,202,287,346,423,519,568,615,685,760,826,891,968,1040,1114,1179,1251,1319,1371,1443,1517,1584,1664,1737,1814,1904,1974,2025,2096"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-night-v8_values-night-v8.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-night/styles.xml", "from": {"startLines": "2,4,6,8,10,12,14", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,247,350,465,586,707", "endLines": "3,5,7,9,11,13,15", "endColumns": "12,12,12,12,12,12,12", "endOffsets": "139,242,345,460,581,702,815"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-et_values-et.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-et/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,219,300,356,428,515,566,614,676,740,815,887,967,1031,1102,1166,1228,1291,1344,1425,1500,1569,1649,1721,1794,1893,1965,2018", "endColumns": "86,76,80,55,71,86,50,47,61,63,74,71,79,63,70,63,61,62,52,80,74,68,79,71,72,98,71,52,70", "endOffsets": "137,214,295,351,423,510,561,609,671,735,810,882,962,1026,1097,1161,1223,1286,1339,1420,1495,1564,1644,1716,1789,1888,1960,2013,2084"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-te_values-te.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-te/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,218,299,359,434,529,582,634,695,758,824,888,959,1022,1087,1152,1213,1274,1328,1411,1489,1558,1639,1712,1799,1898,1969,2022", "endColumns": "83,78,80,59,74,94,52,51,60,62,65,63,70,62,64,64,60,60,53,82,77,68,80,72,86,98,70,52,70", "endOffsets": "134,213,294,354,429,524,577,629,690,753,819,883,954,1017,1082,1147,1208,1269,1323,1406,1484,1553,1634,1707,1794,1893,1964,2017,2088"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v17_values-v17.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v17/values-v17.xml", "from": {"startLines": "2,5,9,12,15,18,22,25,29,33,37,40,43,46,50,53,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,456,614,764,936,1161,1331,1559,1783,2025,2196,2370,2539,2812,3012,3216", "endLines": "4,8,11,14,17,21,24,28,32,36,39,42,45,49,52,56,60", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "223,451,609,759,931,1156,1326,1554,1778,2020,2191,2365,2534,2807,3007,3211,3540"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-sw600dp-v13_values-sw600dp-v13.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-sw600dp/styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "239"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1233", "endLines": "22", "endColumns": "12", "endOffsets": "1417"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-sw600dp/integers.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "62", "endOffsets": "113"}, "to": {"startLines": "18", "startColumns": "4", "startOffsets": "1170", "endColumns": "62", "endOffsets": "1228"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-sw600dp/dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543,611,671,741,812,884,942,1000,1106", "endColumns": "68,68,69,73,75,58,70,67,59,69,70,71,57,57,105,63", "endOffsets": "119,188,258,332,408,467,538,606,666,736,807,879,937,995,1101,1165"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-bn_values-bn.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-bn/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,216,292,356,431,530,579,627,688,751,816,880,951,1014,1079,1143,1204,1265,1322,1402,1480,1549,1629,1706,1789,1887,1962,2015", "endColumns": "78,81,75,63,74,98,48,47,60,62,64,63,70,62,64,63,60,60,56,79,77,68,79,76,82,97,74,52,70", "endOffsets": "129,211,287,351,426,525,574,622,683,746,811,875,946,1009,1074,1138,1199,1260,1317,1397,1475,1544,1624,1701,1784,1882,1957,2010,2081"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ky_values-ky.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ky/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,283,337,414,501,550,599,660,723,789,853,924,987,1052,1117,1178,1239,1290,1370,1447,1515,1591,1669,1740,1834,1907,1959", "endColumns": "73,64,88,53,76,86,48,48,60,62,65,63,70,62,64,64,60,60,50,79,76,67,75,77,70,93,72,51,70", "endOffsets": "124,189,278,332,409,496,545,594,655,718,784,848,919,982,1047,1112,1173,1234,1285,1365,1442,1510,1586,1664,1735,1829,1902,1954,2025"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-xlarge-v4_values-xlarge-v4.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-xlarge/dimens.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,173,230", "endColumns": "58,58,56,56", "endOffsets": "109,168,225,282"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "197,256,315,372", "endColumns": "58,58,56,56", "endOffsets": "251,310,367,424"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-xlarge-v4/values-xlarge-v4.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,126", "endColumns": "70,70", "endOffsets": "121,192"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-h720dp-v13_values-h720dp-v13.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-h720dp/dimens.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-be_values-be.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-be/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,218,304,360,438,526,576,624,686,750,816,880,946,1010,1076,1141,1203,1265,1318,1394,1470,1538,1616,1692,1767,1869,1939,1991", "endColumns": "89,72,85,55,77,87,49,47,61,63,65,63,65,63,65,64,61,61,52,75,75,67,77,75,74,101,69,51,70", "endOffsets": "140,213,299,355,433,521,571,619,681,745,811,875,941,1005,1071,1136,1198,1260,1313,1389,1465,1533,1611,1687,1762,1864,1934,1986,2057"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-it_values-it.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-it/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,204,283,337,416,511,559,605,667,731,795,859,931,995,1062,1138,1200,1262,1313,1389,1467,1535,1609,1685,1762,1858,1928,1982", "endColumns": "78,69,78,53,78,94,47,45,61,63,63,63,71,63,66,75,61,61,50,75,77,67,73,75,76,95,69,53,70", "endOffsets": "129,199,278,332,411,506,554,600,662,726,790,854,926,990,1057,1133,1195,1257,1308,1384,1462,1530,1604,1680,1757,1853,1923,1977,2048"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-zu_values-zu.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-zu/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,210,294,352,425,522,573,623,684,747,811,875,946,1009,1074,1138,1199,1262,1318,1392,1468,1536,1613,1690,1766,1856,1922,1974", "endColumns": "77,76,83,57,72,96,50,49,60,62,63,63,70,62,64,63,60,62,55,73,75,67,76,76,75,89,65,51,70", "endOffsets": "128,205,289,347,420,517,568,618,679,742,806,870,941,1004,1069,1133,1194,1257,1313,1387,1463,1531,1608,1685,1761,1851,1917,1969,2040"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-cs_values-cs.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-cs/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,204,283,339,414,501,553,604,665,728,794,858,923,986,1051,1118,1179,1240,1294,1368,1447,1516,1592,1673,1750,1846,1914,1967", "endColumns": "76,71,78,55,74,86,51,50,60,62,65,63,64,62,64,66,60,60,53,73,78,68,75,80,76,95,67,52,70", "endOffsets": "127,199,278,334,409,496,548,599,660,723,789,853,918,981,1046,1113,1174,1235,1289,1363,1442,1511,1587,1668,1745,1841,1909,1962,2033"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-as_values-as.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-as/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,202,279,345,417,506,557,607,668,731,794,859,929,992,1059,1123,1184,1245,1304,1376,1461,1534,1613,1703,1793,1893,1966,2026", "endColumns": "77,68,76,65,71,88,50,49,60,62,62,64,69,62,66,63,60,60,58,71,84,72,78,89,89,99,72,59,70", "endOffsets": "128,197,274,340,412,501,552,602,663,726,789,854,924,987,1054,1118,1179,1240,1299,1371,1456,1529,1608,1698,1788,1888,1961,2021,2092"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-hy_values-hy.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-hy/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,199,279,338,414,499,552,603,664,727,793,857,928,991,1056,1120,1181,1242,1297,1374,1451,1520,1600,1678,1748,1838,1907,1960", "endColumns": "72,70,79,58,75,84,52,50,60,62,65,63,70,62,64,63,60,60,54,76,76,68,79,77,69,89,68,52,70", "endOffsets": "123,194,274,333,409,494,547,598,659,722,788,852,923,986,1051,1115,1176,1237,1292,1369,1446,1515,1595,1673,1743,1833,1902,1955,2026"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-sv_values-sv.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-sv/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,204,285,340,412,495,542,588,651,716,782,846,919,984,1051,1119,1185,1248,1298,1374,1444,1510,1585,1658,1730,1817,1889,1939", "endColumns": "75,72,80,54,71,82,46,45,62,64,65,63,72,64,66,67,65,62,49,75,69,65,74,72,71,86,71,49,70", "endOffsets": "126,199,280,335,407,490,537,583,646,711,777,841,914,979,1046,1114,1180,1243,1293,1369,1439,1505,1580,1653,1725,1812,1884,1934,2005"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-zh-rCN/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,186,256,308,375,451,498,544,605,668,736,802,867,930,995,1057,1118,1179,1227,1293,1359,1424,1491,1557,1625,1707,1771,1820", "endColumns": "65,64,69,51,66,75,46,45,60,62,67,65,64,62,64,61,60,60,47,65,65,64,66,65,67,81,63,48,70", "endOffsets": "116,181,251,303,370,446,493,539,600,663,731,797,862,925,990,1052,1113,1174,1222,1288,1354,1419,1486,1552,1620,1702,1766,1815,1886"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-de_values-de.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-de/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,198,280,336,411,496,544,590,652,716,783,854,932,1002,1076,1146,1214,1281,1333,1414,1487,1556,1637,1710,1787,1876,1948,2001", "endColumns": "74,67,81,55,74,84,47,45,61,63,66,70,77,69,73,69,67,66,51,80,72,68,80,72,76,88,71,52,70", "endOffsets": "125,193,275,331,406,491,539,585,647,711,778,849,927,997,1071,1141,1209,1276,1328,1409,1482,1551,1632,1705,1782,1871,1943,1996,2067"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-mdpi-v4_values-mdpi-v4.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-mdpi/bools.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "45", "endOffsets": "96"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-mdpi/dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,94,130,166,205,242,281,318,355,392,431,468,507,544,581,618,657,694,729,768,805,842,879,918,955,994,1033,1072,1109,1144,1181,1218,1255,1294,1329,1366,1401,1438,1475,1510,1547,1584,1619,1656,1691,1728,1763,1800", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "89,125,161,200,237,276,313,350,387,426,463,502,539,576,613,652,689,724,763,800,837,874,913,950,989,1028,1067,1104,1139,1176,1213,1250,1289,1324,1361,1396,1433,1470,1505,1542,1579,1614,1651,1686,1723,1758,1795,1830"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "101,140,176,212,251,288,327,364,401,438,477,514,553,590,627,664,703,740,775,814,851,888,925,964,1001,1040,1079,1118,1155,1190,1227,1264,1301,1340,1375,1412,1447,1484,1521,1556,1593,1630,1665,1702,1737,1774,1809,1846", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "135,171,207,246,283,322,359,396,433,472,509,548,585,622,659,698,735,770,809,846,883,920,959,996,1035,1074,1113,1150,1185,1222,1259,1296,1335,1370,1407,1442,1479,1516,1551,1588,1625,1660,1697,1732,1769,1804,1841,1876"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-fr-rCA/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,221,302,359,445,545,599,649,710,773,842,907,978,1041,1104,1169,1230,1291,1357,1437,1519,1592,1673,1751,1823,1915,1984,2041", "endColumns": "80,84,80,56,85,99,53,49,60,62,68,64,70,62,62,64,60,60,65,79,81,72,80,77,71,91,68,56,70", "endOffsets": "131,216,297,354,440,540,594,644,705,768,837,902,973,1036,1099,1164,1225,1286,1352,1432,1514,1587,1668,1746,1818,1910,1979,2036,2107"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-pa_values-pa.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-pa/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,195,270,326,396,479,527,573,634,697,763,827,898,961,1026,1090,1151,1212,1261,1332,1406,1473,1552,1622,1702,1794,1864,1915", "endColumns": "72,66,74,55,69,82,47,45,60,62,65,63,70,62,64,63,60,60,48,70,73,66,78,69,79,91,69,50,70", "endOffsets": "123,190,265,321,391,474,522,568,629,692,758,822,893,956,1021,1085,1146,1207,1256,1327,1401,1468,1547,1617,1697,1789,1859,1910,1981"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ur_values-ur.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ur/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,211,290,346,420,510,557,603,665,729,795,859,931,995,1061,1125,1187,1249,1304,1382,1461,1533,1614,1685,1763,1861,1929,1980", "endColumns": "79,75,78,55,73,89,46,45,61,63,65,63,71,63,65,63,61,61,54,77,78,71,80,70,77,97,67,50,70", "endOffsets": "130,206,285,341,415,505,552,598,660,724,790,854,926,990,1056,1120,1182,1244,1299,1377,1456,1528,1609,1680,1758,1856,1924,1975,2046"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-el_values-el.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-el/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,224,311,366,441,537,596,652,713,776,842,906,977,1040,1105,1172,1233,1294,1349,1430,1510,1582,1663,1743,1821,1914,1984,2040", "endColumns": "87,80,86,54,74,95,58,55,60,62,65,63,70,62,64,66,60,60,54,80,79,71,80,79,77,92,69,55,70", "endOffsets": "138,219,306,361,436,532,591,647,708,771,837,901,972,1035,1100,1167,1228,1289,1344,1425,1505,1577,1658,1738,1816,1909,1979,2035,2106"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-eu_values-eu.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-eu/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,202,282,338,414,508,566,620,682,746,813,877,949,1013,1079,1146,1208,1271,1323,1402,1482,1551,1630,1707,1788,1892,1961,2014", "endColumns": "78,67,79,55,75,93,57,53,61,63,66,63,71,63,65,66,61,62,51,78,79,68,78,76,80,103,68,52,70", "endOffsets": "129,197,277,333,409,503,561,615,677,741,808,872,944,1008,1074,1141,1203,1266,1318,1397,1477,1546,1625,1702,1783,1887,1956,2009,2080"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ro_values-ro.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ro/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,220,303,361,443,534,590,641,702,765,831,895,965,1028,1093,1158,1219,1281,1334,1416,1499,1569,1653,1729,1805,1902,1975,2029", "endColumns": "90,73,82,57,81,90,55,50,60,62,65,63,69,62,64,64,60,61,52,81,82,69,83,75,75,96,72,53,70", "endOffsets": "141,215,298,356,438,529,585,636,697,760,826,890,960,1023,1088,1153,1214,1276,1329,1411,1494,1564,1648,1724,1800,1897,1970,2024,2095"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-lt_values-lt.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-lt/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,141,212,295,352,431,522,575,626,690,756,824,890,964,1030,1098,1171,1235,1299,1354,1433,1512,1582,1662,1737,1820,1929,2000,2054", "endColumns": "85,70,82,56,78,90,52,50,63,65,67,65,73,65,67,72,63,63,54,78,78,69,79,74,82,108,70,53,70", "endOffsets": "136,207,290,347,426,517,570,621,685,751,819,885,959,1025,1093,1166,1230,1294,1349,1428,1507,1577,1657,1732,1815,1924,1995,2049,2120"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-large-v4_values-large-v4.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-large/styles.xml", "from": {"startLines": "2,4,6,8", "startColumns": "4,4,4,4", "startOffsets": "55,178,313,454", "endLines": "3,5,7,9", "endColumns": "12,12,12,12", "endOffsets": "173,308,449,602"}, "to": {"startLines": "9,11,13,15", "startColumns": "4,4,4,4", "startOffsets": "466,589,724,865", "endLines": "10,12,14,16", "endColumns": "12,12,12,12", "endOffsets": "584,719,860,1013"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-large/dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,114,174,234,293,352,409", "endColumns": "58,59,59,58,58,56,56", "endOffsets": "109,169,229,288,347,404,461"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-km_values-km.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-km/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,198,280,337,411,499,547,594,655,718,784,848,919,982,1047,1111,1172,1233,1286,1360,1435,1505,1585,1663,1741,1836,1904,1958", "endColumns": "72,69,81,56,73,87,47,46,60,62,65,63,70,62,64,63,60,60,52,73,74,69,79,77,77,94,67,53,70", "endOffsets": "123,193,275,332,406,494,542,589,650,713,779,843,914,977,1042,1106,1167,1228,1281,1355,1430,1500,1580,1658,1736,1831,1899,1953,2024"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-gl_values-gl.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-gl/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,219,297,352,424,520,575,626,688,752,820,884,955,1019,1085,1150,1212,1274,1326,1403,1482,1551,1629,1704,1781,1873,1943,1996", "endColumns": "81,81,77,54,71,95,54,50,61,63,67,63,70,63,65,64,61,61,51,76,78,68,77,74,76,91,69,52,73", "endOffsets": "132,214,292,347,419,515,570,621,683,747,815,879,950,1014,1080,1145,1207,1269,1321,1398,1477,1546,1624,1699,1776,1868,1938,1991,2065"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,219,297,352,424,520,575,626,688,752,820,884,955,1019,1085,1150,1212,1274,1326,1403,1482,1551,1629,1704,1781,1873,1943,1996", "endColumns": "81,81,77,54,71,95,54,50,61,63,67,63,70,63,65,64,61,61,51,76,78,68,77,74,76,91,69,52,70", "endOffsets": "132,214,292,347,419,515,570,621,683,747,815,879,950,1014,1080,1145,1207,1269,1321,1398,1477,1546,1624,1699,1776,1868,1938,1991,2062"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ne_values-ne.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ne/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,204,282,343,420,517,582,642,703,766,832,896,967,1030,1095,1159,1220,1281,1339,1419,1505,1578,1663,1736,1821,1925,2007,2064", "endColumns": "74,73,77,60,76,96,64,59,60,62,65,63,70,62,64,63,60,60,57,79,85,72,84,72,84,103,81,56,70", "endOffsets": "125,199,277,338,415,512,577,637,698,761,827,891,962,1025,1090,1154,1215,1276,1334,1414,1500,1573,1658,1731,1816,1920,2002,2059,2130"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ml_values-ml.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ml/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,232,317,380,455,557,605,651,712,775,847,911,982,1046,1111,1180,1241,1302,1354,1435,1511,1579,1663,1734,1815,1907,1978,2031", "endColumns": "88,87,84,62,74,101,47,45,60,62,71,63,70,63,64,68,60,60,51,80,75,67,83,70,80,91,70,52,70", "endOffsets": "139,227,312,375,450,552,600,646,707,770,842,906,977,1041,1106,1175,1236,1297,1349,1430,1506,1574,1658,1729,1810,1902,1973,2026,2097"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-mr_values-mr.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-mr/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,212,289,349,420,505,553,601,662,725,789,856,927,990,1055,1122,1183,1244,1294,1376,1448,1514,1593,1665,1747,1837,1912,1963", "endColumns": "80,75,76,59,70,84,47,47,60,62,63,66,70,62,64,66,60,60,49,81,71,65,78,71,81,89,74,50,70", "endOffsets": "131,207,284,344,415,500,548,596,657,720,784,851,922,985,1050,1117,1178,1239,1289,1371,1443,1509,1588,1660,1742,1832,1907,1958,2029"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-si_values-si.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-si/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,212,289,347,422,508,568,625,686,749,814,878,949,1012,1077,1141,1202,1263,1317,1396,1471,1539,1619,1689,1765,1857,1926,1979", "endColumns": "79,76,76,57,74,85,59,56,60,62,64,63,70,62,64,63,60,60,53,78,74,67,79,69,75,91,68,52,70", "endOffsets": "130,207,284,342,417,503,563,620,681,744,809,873,944,1007,1072,1136,1197,1258,1312,1391,1466,1534,1614,1684,1760,1852,1921,1974,2045"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-sr_values-sr.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-sr/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,207,283,339,413,501,553,603,664,727,793,857,928,991,1056,1131,1192,1253,1311,1386,1464,1535,1609,1688,1759,1859,1926,1981", "endColumns": "78,72,75,55,73,87,51,49,60,62,65,63,70,62,64,74,60,60,57,74,77,70,73,78,70,99,66,54,70", "endOffsets": "129,202,278,334,408,496,548,598,659,722,788,852,923,986,1051,1126,1187,1248,1306,1381,1459,1530,1604,1683,1754,1854,1921,1976,2047"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-zh-rHK/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,183,253,305,372,450,497,543,605,669,731,797,863,927,993,1055,1117,1179,1227,1293,1359,1424,1491,1557,1625,1709,1773,1822", "endColumns": "64,62,69,51,66,77,46,45,61,63,61,65,65,63,65,61,61,61,47,65,65,64,66,65,67,83,63,48,70", "endOffsets": "115,178,248,300,367,445,492,538,600,664,726,792,858,922,988,1050,1112,1174,1222,1288,1354,1419,1486,1552,1620,1704,1768,1817,1888"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-or_values-or.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-or/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,224,301,357,431,521,569,616,677,740,807,872,943,1006,1071,1137,1198,1258,1310,1390,1465,1541,1622,1696,1784,1880,1952,2012", "endColumns": "88,79,76,55,73,89,47,46,60,62,66,64,70,62,64,65,60,59,51,79,74,75,80,73,87,95,71,59,70", "endOffsets": "139,219,296,352,426,516,564,611,672,735,802,867,938,1001,1066,1132,1193,1253,1305,1385,1460,1536,1617,1691,1779,1875,1947,2007,2078"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ca_values-ca.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ca/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,219,296,349,427,523,578,629,690,753,817,882,951,1014,1077,1141,1202,1263,1316,1397,1476,1544,1624,1699,1777,1870,1939,1991", "endColumns": "87,75,76,52,77,95,54,50,60,62,63,64,68,62,62,63,60,60,52,80,78,67,79,74,77,92,68,51,70", "endOffsets": "138,214,291,344,422,518,573,624,685,748,812,877,946,1009,1072,1136,1197,1258,1311,1392,1471,1539,1619,1694,1772,1865,1934,1986,2057"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-port_values-port.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-port/bools.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ru_values-ru.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ru/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,140,212,294,350,425,515,565,613,675,739,805,868,934,998,1064,1129,1191,1253,1303,1379,1457,1525,1604,1681,1759,1867,1937,1989", "endColumns": "84,71,81,55,74,89,49,47,61,63,65,62,65,63,65,64,61,61,49,75,77,67,78,76,77,107,69,51,70", "endOffsets": "135,207,289,345,420,510,560,608,670,734,800,863,929,993,1059,1124,1186,1248,1298,1374,1452,1520,1599,1676,1754,1862,1932,1984,2055"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-af_values-af.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-af/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,199,275,330,403,491,538,585,646,709,775,839,909,972,1037,1106,1171,1235,1286,1363,1436,1503,1581,1654,1726,1813,1881,1932", "endColumns": "73,69,75,54,72,87,46,46,60,62,65,63,69,62,64,68,64,63,50,76,72,66,77,72,71,86,67,50,70", "endOffsets": "124,194,270,325,398,486,533,580,641,704,770,834,904,967,1032,1101,1166,1230,1281,1358,1431,1498,1576,1649,1721,1808,1876,1927,1998"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-lo_values-lo.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-lo/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,195,272,327,402,484,532,580,641,704,770,834,905,968,1033,1097,1158,1219,1269,1346,1413,1481,1556,1630,1704,1794,1860,1912", "endColumns": "72,66,76,54,74,81,47,47,60,62,65,63,70,62,64,63,60,60,49,76,66,67,74,73,73,89,65,51,70", "endOffsets": "123,190,267,322,397,479,527,575,636,699,765,829,900,963,1028,1092,1153,1214,1264,1341,1408,1476,1551,1625,1699,1789,1855,1907,1978"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-land_values-land.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-land/dimens.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-land/styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "239"}, "to": {"startLines": "5", "startColumns": "4", "startOffsets": "264", "endLines": "8", "endColumns": "12", "endOffsets": "448"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-gu_values-gu.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-gu/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,208,285,342,413,506,554,602,663,726,795,859,930,993,1058,1125,1186,1247,1297,1373,1445,1512,1591,1661,1741,1834,1907,1958", "endColumns": "78,73,76,56,70,92,47,47,60,62,68,63,70,62,64,66,60,60,49,75,71,66,78,69,79,92,72,50,70", "endOffsets": "129,203,280,337,408,501,549,597,658,721,790,854,925,988,1053,1120,1181,1242,1292,1368,1440,1507,1586,1656,1736,1829,1902,1953,2024"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-fi_values-fi.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-fi/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,203,274,330,405,493,551,603,664,727,793,857,922,985,1051,1120,1181,1245,1295,1372,1445,1512,1588,1658,1732,1828,1897,1948", "endColumns": "77,69,70,55,74,87,57,51,60,62,65,63,64,62,65,68,60,63,49,76,72,66,75,69,73,95,68,50,70", "endOffsets": "128,198,269,325,400,488,546,598,659,722,788,852,917,980,1046,1115,1176,1240,1290,1367,1440,1507,1583,1653,1727,1823,1892,1943,2014"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-watch-v21_values-watch-v21.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-watch-v21/values-watch-v21.xml", "from": {"startLines": "2,6,10", "startColumns": "4,4,4", "startOffsets": "55,271,499", "endLines": "5,9,13", "endColumns": "12,12,12", "endOffsets": "266,494,724"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ka_values-ka.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ka/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,208,289,347,422,505,560,612,673,736,801,867,938,1001,1066,1130,1191,1252,1305,1388,1465,1533,1616,1691,1765,1855,1923,1975", "endColumns": "78,73,80,57,74,82,54,51,60,62,64,65,70,62,64,63,60,60,52,82,76,67,82,74,73,89,67,51,70", "endOffsets": "129,203,284,342,417,500,555,607,668,731,796,862,933,996,1061,1125,1186,1247,1300,1383,1460,1528,1611,1686,1760,1850,1918,1970,2041"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v25_values-v25.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v25/values-v25.xml", "from": {"startLines": "2,3,4,6", "startColumns": "4,4,4,4", "startOffsets": "55,126,209,308", "endLines": "2,3,5,7", "endColumns": "70,82,12,12", "endOffsets": "121,204,303,414"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v21_values-v21.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/8fd5d53888178dd9013834ef7b031501/support-media-compat-28.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "265,268,272,276", "startColumns": "4,4,4,4", "startOffsets": "19504,19672,19961,20257", "endLines": "267,270,274,278", "endColumns": "12,12,12,12", "endOffsets": "19667,19830,20124,20419"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,354,470,596,722,850,1022", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,465,591,717,845,1017,1355"}, "to": {"startLines": "2,3,4,5,263,264,271,275,311,314", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,19262,19378,19835,20129,22137,22309", "endLines": "2,3,4,5,263,264,271,275,313,318", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,19373,19499,19956,20252,22304,22642"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,10,18,26", "startColumns": "4,4,4,4", "startOffsets": "55,484,910,1333", "endLines": "9,17,25,33", "endColumns": "10,10,10,10", "endOffsets": "479,905,1328,1763"}, "to": {"startLines": "279,287,295,303", "startColumns": "4,4,4,4", "startOffsets": "20424,20853,21279,21702", "endLines": "286,294,302,310", "endColumns": "10,10,10,10", "endOffsets": "20848,21274,21697,22132"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,107,110,154,157,160,162,164,166,169,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,221,222,223,233,234,235,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4234,4383,4532,4644,4791,4944,5091,5166,5255,5342,5443,5546,8614,8799,11879,12076,12275,12398,12521,12634,12817,12948,13149,13238,13349,13582,13683,13778,13901,14030,14147,14324,14423,14558,14701,14836,14955,15156,15275,15368,15479,15535,15642,15837,15948,16081,16176,16267,16358,16475,16614,16685,16768,17448,17505,17563,18257", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,106,109,153,156,159,161,163,165,168,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,220,221,222,232,233,234,246,258", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4229,4378,4527,4639,4786,4939,5086,5161,5250,5337,5438,5541,8609,8794,11874,12071,12270,12393,12516,12629,12812,12943,13144,13233,13344,13577,13678,13773,13896,14025,14142,14319,14418,14553,14696,14831,14950,15151,15270,15363,15474,15530,15637,15832,15943,16076,16171,16262,16353,16470,16609,16680,16763,17443,17500,17558,18252,18958"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,24,25,26,28,30,31,32,33,34,36,38,40,42,44,46,47,52,54,56,57,58,60,62,63,64,65,66,67,111,114,158,161,164,166,168,170,173,175,178,179,180,183,184,185,186,187,188,191,192,194,196,198,200,204,206,207,208,209,211,215,217,219,220,221,222,223,225,226,227,237,238,239,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "354,445,548,651,756,863,972,1081,1190,1299,1408,1515,1618,1737,1892,2047,2152,2273,2374,2521,2662,2765,2884,2991,3094,3249,3420,3569,3734,3891,4042,4161,4533,4682,4831,4943,5090,5243,5390,5465,5554,5641,5742,5845,8913,9098,12178,12375,12574,12697,12820,12933,13116,13247,13448,13537,13648,13881,13982,14077,14200,14329,14446,14623,14722,14857,15000,15135,15254,15455,15574,15667,15778,15834,15941,16136,16247,16380,16475,16566,16657,16774,16913,16984,17067,17747,17804,17862,18556", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,23,24,25,27,29,30,31,32,33,35,37,39,41,43,45,46,51,53,55,56,57,59,61,62,63,64,65,66,110,113,157,160,163,165,167,169,172,174,177,178,179,182,183,184,185,186,187,190,191,193,195,197,199,203,205,206,207,208,210,214,216,218,219,220,221,222,224,225,226,236,237,238,250,262", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "440,543,646,751,858,967,1076,1185,1294,1403,1510,1613,1732,1887,2042,2147,2268,2369,2516,2657,2760,2879,2986,3089,3244,3415,3564,3729,3886,4037,4156,4528,4677,4826,4938,5085,5238,5385,5460,5549,5636,5737,5840,8908,9093,12173,12370,12569,12692,12815,12928,13111,13242,13443,13532,13643,13876,13977,14072,14195,14324,14441,14618,14717,14852,14995,15130,15249,15450,15569,15662,15773,15829,15936,16131,16242,16375,16470,16561,16652,16769,16908,16979,17062,17742,17799,17857,18551,19257"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ja_values-ja.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ja/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,185,260,312,380,458,506,552,613,676,742,806,877,940,1005,1069,1130,1191,1239,1311,1380,1445,1518,1584,1650,1731,1798,1847", "endColumns": "66,62,74,51,67,77,47,45,60,62,65,63,70,62,64,63,60,60,47,71,68,64,72,65,65,80,66,48,70", "endOffsets": "117,180,255,307,375,453,501,547,608,671,737,801,872,935,1000,1064,1125,1186,1234,1306,1375,1440,1513,1579,1645,1726,1793,1842,1913"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-hr_values-hr.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-hr/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,197,277,333,407,496,551,604,665,728,794,858,929,992,1057,1126,1187,1248,1304,1378,1461,1537,1612,1696,1773,1875,1942,2002", "endColumns": "74,66,79,55,73,88,54,52,60,62,65,63,70,62,64,68,60,60,55,73,82,75,74,83,76,101,66,59,70", "endOffsets": "125,192,272,328,402,491,546,599,660,723,789,853,924,987,1052,1121,1182,1243,1299,1373,1456,1532,1607,1691,1768,1870,1937,1997,2068"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-pt_values-pt.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-pt/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,221,298,357,428,516,570,620,681,744,810,874,945,1008,1073,1138,1199,1260,1317,1394,1476,1548,1626,1704,1784,1879,1949,2005", "endColumns": "89,75,76,58,70,87,53,49,60,62,65,63,70,62,64,64,60,60,56,76,81,71,77,77,79,94,69,55,70", "endOffsets": "140,216,293,352,423,511,565,615,676,739,805,869,940,1003,1068,1133,1194,1255,1312,1389,1471,1543,1621,1699,1779,1874,1944,2000,2071"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-hi_values-hi.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-hi/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,198,278,334,406,498,546,594,655,718,784,848,919,982,1047,1111,1172,1233,1293,1372,1445,1517,1597,1669,1751,1846,1917,1968", "endColumns": "75,66,79,55,71,91,47,47,60,62,65,63,70,62,64,63,60,60,59,78,72,71,79,71,81,94,70,50,70", "endOffsets": "126,193,273,329,401,493,541,589,650,713,779,843,914,977,1042,1106,1167,1228,1288,1367,1440,1512,1592,1664,1746,1841,1912,1963,2034"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-in_values-in.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-in/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,207,282,339,413,499,552,601,662,725,791,855,926,989,1054,1118,1179,1240,1296,1369,1448,1519,1593,1672,1750,1839,1908,1963", "endColumns": "79,71,74,56,73,85,52,48,60,62,65,63,70,62,64,63,60,60,55,72,78,70,73,78,77,88,68,54,70", "endOffsets": "130,202,277,334,408,494,547,596,657,720,786,850,921,984,1049,1113,1174,1235,1291,1364,1443,1514,1588,1667,1745,1834,1903,1958,2029"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-iw_values-iw.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-iw/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,198,276,330,400,484,533,581,642,706,772,836,907,970,1035,1102,1163,1225,1276,1348,1422,1490,1563,1635,1705,1790,1856,1908", "endColumns": "73,68,77,53,69,83,48,47,60,63,65,63,70,62,64,66,60,61,50,71,73,67,72,71,69,84,65,51,70", "endOffsets": "124,193,271,325,395,479,528,576,637,701,767,831,902,965,1030,1097,1158,1220,1271,1343,1417,1485,1558,1630,1700,1785,1851,1903,1974"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-fr_values-fr.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-fr/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,221,302,354,430,530,584,634,695,758,827,892,963,1026,1089,1154,1215,1276,1332,1412,1494,1567,1648,1726,1803,1895,1964,2021", "endColumns": "80,84,80,51,75,99,53,49,60,62,68,64,70,62,62,64,60,60,55,79,81,72,80,77,76,91,68,56,70", "endOffsets": "131,216,297,349,425,525,579,629,690,753,822,887,958,1021,1084,1149,1210,1271,1327,1407,1489,1562,1643,1721,1798,1890,1959,2016,2087"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values_values.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,21,23,47,58,59,60,61,62,63,68,69,70,71,75,76,77,78,79,80,81,82,107,108,109,110,112,113,114,115,117,118,121,122,123,124,125,126,127,128,129,130,131,132,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,410,411,412,413,415,422,423,429,444,547,548,549,550,551,567,575,576,580,584,595,600,606,613,617,621,626,630,634,638,642,646,650,656,660,666,670,676,680,685,689,692,696,702,706,712,716,722,725,729,733,737,741,745,746,747,748,751,754,757,760,764,765,766,767,768,771,773,775,777,782,783,787,793,797,798,800,811,812,816,822,826,880,881,885,912,916,917,921,1171,1338,1364,1532,1558,1589,1597,1603,1617,1639,1644,1649,1659,1668,1677,1681,1688,1696,1703,1704,1713,1716,1719,1723,1727,1731,1734,1735,1739,1743,1753,1758,1765,1771,1772,1775,1779,1784,1786,1788,1791,1794,1796,1800,1803,1810,1813,1816,1820,1822,1826,1828,1830,1832,1836,1844,1852,1864,1870,1879,1882,1893,1896,1901,1902,1988,2046,2109,2110,2120,2129,2130,2132,2136,2139,2142,2145,2148,2151,2154,2157,2161,2164,2167,2170,2174,2177,2181,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2207,2209,2210,2211,2212,2213,2214,2215,2216,2218,2219,2221,2222,2224,2226,2227,2229,2230,2231,2232,2233,2234,2236,2237,2238,2239,2240,2395,2397,2399,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2415,2416,2417,2418,2419,2420,2422,2426,2495,2496,2497,2498,2499,2500,2501,2528,2530,2532,2534,2536,2538,2539,2540,2541,2543,2545,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2561,2562,2563,2564,2566,2568,2569,2571,2572,2574,2576,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2591,2592,2593,2594,2596,2597,2598,2599,2600,2602,2604,2606,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,931,1029,2401,2954,3035,3096,3171,3247,3324,3585,3670,3752,3828,4036,4113,4191,4297,4403,4482,4562,4619,6109,6183,6258,6323,6444,6504,6565,6637,6769,6836,6995,7054,7113,7172,7231,7290,7344,7398,7451,7505,7559,7613,8924,8998,9077,9150,9224,9295,9367,9439,9550,9607,9665,9738,9812,9886,9961,10033,10106,10176,10247,10307,25306,25363,25411,25460,25551,25884,25931,26215,26942,34045,34123,34213,34301,34397,35377,35959,36048,36295,36576,37242,37527,37920,38397,38619,38841,39117,39344,39574,39804,40034,40264,40491,40910,41136,41561,41791,42219,42438,42721,42929,43060,43287,43713,43938,44365,44586,45011,45131,45407,45708,46032,46323,46637,46774,46905,47010,47252,47419,47623,47831,48102,48214,48326,48431,48548,48762,48908,49048,49134,49482,49570,49816,50234,50483,50565,50663,51255,51355,51607,52031,52286,56088,56177,56414,58438,58680,58782,59035,77052,87181,88697,98925,100453,102210,102836,103256,104317,105582,105838,106074,106621,107115,107720,107918,108498,109062,109437,109555,110093,110250,110446,110719,110975,111145,111286,111350,111632,111918,112594,112858,113196,113549,113643,113829,114135,114397,114522,114649,114888,115099,115218,115411,115588,116043,116224,116346,116605,116718,116905,117007,117114,117243,117518,118026,118522,119399,119693,120263,120412,121144,121316,121652,121744,126069,130351,135120,135182,135760,136344,136435,136548,136777,136937,137089,137260,137426,137595,137762,137925,138168,138338,138511,138682,138956,139155,139360,139690,139774,139870,139966,140064,140164,140266,140368,140470,140572,140674,140774,140870,140982,141111,141234,141365,141496,141594,141708,141802,141942,142076,142172,142284,142384,142500,142596,142708,142808,142948,143084,143248,143378,143536,143686,143827,143971,144106,144218,144368,144496,144624,144760,144892,145022,145152,145264,153878,154024,154168,154306,154372,154462,154538,154642,154732,154834,154942,155050,155150,155230,155322,155420,155530,155608,155714,155806,155910,156020,156142,156305,160790,160870,160970,161060,161170,161264,161370,163292,163392,163504,163618,163734,163850,163944,164058,164170,164272,164392,164514,164596,164700,164820,164946,165044,165138,165226,165338,165454,165576,165688,165863,165979,166065,166157,166269,166393,166460,166586,166654,166782,166926,167054,167123,167218,167333,167446,167545,167654,167765,167876,167977,168082,168182,168312,168403,168526,168620,168732,168818,168922,169018,169106,169224,169328,169432,169558,169646,169754,169854,169944,170054,170138,170240,170324,170378,170442,170548,170658,170742", "endLines": "19,21,23,47,58,59,60,61,62,63,68,69,70,71,75,76,77,78,79,80,81,82,107,108,109,110,112,113,114,115,117,118,121,122,123,124,125,126,127,128,129,130,131,132,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,410,411,412,413,415,422,423,429,444,547,548,549,550,551,574,575,579,583,587,599,605,612,616,620,625,629,633,637,641,645,649,655,659,665,669,675,679,684,688,691,695,701,705,711,715,721,724,728,732,736,740,744,745,746,747,750,753,756,759,763,764,765,766,767,770,772,774,776,781,782,786,792,796,797,799,810,811,815,821,825,826,880,884,911,915,916,920,948,1337,1363,1531,1557,1588,1596,1602,1616,1638,1643,1648,1658,1667,1676,1680,1687,1695,1702,1703,1712,1715,1718,1722,1726,1730,1733,1734,1738,1742,1752,1757,1764,1770,1771,1774,1778,1783,1785,1787,1790,1793,1795,1799,1802,1809,1812,1815,1819,1821,1825,1827,1829,1831,1835,1843,1851,1863,1869,1878,1881,1892,1895,1900,1901,1906,2045,2104,2109,2119,2128,2129,2131,2135,2138,2141,2144,2147,2150,2153,2156,2160,2163,2166,2169,2173,2176,2180,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2206,2208,2209,2210,2211,2212,2213,2214,2215,2217,2218,2220,2221,2223,2225,2226,2228,2229,2230,2231,2232,2233,2235,2236,2237,2238,2239,2240,2396,2398,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2414,2415,2416,2417,2418,2419,2421,2425,2429,2495,2496,2497,2498,2499,2500,2501,2529,2531,2533,2535,2537,2538,2539,2540,2542,2544,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2560,2561,2562,2563,2565,2567,2568,2570,2571,2573,2575,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2590,2591,2592,2593,2595,2596,2597,2598,2599,2601,2603,2605,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621", "endColumns": "54,44,48,40,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,56,47,48,50,33,46,48,45,31,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119", "endOffsets": "881,971,1073,2437,3030,3091,3166,3242,3319,3397,3665,3747,3823,3899,4108,4186,4292,4398,4477,4557,4614,4672,6178,6253,6318,6384,6499,6560,6632,6705,6831,6899,7049,7108,7167,7226,7285,7339,7393,7446,7500,7554,7608,7662,8993,9072,9145,9219,9290,9362,9434,9507,9602,9660,9733,9807,9881,9956,10028,10101,10171,10242,10302,10363,25358,25406,25455,25506,25580,25926,25975,26256,26969,34118,34208,34296,34392,34482,35954,36043,36290,36571,36823,37522,37915,38392,38614,38836,39112,39339,39569,39799,40029,40259,40486,40905,41131,41556,41786,42214,42433,42716,42924,43055,43282,43708,43933,44360,44581,45006,45126,45402,45703,46027,46318,46632,46769,46900,47005,47247,47414,47618,47826,48097,48209,48321,48426,48543,48757,48903,49043,49129,49477,49565,49811,50229,50478,50560,50658,51250,51350,51602,52026,52281,52375,56172,56409,58433,58675,58777,59030,61186,87176,88692,98920,100448,102205,102831,103251,104312,105577,105833,106069,106616,107110,107715,107913,108493,109057,109432,109550,110088,110245,110441,110714,110970,111140,111281,111345,111627,111913,112589,112853,113191,113544,113638,113824,114130,114392,114517,114644,114883,115094,115213,115406,115583,116038,116219,116341,116600,116713,116900,117002,117109,117238,117513,118021,118517,119394,119688,120258,120407,121139,121311,121647,121739,122017,130346,134771,135177,135755,136339,136430,136543,136772,136932,137084,137255,137421,137590,137757,137920,138163,138333,138506,138677,138951,139150,139355,139685,139769,139865,139961,140059,140159,140261,140363,140465,140567,140669,140769,140865,140977,141106,141229,141360,141491,141589,141703,141797,141937,142071,142167,142279,142379,142495,142591,142703,142803,142943,143079,143243,143373,143531,143681,143822,143966,144101,144213,144363,144491,144619,144755,144887,145017,145147,145259,145399,154019,154163,154301,154367,154457,154533,154637,154727,154829,154937,155045,155145,155225,155317,155415,155525,155603,155709,155801,155905,156015,156137,156300,156457,160865,160965,161055,161165,161259,161365,161457,163387,163499,163613,163729,163845,163939,164053,164165,164267,164387,164509,164591,164695,164815,164941,165039,165133,165221,165333,165449,165571,165683,165858,165974,166060,166152,166264,166388,166455,166581,166649,166777,166921,167049,167118,167213,167328,167441,167540,167649,167760,167871,167972,168077,168177,168307,168398,168521,168615,168727,168813,168917,169013,169101,169219,169323,169427,169553,169641,169749,169849,169939,170049,170133,170235,170319,170373,170437,170543,170653,170737,170857"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/integers.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "446,447,448,449,450,451,452,453,454,455,456,457,458,459,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27022,27086,27148,27214,27276,27336,27393,27456,27528,27585,27642,27702,27760,27830,27887", "endColumns": "63,61,65,61,59,56,62,71,56,56,59,57,69,56,69", "endOffsets": "27081,27143,27209,27271,27331,27388,27451,27523,27580,27637,27697,27755,27825,27882,27952"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/styles.xml", "from": {"startLines": "-1,-1,-1,-1,69,-1,-1,61,-1,-1,-1,-1,-1,74", "startColumns": "-1,-1,-1,-1,4,-1,-1,4,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,2546,-1,-1,2257,-1,-1,-1,-1,-1,2771", "endLines": "-1,-1,-1,-1,72,-1,-1,66,-1,-1,-1,-1,-1,78", "endColumns": "-1,-1,-1,-1,12,-1,-1,12,-1,-1,-1,-1,-1,12", "endOffsets": "-1,-1,-1,-1,2765,-1,-1,2521,-1,-1,-1,-1,-1,2975"}, "to": {"startLines": "556,561,562,566,2524,2900,2904,2909,2915,2920,2925,2930,2935,2940", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "34764,35039,35129,35297,163068,187583,187734,187967,188236,188463,188696,188929,189150,189371", "endLines": "560,561,565,566,2527,2903,2908,2914,2919,2924,2929,2934,2939,2944", "endColumns": "12,89,12,79,12,12,12,12,12,12,12,12,12,12", "endOffsets": "35034,35124,35292,35372,163287,187729,187962,188231,188458,188691,188924,189145,189366,189575"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/e476e0dd04b5343b91450d154c8a112f/cardview-v7-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,83,84,85,86,588,1980,1982,1985", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "510,4677,4738,4800,4862,36828,125751,125815,125941", "endLines": "13,83,84,85,86,594,1981,1984,1987", "endColumns": "51,60,61,61,63,12,12,12,12", "endOffsets": "557,4733,4795,4857,4921,37237,125810,125936,126064"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/bools.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "48,49,50,51", "startColumns": "4,4,4,4", "startOffsets": "2442,2497,2556,2618", "endColumns": "54,58,61,61", "endOffsets": "2492,2551,2613,2675"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "9,10,11,12,14,15,16,17,20,22,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,96,97,98,99,100,101,102,103,104,105,106,136,137,138,139,140,141,142,143,419,420,427,428,435,436,437,445,552,827,828,829,834,835,839,845,849,850,851,852,863,864,865,869,875,879,949,950,951,981,1001,1047,1077,1097,1117,1163,1167,1907,1921,1962,1970,2105,2106,2107,2108,2257,2260,2261,2264,2267,2268,2271,2275,2280,2288,2296,2305,2313,2317,2325,2333,2341,2349,2357,2366,2375,2383,2392,2430,2432,2437,2439,2444,2448,2452,2453,2458,2459,2460,2461,2462,2463,2465,2466,2471,2472,2473,2474,2475,2476,2477,2479,2483,2487,2491,2502,2503,2504,2505,2506,2507,2508,2509,2510,2513,2517,2520,2624,2632,2639,2648,2652,2667,2675,2678,2687,2692,2703,2711,2714,2723,2730,2731,2750,2753,2759,2762,2771,2774,2777,2780,2783,2786,2790,2793,2802,2805,2813,2818,2826,2831,2835,2836,2847,2854,2858,2862,2863,2867,2875,2879,2884,2889", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,334,394,455,562,615,673,721,886,976,1078,1136,1196,1254,1300,1360,1413,1459,1509,1556,1614,1672,1731,1791,1853,1915,1977,2039,2101,2163,2224,2286,2348,5346,5420,5483,5551,5632,5696,5762,5832,5902,5972,6042,7834,7897,7962,8028,8081,8157,8223,8310,25721,25779,26127,26172,26498,26545,26590,26974,34487,52380,52473,52580,52923,53030,53259,53668,53900,54000,54105,54224,54822,54969,55088,55323,55738,55976,61191,61312,61445,63524,65020,68254,70329,71837,73361,76591,76815,122022,122826,124586,125036,134776,134849,134936,135021,146544,146739,146831,147004,147166,147261,147430,147673,147966,148375,148789,149221,149639,149880,150310,150745,151155,151577,151987,152416,152842,153258,153696,156462,156530,156874,156954,157310,157460,157604,157688,158053,158151,158259,158357,158467,158583,158709,158805,159182,159292,159416,159554,159664,159786,159914,160052,160214,160430,160586,161462,161546,161650,161744,161858,161970,162094,162190,162270,162459,162665,162858,171001,171433,171854,172279,172476,173424,173945,174068,174705,174926,175741,176210,176393,176989,177449,177554,178815,178965,179382,179547,180227,180386,180477,180561,180757,180924,181146,181306,181683,181842,182170,182387,182962,183312,183561,183658,184364,184802,185043,185232,185366,185557,186194,186444,186747,186962", "endLines": "9,10,11,12,14,15,16,17,20,22,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,96,97,98,99,100,101,102,103,104,105,106,136,137,138,139,140,141,142,143,419,420,427,428,435,436,437,445,555,827,828,833,834,838,844,848,849,850,851,862,863,864,868,874,878,879,949,950,980,1000,1046,1076,1096,1116,1162,1166,1170,1920,1961,1969,1979,2105,2106,2107,2108,2259,2260,2263,2266,2267,2270,2274,2279,2287,2295,2304,2312,2316,2324,2332,2340,2348,2356,2365,2374,2382,2391,2394,2431,2436,2438,2443,2447,2451,2452,2457,2458,2459,2460,2461,2462,2464,2465,2470,2471,2472,2473,2474,2475,2476,2478,2482,2486,2490,2494,2502,2503,2504,2505,2506,2507,2508,2509,2512,2516,2519,2523,2631,2638,2647,2651,2666,2674,2677,2686,2691,2702,2710,2713,2722,2729,2730,2749,2752,2758,2761,2770,2773,2776,2779,2782,2785,2789,2792,2801,2804,2812,2817,2825,2830,2834,2835,2846,2853,2857,2861,2862,2866,2874,2878,2883,2888,2896", "endColumns": "55,59,60,54,52,57,47,48,44,52,57,59,57,45,59,52,45,49,46,57,57,58,59,61,61,61,61,61,61,60,61,61,52,73,62,67,80,63,65,69,69,69,69,66,62,64,65,52,75,65,86,75,57,61,44,42,46,44,50,47,10,92,106,10,106,10,10,10,99,104,118,10,146,118,10,10,10,111,120,132,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,91,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,10,97,107,97,109,115,10,95,10,109,123,137,109,121,127,10,10,10,10,10,83,103,93,113,111,123,95,79,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,96,10,10,10,10,133,10,10,10,10,10,10", "endOffsets": "329,389,450,505,610,668,716,765,926,1024,1131,1191,1249,1295,1355,1408,1454,1504,1551,1609,1667,1726,1786,1848,1910,1972,2034,2096,2158,2219,2281,2343,2396,5415,5478,5546,5627,5691,5757,5827,5897,5967,6037,6104,7892,7957,8023,8076,8152,8218,8305,8381,25774,25836,26167,26210,26540,26585,26636,27017,34759,52468,52575,52918,53025,53254,53663,53895,53995,54100,54219,54817,54964,55083,55318,55733,55971,56083,61307,61440,63519,65015,68249,70324,71832,73356,76586,76810,77047,122821,124581,125031,125746,134844,134931,135016,135115,146734,146826,146999,147161,147256,147425,147668,147961,148370,148784,149216,149634,149875,150305,150740,151150,151572,151982,152411,152837,153253,153691,153873,156525,156869,156949,157305,157455,157599,157683,158048,158146,158254,158352,158462,158578,158704,158800,159177,159287,159411,159549,159659,159781,159909,160047,160209,160425,160581,160785,161541,161645,161739,161853,161965,162089,162185,162265,162454,162660,162853,163063,171428,171849,172274,172471,173419,173940,174063,174700,174921,175736,176205,176388,176984,177444,177549,178810,178960,179377,179542,180222,180381,180472,180556,180752,180919,181141,181301,181678,181837,182165,182382,182957,183307,183556,183653,184359,184797,185038,185227,185361,185552,186189,186439,186742,186957,187433"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/8fd5d53888178dd9013834ef7b031501/support-media-compat-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "152,2246,2248,2249,2254,2256", "startColumns": "4,4,4,4,4,4", "startOffsets": "8835,145722,145898,146020,146282,146477", "endColumns": "88,65,121,60,65,66", "endOffsets": "8919,145783,146015,146076,146343,146539"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/drawables.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "402,403,404,405,406,407,408,409", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "24676,24756,24838,24918,24994,25072,25158,25230", "endColumns": "79,81,79,75,77,85,71,75", "endOffsets": "24751,24833,24913,24989,25067,25153,25225,25301"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27957,28031,28101,28179,28233,28303,28388,28436,28482,28553,28631,28709,28781,28855,28929,29003,29083,29156,29225,29297,29374,29435,29498,29564,29628,29699,29762,29827,29891,29952,30013,30065,30138,30212,30281,30356,30430,30504,30593,30663,30716,30759,30883,30961,31061,31099,31203,31269,31317,31448,31579,31649,31776,31857,31933,32007,32060,32097,32175,32455,32600,32734,32815,32896,32949,32999,33046,33091,33143,33196,33247,33286,33335,33388,33435,33506,33553,33616,33661,33700,33753,33811,33856,33899,33950,33998", "endColumns": "73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,88,69,52,42,123,77,99,37,103,65,47,130,130,69,126,80,75,73,52,36,77,279,144,133,80,80,52,49,46,44,51,52,50,38,48,52,46,70,46,62,44,38,52,57,44,42,50,47,46", "endOffsets": "28026,28096,28174,28228,28298,28383,28431,28477,28548,28626,28704,28776,28850,28924,28998,29078,29151,29220,29292,29369,29430,29493,29559,29623,29694,29757,29822,29886,29947,30008,30060,30133,30207,30276,30351,30425,30499,30588,30658,30711,30754,30878,30956,31056,31094,31198,31264,31312,31443,31574,31644,31771,31852,31928,32002,32055,32092,32170,32450,32595,32729,32810,32891,32944,32994,33041,33086,33138,33191,33242,33281,33330,33383,33430,33501,33548,33611,33656,33695,33748,33806,33851,33894,33945,33993,34040"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10462,10531,10600,10670,10744,10820,10884,10961,11037,11114,11179,11248,11325,11400,11469,11537,11614,11680,11741,11838,11903,11972,12071,12142,12201,12259,12316,12375,12439,12499,12560,12621,12682,12754,12821,12878,12935,12994,13057,13121,13184,13249,13308,13373,13439,13505,13575,13639,13692,13805,13863,13926,13990,14054,14129,14202,14274,14323,14384,14445,14506,14568,14632,14696,14760,14825,14888,14948,15009,15075,15134,15194,15256,15327,15387,15455,15513,15569,15615,15664,15723,15780,15834,15904,15972,16044,16114,16175,16249,16322,16376,16455,16533,16606,16671,16734,16800,16871,16942,17004,17073,17139,17206,17273,17329,17380,17433,17485,17539,17610,17673,17732,17794,17853,17926,17993,18053,18116,18191,18263,18334,18390,18461,18518,18575,18641,18705,18776,18833,18886,18949,19001,19059,19126,19185,19246,19288,19347,19395,19451,19515,19575,19637,19692,19749,19812,19877,19952,20028,20100,20166,20232,20313,20388,20444,20497,20558,20616,20666,20715,20764,20813,20875,20927,20972,21027,21081,21134,21188,21239,21288,21339,21400,21461,21523,21573,21614,21664,21712,21774,21825,21874,21943,22004,22060,22131,22196,22265,22316,22379,22449,22518,22588,22650,22720,22790,22865,22924,22974,23033,23094,23155,23217,23281,23343,23404,23472,23572,23632,23698,23771,23840,23897,23949,24011,24064,24117,24170,24223,24275,24333,24378,24444,24508,24565,24622", "endColumns": "68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,59,60,60,60,71,66,56,56,58,62,63,62,64,58,64,65,65,69,63,52,112,57,62,63,63,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,57,55,45,48,58,56,53,69,67,71,69,60,73,72,53,78,77,72,64,62,65,70,70,61,68,65,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,70,55,70,56,56,65,63,70,56,52,62,51,57,66,58,60,41,58,47,55,63,59,61,54,56,62,64,74,75,71,65,65,80,74,55,52,60,57,49,48,48,48,61,51,44,54,53,52,53,50,48,50,60,60,61,49,40,49,47,61,50,48,68,60,55,70,64,68,50,62,69,68,69,61,69,69,74,58,49,58,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,52,52,52,52,51,57,44,65,63,56,56,53", "endOffsets": "10526,10595,10665,10739,10815,10879,10956,11032,11109,11174,11243,11320,11395,11464,11532,11609,11675,11736,11833,11898,11967,12066,12137,12196,12254,12311,12370,12434,12494,12555,12616,12677,12749,12816,12873,12930,12989,13052,13116,13179,13244,13303,13368,13434,13500,13570,13634,13687,13800,13858,13921,13985,14049,14124,14197,14269,14318,14379,14440,14501,14563,14627,14691,14755,14820,14883,14943,15004,15070,15129,15189,15251,15322,15382,15450,15508,15564,15610,15659,15718,15775,15829,15899,15967,16039,16109,16170,16244,16317,16371,16450,16528,16601,16666,16729,16795,16866,16937,16999,17068,17134,17201,17268,17324,17375,17428,17480,17534,17605,17668,17727,17789,17848,17921,17988,18048,18111,18186,18258,18329,18385,18456,18513,18570,18636,18700,18771,18828,18881,18944,18996,19054,19121,19180,19241,19283,19342,19390,19446,19510,19570,19632,19687,19744,19807,19872,19947,20023,20095,20161,20227,20308,20383,20439,20492,20553,20611,20661,20710,20759,20808,20870,20922,20967,21022,21076,21129,21183,21234,21283,21334,21395,21456,21518,21568,21609,21659,21707,21769,21820,21869,21938,21999,22055,22126,22191,22260,22311,22374,22444,22513,22583,22645,22715,22785,22860,22919,22969,23028,23089,23150,23212,23276,23338,23399,23467,23567,23627,23693,23766,23835,23892,23944,24006,24059,24112,24165,24218,24270,24328,24373,24439,24503,24560,24617,24671"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c8781cf76c3d46af7130a3e9cb97651e/transition-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "414,421,424,425,426,439,440,441,442,443", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "25511,25841,25980,26027,26082,26676,26730,26782,26831,26892", "endColumns": "39,42,46,54,44,53,51,48,60,49", "endOffsets": "25546,25879,26022,26077,26122,26725,26777,26826,26887,26937"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/96d6faed5a5735fd14bdec9d38b8895e/recyclerview-v7-28.0.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "416", "startColumns": "4", "startOffsets": "25585", "endColumns": "65", "endOffsets": "25646"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "150,151,417,418,430,431,432,433,434,438,2241,2242,2247,2250,2255,2622,2623", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8703,8772,25651,25686,26261,26311,26372,26429,26463,26641,145404,145521,145788,146081,146348,170862,170934", "endLines": "150,151,417,418,430,431,432,433,434,438,2241,2245,2247,2253,2255,2622,2623", "endColumns": "68,62,34,34,49,60,56,33,34,34,116,12,109,12,128,71,66", "endOffsets": "8767,8830,25681,25716,26306,26367,26424,26458,26493,26671,145516,145717,145893,146277,146472,170929,170996"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/a431d7a91ec2d1f028c3cc59a84cc899/coordinatorlayout-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "18,2897", "startColumns": "4,4", "startOffsets": "770,187438", "endLines": "18,2899", "endColumns": "60,12", "endOffsets": "826,187578"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "52,53,54,55,56,57,64,65,66,67,72,73,74,87,88,89,90,91,92,93,94,95,111,116,119,120,133,134,135,144,145,146,147,148,149,161,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2680,2719,2766,2810,2856,2903,3402,3447,3493,3539,3904,3944,3991,4926,4972,5019,5070,5116,5162,5208,5254,5300,6389,6710,6904,6949,7667,7722,7780,8386,8438,8491,8544,8597,8650,9512,10368,10408", "endColumns": "38,46,43,45,46,50,44,45,45,45,39,46,44,45,46,50,45,45,45,45,45,45,54,58,44,45,54,57,53,51,52,52,52,52,52,37,39,53", "endOffsets": "2714,2761,2805,2851,2898,2949,3442,3488,3534,3580,3939,3986,4031,4967,5014,5065,5111,5157,5203,5249,5295,5341,6439,6764,6944,6990,7717,7775,7829,8433,8486,8539,8592,8645,8698,9545,10403,10457"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/arrays.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "8", "endColumns": "12", "endOffsets": "273"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ms_values-ms.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ms/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,211,289,346,420,501,550,599,660,723,788,852,921,984,1049,1113,1174,1235,1285,1367,1446,1513,1592,1666,1743,1835,1906,1957", "endColumns": "80,74,77,56,73,80,48,48,60,62,64,63,68,62,64,63,60,60,49,81,78,66,78,73,76,91,70,50,70", "endOffsets": "131,206,284,341,415,496,545,594,655,718,783,847,916,979,1044,1108,1169,1230,1280,1362,1441,1508,1587,1661,1738,1830,1901,1952,2023"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-sl_values-sl.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-sl/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,209,287,344,417,506,562,616,678,742,808,872,938,1002,1068,1138,1200,1262,1316,1394,1473,1543,1626,1704,1778,1891,1958,2012", "endColumns": "76,76,77,56,72,88,55,53,61,63,65,63,65,63,65,69,61,61,53,77,78,69,82,77,73,112,66,53,70", "endOffsets": "127,204,282,339,412,501,557,611,673,737,803,867,933,997,1063,1133,1195,1257,1311,1389,1468,1538,1621,1699,1773,1886,1953,2007,2078"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-kn_values-kn.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-kn/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,225,308,366,443,540,588,635,696,759,825,889,960,1023,1088,1152,1213,1274,1326,1412,1493,1562,1645,1720,1804,1901,1971,2024", "endColumns": "87,81,82,57,76,96,47,46,60,62,65,63,70,62,64,63,60,60,51,85,80,68,82,74,83,96,69,52,70", "endOffsets": "138,220,303,361,438,535,583,630,691,754,820,884,955,1018,1083,1147,1208,1269,1321,1407,1488,1557,1640,1715,1799,1896,1966,2019,2090"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-en-rXC/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,400,579,734,906,1091,1235,1382,1543,1706,1874,2040,2213,2378,2545,2710,2873,3034,3188,3362,3537,3708,3885,4058,4233,4438,4608,4758", "endColumns": "174,169,178,154,171,184,143,146,160,162,167,165,172,164,166,164,162,160,153,173,174,170,176,172,174,204,169,149,172", "endOffsets": "225,395,574,729,901,1086,1230,1377,1538,1701,1869,2035,2208,2373,2540,2705,2868,3029,3183,3357,3532,3703,3880,4053,4228,4433,4603,4753,4926"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-watch-v20_values-watch-v20.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-watch-v20/values-watch-v20.xml", "from": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,214,385", "endLines": "4,7,10", "endColumns": "12,12,12", "endOffsets": "209,380,553"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-tl_values-tl.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-tl/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,222,309,367,443,534,584,632,693,756,822,886,957,1020,1085,1149,1210,1271,1325,1404,1485,1556,1636,1724,1802,1898,1970,2025", "endColumns": "88,77,86,57,75,90,49,47,60,62,65,63,70,62,64,63,60,60,53,78,80,70,79,87,77,95,71,54,70", "endOffsets": "139,217,304,362,438,529,579,627,688,751,817,881,952,1015,1080,1144,1205,1266,1320,1399,1480,1551,1631,1719,1797,1893,1965,2020,2091"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-es-rUS/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,224,302,357,428,521,577,629,690,753,819,883,953,1016,1085,1151,1212,1273,1325,1407,1477,1548,1626,1704,1781,1873,1943,1996", "endColumns": "89,78,77,54,70,92,55,51,60,62,65,63,69,62,68,65,60,60,51,81,69,70,77,77,76,91,69,52,70", "endOffsets": "140,219,297,352,423,516,572,624,685,748,814,878,948,1011,1080,1146,1207,1268,1320,1402,1472,1543,1621,1699,1776,1868,1938,1991,2062"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-b+sr+Latn/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,208,284,340,414,502,555,606,667,730,796,860,931,994,1059,1134,1195,1256,1314,1390,1468,1539,1614,1693,1764,1866,1933,1988", "endColumns": "78,73,75,55,73,87,52,50,60,62,65,63,70,62,64,74,60,60,57,75,77,70,74,78,70,101,66,54,70", "endOffsets": "129,203,279,335,409,497,550,601,662,725,791,855,926,989,1054,1129,1190,1251,1309,1385,1463,1534,1609,1688,1759,1861,1928,1983,2054"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-mk_values-mk.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-mk/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,207,285,341,416,505,559,611,678,747,814,884,961,1030,1101,1167,1234,1295,1352,1428,1505,1576,1653,1735,1809,1898,1966,2021", "endColumns": "77,73,77,55,74,88,53,51,66,68,66,69,76,68,70,65,66,60,56,75,76,70,76,81,73,88,67,54,70", "endOffsets": "128,202,280,336,411,500,554,606,673,742,809,879,956,1025,1096,1162,1229,1290,1347,1423,1500,1571,1648,1730,1804,1893,1961,2016,2087"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-az_values-az.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-az/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,202,284,342,419,503,555,604,665,728,793,862,933,996,1061,1126,1187,1249,1304,1381,1458,1528,1607,1682,1762,1853,1925,1979", "endColumns": "77,68,81,57,76,83,51,48,60,62,64,68,70,62,64,64,60,61,54,76,76,69,78,74,79,90,71,53,70", "endOffsets": "128,197,279,337,414,498,550,599,660,723,788,857,928,991,1056,1121,1182,1244,1299,1376,1453,1523,1602,1677,1757,1848,1920,1974,2045"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-th_values-th.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-th/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,198,276,335,407,487,535,583,644,707,773,837,908,971,1036,1100,1161,1222,1273,1351,1425,1493,1571,1647,1718,1804,1869,1921", "endColumns": "74,67,77,58,71,79,47,47,60,62,65,63,70,62,64,63,60,60,50,77,73,67,77,75,70,85,64,51,70", "endOffsets": "125,193,271,330,402,482,530,578,639,702,768,832,903,966,1031,1095,1156,1217,1268,1346,1420,1488,1566,1642,1713,1799,1864,1916,1987"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-sq_values-sq.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-sq/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,209,291,348,428,521,573,622,683,746,812,876,948,1011,1076,1143,1204,1267,1320,1396,1471,1539,1615,1689,1775,1862,1931,1983", "endColumns": "83,69,81,56,79,92,51,48,60,62,65,63,71,62,64,66,60,62,52,75,74,67,75,73,85,86,68,51,70", "endOffsets": "134,204,286,343,423,516,568,617,678,741,807,871,943,1006,1071,1138,1199,1262,1315,1391,1466,1534,1610,1684,1770,1857,1926,1978,2049"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-nb_values-nb.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-nb/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,198,282,338,408,491,538,584,645,708,774,838,909,972,1037,1105,1166,1227,1277,1350,1419,1485,1559,1628,1699,1785,1852,1902", "endColumns": "77,64,83,55,69,82,46,45,60,62,65,63,70,62,64,67,60,60,49,72,68,65,73,68,70,85,66,49,70", "endOffsets": "128,193,277,333,403,486,533,579,640,703,769,833,904,967,1032,1100,1161,1222,1272,1345,1414,1480,1554,1623,1694,1780,1847,1897,1968"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-en-rAU/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,199,277,331,401,486,534,580,641,704,770,834,905,968,1033,1097,1158,1219,1271,1344,1418,1487,1562,1636,1710,1799,1869,1922", "endColumns": "73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,88,69,52,70", "endOffsets": "124,194,272,326,396,481,529,575,636,699,765,829,900,963,1028,1092,1153,1214,1266,1339,1413,1482,1557,1631,1705,1794,1864,1917,1988"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-en-rCA/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,199,277,331,401,486,534,580,641,704,770,834,905,968,1033,1097,1158,1219,1271,1344,1418,1487,1562,1636,1710,1799,1869,1922", "endColumns": "73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,88,69,52,70", "endOffsets": "124,194,272,326,396,481,529,575,636,699,765,829,900,963,1028,1092,1153,1214,1266,1339,1413,1482,1557,1631,1705,1794,1864,1917,1988"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-mn_values-mn.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-mn/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,208,291,348,424,506,559,611,672,735,801,867,935,998,1063,1125,1186,1246,1298,1377,1451,1518,1596,1668,1741,1833,1900,1952", "endColumns": "82,69,82,56,75,81,52,51,60,62,65,65,67,62,64,61,60,59,51,78,73,66,77,71,72,91,66,51,70", "endOffsets": "133,203,286,343,419,501,554,606,667,730,796,862,930,993,1058,1120,1181,1241,1293,1372,1446,1513,1591,1663,1736,1828,1895,1947,2018"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-bg_values-bg.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-bg/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,140,217,292,348,423,514,564,612,673,736,802,866,937,1000,1065,1143,1204,1265,1318,1402,1481,1551,1635,1712,1790,1883,1952,2006", "endColumns": "84,76,74,55,74,90,49,47,60,62,65,63,70,62,64,77,60,60,52,83,78,69,83,76,77,92,68,53,70", "endOffsets": "135,212,287,343,418,509,559,607,668,731,797,861,932,995,1060,1138,1199,1260,1313,1397,1476,1546,1630,1707,1785,1878,1947,2001,2072"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-pl_values-pl.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-pl/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,141,213,291,347,426,515,564,611,672,735,801,865,937,1000,1065,1130,1191,1252,1304,1383,1462,1531,1610,1692,1770,1866,1932,1985", "endColumns": "85,71,77,55,78,88,48,46,60,62,65,63,71,62,64,64,60,60,51,78,78,68,78,81,77,95,65,52,70", "endOffsets": "136,208,286,342,421,510,559,606,667,730,796,860,932,995,1060,1125,1186,1247,1299,1378,1457,1526,1605,1687,1765,1861,1927,1980,2051"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-xxhdpi-v4_values-xxhdpi-v4.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-xxhdpi/bools.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "41", "endOffsets": "92"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-xxhdpi/dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,94,130,166,205,242,281,318,355,392,431,468,507,544,581,618,657,694,729,768,805,842,879,918,955,994,1033,1072,1109,1144,1181,1218,1255,1294,1329,1366,1401,1438,1475,1510,1547,1584,1619,1656,1691,1728,1763,1800", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "89,125,161,200,237,276,313,350,387,426,463,502,539,576,613,652,689,724,763,800,837,874,913,950,989,1028,1067,1104,1139,1176,1213,1250,1289,1324,1361,1396,1433,1470,1505,1542,1579,1614,1651,1686,1723,1758,1795,1830"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "97,136,172,208,247,284,323,360,397,434,473,510,549,586,623,660,699,736,771,810,847,884,921,960,997,1036,1075,1114,1151,1186,1223,1260,1297,1336,1371,1408,1443,1480,1517,1552,1589,1626,1661,1698,1733,1770,1805,1842", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "131,167,203,242,279,318,355,392,429,468,505,544,581,618,655,694,731,766,805,842,879,916,955,992,1031,1070,1109,1146,1181,1218,1255,1292,1331,1366,1403,1438,1475,1512,1547,1584,1621,1656,1693,1728,1765,1800,1837,1872"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-en-rIN/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,199,277,331,401,486,534,580,641,704,770,834,905,968,1033,1097,1158,1219,1271,1344,1418,1487,1562,1636,1710,1799,1869,1922", "endColumns": "73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,88,69,52,70", "endOffsets": "124,194,272,326,396,481,529,575,636,699,765,829,900,963,1028,1092,1153,1214,1266,1339,1413,1482,1557,1631,1705,1794,1864,1917,1988"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-kk_values-kk.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-kk/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,210,290,345,421,510,562,612,673,736,802,866,937,1000,1065,1132,1193,1255,1306,1379,1454,1522,1599,1679,1749,1848,1917,1969", "endColumns": "81,72,79,54,75,88,51,49,60,62,65,63,70,62,64,66,60,61,50,72,74,67,76,79,69,98,68,51,70", "endOffsets": "132,205,285,340,416,505,557,607,668,731,797,861,932,995,1060,1127,1188,1250,1301,1374,1449,1517,1594,1674,1744,1843,1912,1964,2035"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-xhdpi-v4_values-xhdpi-v4.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-xhdpi/dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,94,130,166,205,242,281,318,355,392,431,468,507,544,581,618,657,694,729,768,805,842,879,918,955,994,1033,1072,1109,1144,1181,1218,1255,1294,1329,1366,1401,1438,1475,1510,1547,1584,1619,1656,1691,1728,1763,1800", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "89,125,161,200,237,276,313,350,387,426,463,502,539,576,613,652,689,724,763,800,837,874,913,950,989,1028,1067,1104,1139,1176,1213,1250,1289,1324,1361,1396,1433,1470,1505,1542,1579,1614,1651,1686,1723,1758,1795,1830"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "97,136,172,208,247,284,323,360,397,434,473,510,549,586,623,660,699,736,771,810,847,884,921,960,997,1036,1075,1114,1151,1186,1223,1260,1297,1336,1371,1408,1443,1480,1517,1552,1589,1626,1661,1698,1733,1770,1805,1842", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "131,167,203,242,279,318,355,392,429,468,505,544,581,618,655,694,731,766,805,842,879,916,955,992,1031,1070,1109,1146,1181,1218,1255,1292,1331,1366,1403,1438,1475,1512,1547,1584,1621,1656,1693,1728,1765,1800,1837,1872"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-xhdpi/bools.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "41", "endOffsets": "92"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-fa_values-fa.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-fa/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,208,289,343,416,501,551,599,662,727,793,857,930,995,1062,1131,1194,1254,1305,1387,1460,1528,1608,1683,1762,1856,1927,1979", "endColumns": "79,72,80,53,72,84,49,47,62,64,65,63,72,64,66,68,62,59,50,81,72,67,79,74,78,93,70,51,70", "endOffsets": "130,203,284,338,411,496,546,594,657,722,788,852,925,990,1057,1126,1189,1249,1300,1382,1455,1523,1603,1678,1757,1851,1922,1974,2045"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v24_values-v24.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/8fd5d53888178dd9013834ef7b031501/support-media-compat-28.0.0/res/values-v24/values-v24.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,121,182,248", "endColumns": "65,60,65,66", "endOffsets": "116,177,243,310"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "347,413,474,540", "endColumns": "65,60,65,66", "endOffsets": "408,469,535,602"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v24/values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-my_values-my.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-my/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,209,296,359,441,539,588,637,698,761,827,891,962,1025,1090,1154,1215,1276,1332,1425,1507,1579,1675,1757,1837,1930,2000,2055", "endColumns": "78,74,86,62,81,97,48,48,60,62,65,63,70,62,64,63,60,60,55,92,81,71,95,81,79,92,69,54,70", "endOffsets": "129,204,291,354,436,534,583,632,693,756,822,886,957,1020,1085,1149,1210,1271,1327,1420,1502,1574,1670,1752,1832,1925,1995,2050,2121"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-pt-rBR/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,221,298,357,428,516,570,620,681,744,810,874,945,1008,1073,1138,1199,1260,1317,1394,1476,1548,1626,1704,1784,1879,1949,2005", "endColumns": "89,75,76,58,70,87,53,49,60,62,65,63,70,62,64,64,60,60,56,76,81,71,77,77,79,94,69,55,70", "endOffsets": "140,216,293,352,423,511,565,615,676,739,805,869,940,1003,1068,1133,1194,1255,1312,1389,1471,1543,1621,1699,1779,1874,1944,2000,2071"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-vi_values-vi.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-vi/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,217,296,350,423,512,560,607,668,731,797,861,932,995,1060,1128,1189,1250,1304,1378,1457,1528,1603,1688,1763,1853,1922,1977", "endColumns": "83,77,78,53,72,88,47,46,60,62,65,63,70,62,64,67,60,60,53,73,78,70,74,84,74,89,68,54,70", "endOffsets": "134,212,291,345,418,507,555,602,663,726,792,856,927,990,1055,1123,1184,1245,1299,1373,1452,1523,1598,1683,1758,1848,1917,1972,2043"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v22_values-v22.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v22/values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,553", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,548,896"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-pt-rPT/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,221,298,357,428,522,577,628,690,754,822,886,956,1020,1086,1151,1213,1275,1332,1409,1491,1563,1641,1719,1796,1900,1969,2025", "endColumns": "89,75,76,58,70,93,54,50,61,63,67,63,69,63,65,64,61,61,56,76,81,71,77,77,76,103,68,55,70", "endOffsets": "140,216,293,352,423,517,572,623,685,749,817,881,951,1015,1081,1146,1208,1270,1327,1404,1486,1558,1636,1714,1791,1895,1964,2020,2091"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-nl_values-nl.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-nl/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,218,295,351,429,519,567,614,676,740,811,875,946,1010,1076,1141,1203,1265,1317,1398,1472,1541,1626,1710,1783,1871,1944,1997", "endColumns": "87,74,76,55,77,89,47,46,61,63,70,63,70,63,65,64,61,61,51,80,73,68,84,83,72,87,72,52,70", "endOffsets": "138,213,290,346,424,514,562,609,671,735,806,870,941,1005,1071,1136,1198,1260,1312,1393,1467,1536,1621,1705,1778,1866,1939,1992,2063"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-en-rGB/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,199,277,331,401,486,534,580,641,704,770,834,905,968,1033,1097,1158,1219,1271,1344,1418,1487,1562,1636,1710,1799,1869,1922", "endColumns": "73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,88,69,52,70", "endOffsets": "124,194,272,326,396,481,529,575,636,699,765,829,900,963,1028,1092,1153,1214,1266,1339,1413,1482,1557,1631,1705,1794,1864,1917,1988"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v18_values-v18.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v18/values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-tr_values-tr.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-tr/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,199,281,336,412,502,552,598,659,722,785,849,917,980,1052,1117,1178,1239,1288,1365,1440,1506,1583,1656,1734,1823,1891,1941", "endColumns": "74,68,81,54,75,89,49,45,60,62,62,63,67,62,71,64,60,60,48,76,74,65,76,72,77,88,67,49,70", "endOffsets": "125,194,276,331,407,497,547,593,654,717,780,844,912,975,1047,1112,1173,1234,1283,1360,1435,1501,1578,1651,1729,1818,1886,1936,2007"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ldltr-v21_values-ldltr-v21.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ldltr-v21/styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "3", "endColumns": "12", "endOffsets": "182"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-uk_values-uk.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-uk/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,207,283,339,417,505,555,605,666,729,795,859,930,993,1058,1123,1184,1245,1296,1372,1449,1517,1595,1672,1751,1854,1924,1976", "endColumns": "79,71,75,55,77,87,49,49,60,62,65,63,70,62,64,64,60,60,50,75,76,67,77,76,78,102,69,51,70", "endOffsets": "130,202,278,334,412,500,550,600,661,724,790,854,925,988,1053,1118,1179,1240,1291,1367,1444,1512,1590,1667,1746,1849,1919,1971,2042"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-es_values-es.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-es/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,225,303,358,429,527,583,635,697,761,829,893,964,1028,1094,1160,1222,1284,1336,1413,1483,1552,1630,1708,1785,1877,1947,2000", "endColumns": "86,82,77,54,70,97,55,51,61,63,67,63,70,63,65,65,61,61,51,76,69,68,77,77,76,91,69,52,70", "endOffsets": "137,220,298,353,424,522,578,630,692,756,824,888,959,1023,1089,1155,1217,1279,1331,1408,1478,1547,1625,1703,1780,1872,1942,1995,2066"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-is_values-is.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-is/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,192,274,329,400,484,535,585,646,709,775,839,916,979,1044,1109,1170,1234,1285,1365,1443,1511,1590,1660,1733,1821,1889,1941", "endColumns": "69,66,81,54,70,83,50,49,60,62,65,63,76,62,64,64,60,63,50,79,77,67,78,69,72,87,67,51,70", "endOffsets": "120,187,269,324,395,479,530,580,641,704,770,834,911,974,1039,1104,1165,1229,1280,1360,1438,1506,1585,1655,1728,1816,1884,1936,2007"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ko_values-ko.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ko/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,184,256,308,376,452,502,548,609,672,734,795,866,929,994,1058,1119,1180,1230,1298,1363,1428,1498,1565,1634,1719,1783,1832", "endColumns": "64,63,71,51,67,75,49,45,60,62,61,60,70,62,64,63,60,60,49,67,64,64,69,66,68,84,63,48,70", "endOffsets": "115,179,251,303,371,447,497,543,604,667,729,790,861,924,989,1053,1114,1175,1225,1293,1358,1423,1493,1560,1629,1714,1778,1827,1898"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-da_values-da.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-da/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,197,279,332,402,485,533,580,641,704,770,834,899,962,1027,1095,1156,1217,1266,1345,1423,1489,1573,1646,1717,1803,1870,1920", "endColumns": "72,68,81,52,69,82,47,46,60,62,65,63,64,62,64,67,60,60,48,78,77,65,83,72,70,85,66,49,70", "endOffsets": "123,192,274,327,397,480,528,575,636,699,765,829,894,957,1022,1090,1151,1212,1261,1340,1418,1484,1568,1641,1712,1798,1865,1915,1986"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v16_values-v16.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/res/values-v16/values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v16/values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-zh-rTW/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,183,253,305,372,450,497,543,605,669,737,803,869,933,999,1061,1123,1185,1233,1299,1365,1430,1497,1563,1633,1716,1780,1829", "endColumns": "64,62,69,51,66,77,46,45,61,63,67,65,65,63,65,61,61,61,47,65,65,64,66,65,69,82,63,48,70", "endOffsets": "115,178,248,300,367,445,492,538,600,664,732,798,864,928,994,1056,1118,1180,1228,1294,1360,1425,1492,1558,1628,1711,1775,1824,1895"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-ar_values-ar.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-ar/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,216,295,347,418,502,552,601,662,725,791,855,926,989,1054,1118,1179,1243,1292,1367,1438,1504,1582,1656,1729,1817,1884,1936", "endColumns": "86,73,78,51,70,83,49,48,60,62,65,63,70,62,64,63,60,63,48,74,70,65,77,73,72,87,66,51,70", "endOffsets": "137,211,290,342,413,497,547,596,657,720,786,850,921,984,1049,1113,1174,1238,1287,1362,1433,1499,1577,1651,1724,1812,1879,1931,2002"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-hu_values-hu.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-hu/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,208,293,347,429,529,576,622,683,746,812,876,947,1010,1075,1140,1201,1262,1315,1395,1476,1546,1627,1706,1795,1910,1983,2037", "endColumns": "77,74,84,53,81,99,46,45,60,62,65,63,70,62,64,64,60,60,52,79,80,69,80,78,88,114,72,53,70", "endOffsets": "128,203,288,342,424,524,571,617,678,741,807,871,942,1005,1070,1135,1196,1257,1310,1390,1471,1541,1622,1701,1790,1905,1978,2032,2103"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v28_values-v28.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v28/values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,447", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,442,684"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-sk_values-sk.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-sk/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,202,283,339,417,505,557,608,669,732,801,865,936,999,1064,1132,1193,1254,1308,1383,1462,1531,1607,1689,1768,1867,1935,1994", "endColumns": "76,69,80,55,77,87,51,50,60,62,68,63,70,62,64,67,60,60,53,74,78,68,75,81,78,98,67,58,70", "endOffsets": "127,197,278,334,412,500,552,603,664,727,796,860,931,994,1059,1127,1188,1249,1303,1378,1457,1526,1602,1684,1763,1862,1930,1989,2060"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-hdpi-v4_values-hdpi-v4.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-hdpi/bools.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "45", "endOffsets": "96"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-hdpi/dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,94,130,166,205,242,281,318,355,392,431,468,507,544,581,618,657,694,729,768,805,842,879,918,955,994,1033,1072,1109,1144,1181,1218,1255,1294,1329,1366,1401,1438,1475,1510,1547,1584,1619,1656,1691,1728,1763,1800", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "89,125,161,200,237,276,313,350,387,426,463,502,539,576,613,652,689,724,763,800,837,874,913,950,989,1028,1067,1104,1139,1176,1213,1250,1289,1324,1361,1396,1433,1470,1505,1542,1579,1614,1651,1686,1723,1758,1795,1830"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "101,140,176,212,251,288,327,364,401,438,477,514,553,590,627,664,703,740,775,814,851,888,925,964,1001,1040,1079,1118,1155,1190,1227,1264,1301,1340,1375,1412,1447,1484,1521,1556,1593,1630,1665,1702,1737,1774,1809,1846", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "135,171,207,246,283,322,359,396,433,472,509,548,585,622,659,698,735,770,809,846,883,920,959,996,1035,1074,1113,1150,1185,1222,1259,1296,1335,1370,1407,1442,1479,1516,1551,1588,1625,1660,1697,1732,1769,1804,1841,1876"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-hdpi/styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "6", "endColumns": "12", "endOffsets": "327"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "1881", "endLines": "55", "endColumns": "12", "endOffsets": "2153"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-am_values-am.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-am/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,196,272,328,401,484,533,581,642,705,768,832,903,966,1031,1095,1156,1216,1265,1335,1405,1471,1544,1614,1691,1777,1843,1893", "endColumns": "71,68,75,55,72,82,48,47,60,62,62,63,70,62,64,63,60,59,48,69,69,65,72,69,76,85,65,49,70", "endOffsets": "122,191,267,323,396,479,528,576,637,700,763,827,898,961,1026,1090,1151,1211,1260,1330,1400,1466,1539,1609,1686,1772,1838,1888,1959"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-v26_values-v26.arsc.flat", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v26/values-v26.xml", "from": {"startLines": "2,3,4,8,12,16", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,130,217,431,657,896", "endLines": "2,3,7,11,15,16", "endColumns": "74,86,12,12,12,92", "endOffsets": "125,212,426,652,891,984"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-lv_values-lv.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-lv/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,223,302,358,432,524,577,629,709,787,864,943,1025,1098,1180,1257,1332,1402,1457,1536,1618,1687,1768,1848,1923,2030,2099,2152", "endColumns": "89,77,78,55,73,91,52,51,79,77,76,78,81,72,81,76,74,69,54,78,81,68,80,79,74,106,68,52,70", "endOffsets": "140,218,297,353,427,519,572,624,704,782,859,938,1020,1093,1175,1252,1327,1397,1452,1531,1613,1682,1763,1843,1918,2025,2094,2147,2218"}}]}, {"outputFile": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/values-uz_values-uz.arsc.flat", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-uz/strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,204,288,344,414,500,551,600,661,724,790,854,919,982,1047,1112,1173,1235,1289,1368,1445,1516,1594,1670,1747,1841,1910,1965", "endColumns": "74,73,83,55,69,85,50,48,60,62,65,63,64,62,64,64,60,61,53,78,76,70,77,75,76,93,68,54,70", "endOffsets": "125,199,283,339,409,495,546,595,656,719,785,849,914,977,1042,1107,1168,1230,1284,1363,1440,1511,1589,1665,1742,1836,1905,1960,2031"}}]}]}