{"logs": [{"outputFile": "/Users/<USER>/paker/app/build/intermediates/incremental/mergeDebugResources/merged.dir/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,21,23,47,58,59,60,61,62,63,68,69,70,71,75,76,77,78,79,80,81,82,107,108,109,110,112,113,114,115,117,118,121,122,123,124,125,126,127,128,129,130,131,132,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,410,411,412,413,415,422,423,429,444,547,548,549,550,551,567,575,576,580,584,595,600,606,613,617,621,626,630,634,638,642,646,650,656,660,666,670,676,680,685,689,692,696,702,706,712,716,722,725,729,733,737,741,745,746,747,748,751,754,757,760,764,765,766,767,768,771,773,775,777,782,783,787,793,797,798,800,811,812,816,822,826,880,881,885,912,916,917,921,1171,1338,1364,1532,1558,1589,1597,1603,1617,1639,1644,1649,1659,1668,1677,1681,1688,1696,1703,1704,1713,1716,1719,1723,1727,1731,1734,1735,1739,1743,1753,1758,1765,1771,1772,1775,1779,1784,1786,1788,1791,1794,1796,1800,1803,1810,1813,1816,1820,1822,1826,1828,1830,1832,1836,1844,1852,1864,1870,1879,1882,1893,1896,1901,1902,1988,2046,2109,2110,2120,2129,2130,2132,2136,2139,2142,2145,2148,2151,2154,2157,2161,2164,2167,2170,2174,2177,2181,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2207,2209,2210,2211,2212,2213,2214,2215,2216,2218,2219,2221,2222,2224,2226,2227,2229,2230,2231,2232,2233,2234,2236,2237,2238,2239,2240,2395,2397,2399,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2415,2416,2417,2418,2419,2420,2422,2426,2495,2496,2497,2498,2499,2500,2501,2528,2530,2532,2534,2536,2538,2539,2540,2541,2543,2545,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2561,2562,2563,2564,2566,2568,2569,2571,2572,2574,2576,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2591,2592,2593,2594,2596,2597,2598,2599,2600,2602,2604,2606,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,931,1029,2401,2954,3035,3096,3171,3247,3324,3585,3670,3752,3828,4036,4113,4191,4297,4403,4482,4562,4619,6109,6183,6258,6323,6444,6504,6565,6637,6769,6836,6995,7054,7113,7172,7231,7290,7344,7398,7451,7505,7559,7613,8924,8998,9077,9150,9224,9295,9367,9439,9550,9607,9665,9738,9812,9886,9961,10033,10106,10176,10247,10307,25306,25363,25411,25460,25551,25884,25931,26215,26942,34045,34123,34213,34301,34397,35377,35959,36048,36295,36576,37242,37527,37920,38397,38619,38841,39117,39344,39574,39804,40034,40264,40491,40910,41136,41561,41791,42219,42438,42721,42929,43060,43287,43713,43938,44365,44586,45011,45131,45407,45708,46032,46323,46637,46774,46905,47010,47252,47419,47623,47831,48102,48214,48326,48431,48548,48762,48908,49048,49134,49482,49570,49816,50234,50483,50565,50663,51255,51355,51607,52031,52286,56088,56177,56414,58438,58680,58782,59035,77052,87181,88697,98925,100453,102210,102836,103256,104317,105582,105838,106074,106621,107115,107720,107918,108498,109062,109437,109555,110093,110250,110446,110719,110975,111145,111286,111350,111632,111918,112594,112858,113196,113549,113643,113829,114135,114397,114522,114649,114888,115099,115218,115411,115588,116043,116224,116346,116605,116718,116905,117007,117114,117243,117518,118026,118522,119399,119693,120263,120412,121144,121316,121652,121744,126069,130351,135120,135182,135760,136344,136435,136548,136777,136937,137089,137260,137426,137595,137762,137925,138168,138338,138511,138682,138956,139155,139360,139690,139774,139870,139966,140064,140164,140266,140368,140470,140572,140674,140774,140870,140982,141111,141234,141365,141496,141594,141708,141802,141942,142076,142172,142284,142384,142500,142596,142708,142808,142948,143084,143248,143378,143536,143686,143827,143971,144106,144218,144368,144496,144624,144760,144892,145022,145152,145264,153878,154024,154168,154306,154372,154462,154538,154642,154732,154834,154942,155050,155150,155230,155322,155420,155530,155608,155714,155806,155910,156020,156142,156305,160790,160870,160970,161060,161170,161264,161370,163292,163392,163504,163618,163734,163850,163944,164058,164170,164272,164392,164514,164596,164700,164820,164946,165044,165138,165226,165338,165454,165576,165688,165863,165979,166065,166157,166269,166393,166460,166586,166654,166782,166926,167054,167123,167218,167333,167446,167545,167654,167765,167876,167977,168082,168182,168312,168403,168526,168620,168732,168818,168922,169018,169106,169224,169328,169432,169558,169646,169754,169854,169944,170054,170138,170240,170324,170378,170442,170548,170658,170742", "endLines": "19,21,23,47,58,59,60,61,62,63,68,69,70,71,75,76,77,78,79,80,81,82,107,108,109,110,112,113,114,115,117,118,121,122,123,124,125,126,127,128,129,130,131,132,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,410,411,412,413,415,422,423,429,444,547,548,549,550,551,574,575,579,583,587,599,605,612,616,620,625,629,633,637,641,645,649,655,659,665,669,675,679,684,688,691,695,701,705,711,715,721,724,728,732,736,740,744,745,746,747,750,753,756,759,763,764,765,766,767,770,772,774,776,781,782,786,792,796,797,799,810,811,815,821,825,826,880,884,911,915,916,920,948,1337,1363,1531,1557,1588,1596,1602,1616,1638,1643,1648,1658,1667,1676,1680,1687,1695,1702,1703,1712,1715,1718,1722,1726,1730,1733,1734,1738,1742,1752,1757,1764,1770,1771,1774,1778,1783,1785,1787,1790,1793,1795,1799,1802,1809,1812,1815,1819,1821,1825,1827,1829,1831,1835,1843,1851,1863,1869,1878,1881,1892,1895,1900,1901,1906,2045,2104,2109,2119,2128,2129,2131,2135,2138,2141,2144,2147,2150,2153,2156,2160,2163,2166,2169,2173,2176,2180,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2206,2208,2209,2210,2211,2212,2213,2214,2215,2217,2218,2220,2221,2223,2225,2226,2228,2229,2230,2231,2232,2233,2235,2236,2237,2238,2239,2240,2396,2398,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2414,2415,2416,2417,2418,2419,2421,2425,2429,2495,2496,2497,2498,2499,2500,2501,2529,2531,2533,2535,2537,2538,2539,2540,2542,2544,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2560,2561,2562,2563,2565,2567,2568,2570,2571,2573,2575,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2590,2591,2592,2593,2595,2596,2597,2598,2599,2601,2603,2605,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621", "endColumns": "54,44,48,40,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,56,47,48,50,33,46,48,45,31,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119", "endOffsets": "881,971,1073,2437,3030,3091,3166,3242,3319,3397,3665,3747,3823,3899,4108,4186,4292,4398,4477,4557,4614,4672,6178,6253,6318,6384,6499,6560,6632,6705,6831,6899,7049,7108,7167,7226,7285,7339,7393,7446,7500,7554,7608,7662,8993,9072,9145,9219,9290,9362,9434,9507,9602,9660,9733,9807,9881,9956,10028,10101,10171,10242,10302,10363,25358,25406,25455,25506,25580,25926,25975,26256,26969,34118,34208,34296,34392,34482,35954,36043,36290,36571,36823,37522,37915,38392,38614,38836,39112,39339,39569,39799,40029,40259,40486,40905,41131,41556,41786,42214,42433,42716,42924,43055,43282,43708,43933,44360,44581,45006,45126,45402,45703,46027,46318,46632,46769,46900,47005,47247,47414,47618,47826,48097,48209,48321,48426,48543,48757,48903,49043,49129,49477,49565,49811,50229,50478,50560,50658,51250,51350,51602,52026,52281,52375,56172,56409,58433,58675,58777,59030,61186,87176,88692,98920,100448,102205,102831,103251,104312,105577,105833,106069,106616,107110,107715,107913,108493,109057,109432,109550,110088,110245,110441,110714,110970,111140,111281,111345,111627,111913,112589,112853,113191,113544,113638,113824,114130,114392,114517,114644,114883,115094,115213,115406,115583,116038,116219,116341,116600,116713,116900,117002,117109,117238,117513,118021,118517,119394,119688,120258,120407,121139,121311,121647,121739,122017,130346,134771,135177,135755,136339,136430,136543,136772,136932,137084,137255,137421,137590,137757,137920,138163,138333,138506,138677,138951,139150,139355,139685,139769,139865,139961,140059,140159,140261,140363,140465,140567,140669,140769,140865,140977,141106,141229,141360,141491,141589,141703,141797,141937,142071,142167,142279,142379,142495,142591,142703,142803,142943,143079,143243,143373,143531,143681,143822,143966,144101,144213,144363,144491,144619,144755,144887,145017,145147,145259,145399,154019,154163,154301,154367,154457,154533,154637,154727,154829,154937,155045,155145,155225,155317,155415,155525,155603,155709,155801,155905,156015,156137,156300,156457,160865,160965,161055,161165,161259,161365,161457,163387,163499,163613,163729,163845,163939,164053,164165,164267,164387,164509,164591,164695,164815,164941,165039,165133,165221,165333,165449,165571,165683,165858,165974,166060,166152,166264,166388,166455,166581,166649,166777,166921,167049,167118,167213,167328,167441,167540,167649,167760,167871,167972,168077,168177,168307,168398,168521,168615,168727,168813,168917,169013,169101,169219,169323,169427,169553,169641,169749,169849,169939,170049,170133,170235,170319,170373,170437,170543,170653,170737,170857"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/integers.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "446,447,448,449,450,451,452,453,454,455,456,457,458,459,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27022,27086,27148,27214,27276,27336,27393,27456,27528,27585,27642,27702,27760,27830,27887", "endColumns": "63,61,65,61,59,56,62,71,56,56,59,57,69,56,69", "endOffsets": "27081,27143,27209,27271,27331,27388,27451,27523,27580,27637,27697,27755,27825,27882,27952"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/styles.xml", "from": {"startLines": "-1,-1,-1,-1,69,-1,-1,61,-1,-1,-1,-1,-1,74", "startColumns": "-1,-1,-1,-1,4,-1,-1,4,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,2546,-1,-1,2257,-1,-1,-1,-1,-1,2771", "endLines": "-1,-1,-1,-1,72,-1,-1,66,-1,-1,-1,-1,-1,78", "endColumns": "-1,-1,-1,-1,12,-1,-1,12,-1,-1,-1,-1,-1,12", "endOffsets": "-1,-1,-1,-1,2765,-1,-1,2521,-1,-1,-1,-1,-1,2975"}, "to": {"startLines": "556,561,562,566,2524,2900,2904,2909,2915,2920,2925,2930,2935,2940", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "34764,35039,35129,35297,163068,187583,187734,187967,188236,188463,188696,188929,189150,189371", "endLines": "560,561,565,566,2527,2903,2908,2914,2919,2924,2929,2934,2939,2944", "endColumns": "12,89,12,79,12,12,12,12,12,12,12,12,12,12", "endOffsets": "35034,35124,35292,35372,163287,187729,187962,188231,188458,188691,188924,189145,189366,189575"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/e476e0dd04b5343b91450d154c8a112f/cardview-v7-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,83,84,85,86,588,1980,1982,1985", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "510,4677,4738,4800,4862,36828,125751,125815,125941", "endLines": "13,83,84,85,86,594,1981,1984,1987", "endColumns": "51,60,61,61,63,12,12,12,12", "endOffsets": "557,4733,4795,4857,4921,37237,125810,125936,126064"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/bools.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "48,49,50,51", "startColumns": "4,4,4,4", "startOffsets": "2442,2497,2556,2618", "endColumns": "54,58,61,61", "endOffsets": "2492,2551,2613,2675"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "9,10,11,12,14,15,16,17,20,22,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,96,97,98,99,100,101,102,103,104,105,106,136,137,138,139,140,141,142,143,419,420,427,428,435,436,437,445,552,827,828,829,834,835,839,845,849,850,851,852,863,864,865,869,875,879,949,950,951,981,1001,1047,1077,1097,1117,1163,1167,1907,1921,1962,1970,2105,2106,2107,2108,2257,2260,2261,2264,2267,2268,2271,2275,2280,2288,2296,2305,2313,2317,2325,2333,2341,2349,2357,2366,2375,2383,2392,2430,2432,2437,2439,2444,2448,2452,2453,2458,2459,2460,2461,2462,2463,2465,2466,2471,2472,2473,2474,2475,2476,2477,2479,2483,2487,2491,2502,2503,2504,2505,2506,2507,2508,2509,2510,2513,2517,2520,2624,2632,2639,2648,2652,2667,2675,2678,2687,2692,2703,2711,2714,2723,2730,2731,2750,2753,2759,2762,2771,2774,2777,2780,2783,2786,2790,2793,2802,2805,2813,2818,2826,2831,2835,2836,2847,2854,2858,2862,2863,2867,2875,2879,2884,2889", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,334,394,455,562,615,673,721,886,976,1078,1136,1196,1254,1300,1360,1413,1459,1509,1556,1614,1672,1731,1791,1853,1915,1977,2039,2101,2163,2224,2286,2348,5346,5420,5483,5551,5632,5696,5762,5832,5902,5972,6042,7834,7897,7962,8028,8081,8157,8223,8310,25721,25779,26127,26172,26498,26545,26590,26974,34487,52380,52473,52580,52923,53030,53259,53668,53900,54000,54105,54224,54822,54969,55088,55323,55738,55976,61191,61312,61445,63524,65020,68254,70329,71837,73361,76591,76815,122022,122826,124586,125036,134776,134849,134936,135021,146544,146739,146831,147004,147166,147261,147430,147673,147966,148375,148789,149221,149639,149880,150310,150745,151155,151577,151987,152416,152842,153258,153696,156462,156530,156874,156954,157310,157460,157604,157688,158053,158151,158259,158357,158467,158583,158709,158805,159182,159292,159416,159554,159664,159786,159914,160052,160214,160430,160586,161462,161546,161650,161744,161858,161970,162094,162190,162270,162459,162665,162858,171001,171433,171854,172279,172476,173424,173945,174068,174705,174926,175741,176210,176393,176989,177449,177554,178815,178965,179382,179547,180227,180386,180477,180561,180757,180924,181146,181306,181683,181842,182170,182387,182962,183312,183561,183658,184364,184802,185043,185232,185366,185557,186194,186444,186747,186962", "endLines": "9,10,11,12,14,15,16,17,20,22,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,96,97,98,99,100,101,102,103,104,105,106,136,137,138,139,140,141,142,143,419,420,427,428,435,436,437,445,555,827,828,833,834,838,844,848,849,850,851,862,863,864,868,874,878,879,949,950,980,1000,1046,1076,1096,1116,1162,1166,1170,1920,1961,1969,1979,2105,2106,2107,2108,2259,2260,2263,2266,2267,2270,2274,2279,2287,2295,2304,2312,2316,2324,2332,2340,2348,2356,2365,2374,2382,2391,2394,2431,2436,2438,2443,2447,2451,2452,2457,2458,2459,2460,2461,2462,2464,2465,2470,2471,2472,2473,2474,2475,2476,2478,2482,2486,2490,2494,2502,2503,2504,2505,2506,2507,2508,2509,2512,2516,2519,2523,2631,2638,2647,2651,2666,2674,2677,2686,2691,2702,2710,2713,2722,2729,2730,2749,2752,2758,2761,2770,2773,2776,2779,2782,2785,2789,2792,2801,2804,2812,2817,2825,2830,2834,2835,2846,2853,2857,2861,2862,2866,2874,2878,2883,2888,2896", "endColumns": "55,59,60,54,52,57,47,48,44,52,57,59,57,45,59,52,45,49,46,57,57,58,59,61,61,61,61,61,61,60,61,61,52,73,62,67,80,63,65,69,69,69,69,66,62,64,65,52,75,65,86,75,57,61,44,42,46,44,50,47,10,92,106,10,106,10,10,10,99,104,118,10,146,118,10,10,10,111,120,132,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,91,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,10,97,107,97,109,115,10,95,10,109,123,137,109,121,127,10,10,10,10,10,83,103,93,113,111,123,95,79,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,96,10,10,10,10,133,10,10,10,10,10,10", "endOffsets": "329,389,450,505,610,668,716,765,926,1024,1131,1191,1249,1295,1355,1408,1454,1504,1551,1609,1667,1726,1786,1848,1910,1972,2034,2096,2158,2219,2281,2343,2396,5415,5478,5546,5627,5691,5757,5827,5897,5967,6037,6104,7892,7957,8023,8076,8152,8218,8305,8381,25774,25836,26167,26210,26540,26585,26636,27017,34759,52468,52575,52918,53025,53254,53663,53895,53995,54100,54219,54817,54964,55083,55318,55733,55971,56083,61307,61440,63519,65015,68249,70324,71832,73356,76586,76810,77047,122821,124581,125031,125746,134844,134931,135016,135115,146734,146826,146999,147161,147256,147425,147668,147961,148370,148784,149216,149634,149875,150305,150740,151150,151572,151982,152411,152837,153253,153691,153873,156525,156869,156949,157305,157455,157599,157683,158048,158146,158254,158352,158462,158578,158704,158800,159177,159287,159411,159549,159659,159781,159909,160047,160209,160425,160581,160785,161541,161645,161739,161853,161965,162089,162185,162265,162454,162660,162853,163063,171428,171849,172274,172471,173419,173940,174063,174700,174921,175736,176205,176388,176984,177444,177549,178810,178960,179377,179542,180222,180381,180472,180556,180752,180919,181141,181301,181678,181837,182165,182382,182957,183307,183556,183653,184359,184797,185038,185227,185361,185552,186189,186439,186742,186957,187433"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/8fd5d53888178dd9013834ef7b031501/support-media-compat-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "152,2246,2248,2249,2254,2256", "startColumns": "4,4,4,4,4,4", "startOffsets": "8835,145722,145898,146020,146282,146477", "endColumns": "88,65,121,60,65,66", "endOffsets": "8919,145783,146015,146076,146343,146539"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/drawables.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "402,403,404,405,406,407,408,409", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "24676,24756,24838,24918,24994,25072,25158,25230", "endColumns": "79,81,79,75,77,85,71,75", "endOffsets": "24751,24833,24913,24989,25067,25153,25225,25301"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27957,28031,28101,28179,28233,28303,28388,28436,28482,28553,28631,28709,28781,28855,28929,29003,29083,29156,29225,29297,29374,29435,29498,29564,29628,29699,29762,29827,29891,29952,30013,30065,30138,30212,30281,30356,30430,30504,30593,30663,30716,30759,30883,30961,31061,31099,31203,31269,31317,31448,31579,31649,31776,31857,31933,32007,32060,32097,32175,32455,32600,32734,32815,32896,32949,32999,33046,33091,33143,33196,33247,33286,33335,33388,33435,33506,33553,33616,33661,33700,33753,33811,33856,33899,33950,33998", "endColumns": "73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,88,69,52,42,123,77,99,37,103,65,47,130,130,69,126,80,75,73,52,36,77,279,144,133,80,80,52,49,46,44,51,52,50,38,48,52,46,70,46,62,44,38,52,57,44,42,50,47,46", "endOffsets": "28026,28096,28174,28228,28298,28383,28431,28477,28548,28626,28704,28776,28850,28924,28998,29078,29151,29220,29292,29369,29430,29493,29559,29623,29694,29757,29822,29886,29947,30008,30060,30133,30207,30276,30351,30425,30499,30588,30658,30711,30754,30878,30956,31056,31094,31198,31264,31312,31443,31574,31644,31771,31852,31928,32002,32055,32092,32170,32450,32595,32729,32810,32891,32944,32994,33041,33086,33138,33191,33242,33281,33330,33383,33430,33501,33548,33611,33656,33695,33748,33806,33851,33894,33945,33993,34040"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10462,10531,10600,10670,10744,10820,10884,10961,11037,11114,11179,11248,11325,11400,11469,11537,11614,11680,11741,11838,11903,11972,12071,12142,12201,12259,12316,12375,12439,12499,12560,12621,12682,12754,12821,12878,12935,12994,13057,13121,13184,13249,13308,13373,13439,13505,13575,13639,13692,13805,13863,13926,13990,14054,14129,14202,14274,14323,14384,14445,14506,14568,14632,14696,14760,14825,14888,14948,15009,15075,15134,15194,15256,15327,15387,15455,15513,15569,15615,15664,15723,15780,15834,15904,15972,16044,16114,16175,16249,16322,16376,16455,16533,16606,16671,16734,16800,16871,16942,17004,17073,17139,17206,17273,17329,17380,17433,17485,17539,17610,17673,17732,17794,17853,17926,17993,18053,18116,18191,18263,18334,18390,18461,18518,18575,18641,18705,18776,18833,18886,18949,19001,19059,19126,19185,19246,19288,19347,19395,19451,19515,19575,19637,19692,19749,19812,19877,19952,20028,20100,20166,20232,20313,20388,20444,20497,20558,20616,20666,20715,20764,20813,20875,20927,20972,21027,21081,21134,21188,21239,21288,21339,21400,21461,21523,21573,21614,21664,21712,21774,21825,21874,21943,22004,22060,22131,22196,22265,22316,22379,22449,22518,22588,22650,22720,22790,22865,22924,22974,23033,23094,23155,23217,23281,23343,23404,23472,23572,23632,23698,23771,23840,23897,23949,24011,24064,24117,24170,24223,24275,24333,24378,24444,24508,24565,24622", "endColumns": "68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,59,60,60,60,71,66,56,56,58,62,63,62,64,58,64,65,65,69,63,52,112,57,62,63,63,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,57,55,45,48,58,56,53,69,67,71,69,60,73,72,53,78,77,72,64,62,65,70,70,61,68,65,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,70,55,70,56,56,65,63,70,56,52,62,51,57,66,58,60,41,58,47,55,63,59,61,54,56,62,64,74,75,71,65,65,80,74,55,52,60,57,49,48,48,48,61,51,44,54,53,52,53,50,48,50,60,60,61,49,40,49,47,61,50,48,68,60,55,70,64,68,50,62,69,68,69,61,69,69,74,58,49,58,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,52,52,52,52,51,57,44,65,63,56,56,53", "endOffsets": "10526,10595,10665,10739,10815,10879,10956,11032,11109,11174,11243,11320,11395,11464,11532,11609,11675,11736,11833,11898,11967,12066,12137,12196,12254,12311,12370,12434,12494,12555,12616,12677,12749,12816,12873,12930,12989,13052,13116,13179,13244,13303,13368,13434,13500,13570,13634,13687,13800,13858,13921,13985,14049,14124,14197,14269,14318,14379,14440,14501,14563,14627,14691,14755,14820,14883,14943,15004,15070,15129,15189,15251,15322,15382,15450,15508,15564,15610,15659,15718,15775,15829,15899,15967,16039,16109,16170,16244,16317,16371,16450,16528,16601,16666,16729,16795,16866,16937,16999,17068,17134,17201,17268,17324,17375,17428,17480,17534,17605,17668,17727,17789,17848,17921,17988,18048,18111,18186,18258,18329,18385,18456,18513,18570,18636,18700,18771,18828,18881,18944,18996,19054,19121,19180,19241,19283,19342,19390,19446,19510,19570,19632,19687,19744,19807,19872,19947,20023,20095,20161,20227,20308,20383,20439,20492,20553,20611,20661,20710,20759,20808,20870,20922,20967,21022,21076,21129,21183,21234,21283,21334,21395,21456,21518,21568,21609,21659,21707,21769,21820,21869,21938,21999,22055,22126,22191,22260,22311,22374,22444,22513,22583,22645,22715,22785,22860,22919,22969,23028,23089,23150,23212,23276,23338,23399,23467,23567,23627,23693,23766,23835,23892,23944,24006,24059,24112,24165,24218,24270,24328,24373,24439,24503,24560,24617,24671"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c8781cf76c3d46af7130a3e9cb97651e/transition-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "414,421,424,425,426,439,440,441,442,443", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "25511,25841,25980,26027,26082,26676,26730,26782,26831,26892", "endColumns": "39,42,46,54,44,53,51,48,60,49", "endOffsets": "25546,25879,26022,26077,26122,26725,26777,26826,26887,26937"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/96d6faed5a5735fd14bdec9d38b8895e/recyclerview-v7-28.0.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "416", "startColumns": "4", "startOffsets": "25585", "endColumns": "65", "endOffsets": "25646"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "150,151,417,418,430,431,432,433,434,438,2241,2242,2247,2250,2255,2622,2623", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8703,8772,25651,25686,26261,26311,26372,26429,26463,26641,145404,145521,145788,146081,146348,170862,170934", "endLines": "150,151,417,418,430,431,432,433,434,438,2241,2245,2247,2253,2255,2622,2623", "endColumns": "68,62,34,34,49,60,56,33,34,34,116,12,109,12,128,71,66", "endOffsets": "8767,8830,25681,25716,26306,26367,26424,26458,26493,26671,145516,145717,145893,146277,146472,170929,170996"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/a431d7a91ec2d1f028c3cc59a84cc899/coordinatorlayout-28.0.0/res/values/values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "18,2897", "startColumns": "4,4", "startOffsets": "770,187438", "endLines": "18,2899", "endColumns": "60,12", "endOffsets": "826,187578"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "52,53,54,55,56,57,64,65,66,67,72,73,74,87,88,89,90,91,92,93,94,95,111,116,119,120,133,134,135,144,145,146,147,148,149,161,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2680,2719,2766,2810,2856,2903,3402,3447,3493,3539,3904,3944,3991,4926,4972,5019,5070,5116,5162,5208,5254,5300,6389,6710,6904,6949,7667,7722,7780,8386,8438,8491,8544,8597,8650,9512,10368,10408", "endColumns": "38,46,43,45,46,50,44,45,45,45,39,46,44,45,46,50,45,45,45,45,45,45,54,58,44,45,54,57,53,51,52,52,52,52,52,37,39,53", "endOffsets": "2714,2761,2805,2851,2898,2949,3442,3488,3534,3580,3939,3986,4031,4967,5014,5065,5111,5157,5203,5249,5295,5341,6439,6764,6944,6990,7717,7775,7829,8433,8486,8539,8592,8645,8698,9545,10403,10457"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values/arrays.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "8", "endColumns": "12", "endOffsets": "273"}}]}]}