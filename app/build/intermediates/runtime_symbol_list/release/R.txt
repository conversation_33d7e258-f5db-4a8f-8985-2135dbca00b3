int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim alpha 0x7f01000c
int anim bounce 0x7f01000d
int anim design_bottom_sheet_slide_in 0x7f01000e
int anim design_bottom_sheet_slide_out 0x7f01000f
int anim design_snackbar_in 0x7f010010
int anim design_snackbar_out 0x7f010011
int anim slidein 0x7f010012
int anim slideout 0x7f010013
int anim translate 0x7f010014
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator mtrl_btn_state_list_anim 0x7f020003
int animator mtrl_btn_unelevated_state_list_anim 0x7f020004
int animator mtrl_chip_state_list_anim 0x7f020005
int animator mtrl_fab_hide_motion_spec 0x7f020006
int animator mtrl_fab_show_motion_spec 0x7f020007
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020008
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020009
int array drawer_items 0x7f030000
int attr actionBarDivider 0x7f040000
int attr actionBarItemBackground 0x7f040001
int attr actionBarPopupTheme 0x7f040002
int attr actionBarSize 0x7f040003
int attr actionBarSplitStyle 0x7f040004
int attr actionBarStyle 0x7f040005
int attr actionBarTabBarStyle 0x7f040006
int attr actionBarTabStyle 0x7f040007
int attr actionBarTabTextStyle 0x7f040008
int attr actionBarTheme 0x7f040009
int attr actionBarWidgetTheme 0x7f04000a
int attr actionButtonStyle 0x7f04000b
int attr actionDropDownStyle 0x7f04000c
int attr actionLayout 0x7f04000d
int attr actionMenuTextAppearance 0x7f04000e
int attr actionMenuTextColor 0x7f04000f
int attr actionModeBackground 0x7f040010
int attr actionModeCloseButtonStyle 0x7f040011
int attr actionModeCloseDrawable 0x7f040012
int attr actionModeCopyDrawable 0x7f040013
int attr actionModeCutDrawable 0x7f040014
int attr actionModeFindDrawable 0x7f040015
int attr actionModePasteDrawable 0x7f040016
int attr actionModePopupWindowStyle 0x7f040017
int attr actionModeSelectAllDrawable 0x7f040018
int attr actionModeShareDrawable 0x7f040019
int attr actionModeSplitBackground 0x7f04001a
int attr actionModeStyle 0x7f04001b
int attr actionModeWebSearchDrawable 0x7f04001c
int attr actionOverflowButtonStyle 0x7f04001d
int attr actionOverflowMenuStyle 0x7f04001e
int attr actionProviderClass 0x7f04001f
int attr actionViewClass 0x7f040020
int attr activityChooserViewStyle 0x7f040021
int attr alertDialogButtonGroupStyle 0x7f040022
int attr alertDialogCenterButtons 0x7f040023
int attr alertDialogStyle 0x7f040024
int attr alertDialogTheme 0x7f040025
int attr allowStacking 0x7f040026
int attr alpha 0x7f040027
int attr alphabeticModifiers 0x7f040028
int attr arrowHeadLength 0x7f040029
int attr arrowShaftLength 0x7f04002a
int attr autoCompleteTextViewStyle 0x7f04002b
int attr autoSizeMaxTextSize 0x7f04002c
int attr autoSizeMinTextSize 0x7f04002d
int attr autoSizePresetSizes 0x7f04002e
int attr autoSizeStepGranularity 0x7f04002f
int attr autoSizeTextType 0x7f040030
int attr background 0x7f040031
int attr backgroundSplit 0x7f040032
int attr backgroundStacked 0x7f040033
int attr backgroundTint 0x7f040034
int attr backgroundTintMode 0x7f040035
int attr barLength 0x7f040036
int attr behavior_autoHide 0x7f040037
int attr behavior_fitToContents 0x7f040038
int attr behavior_hideable 0x7f040039
int attr behavior_overlapTop 0x7f04003a
int attr behavior_peekHeight 0x7f04003b
int attr behavior_skipCollapsed 0x7f04003c
int attr borderWidth 0x7f04003d
int attr borderlessButtonStyle 0x7f04003e
int attr bottomAppBarStyle 0x7f04003f
int attr bottomNavigationStyle 0x7f040040
int attr bottomSheetDialogTheme 0x7f040041
int attr bottomSheetStyle 0x7f040042
int attr boxBackgroundColor 0x7f040043
int attr boxBackgroundMode 0x7f040044
int attr boxCollapsedPaddingTop 0x7f040045
int attr boxCornerRadiusBottomEnd 0x7f040046
int attr boxCornerRadiusBottomStart 0x7f040047
int attr boxCornerRadiusTopEnd 0x7f040048
int attr boxCornerRadiusTopStart 0x7f040049
int attr boxStrokeColor 0x7f04004a
int attr boxStrokeWidth 0x7f04004b
int attr buttonBarButtonStyle 0x7f04004c
int attr buttonBarNegativeButtonStyle 0x7f04004d
int attr buttonBarNeutralButtonStyle 0x7f04004e
int attr buttonBarPositiveButtonStyle 0x7f04004f
int attr buttonBarStyle 0x7f040050
int attr buttonGravity 0x7f040051
int attr buttonIconDimen 0x7f040052
int attr buttonPanelSideLayout 0x7f040053
int attr buttonStyle 0x7f040054
int attr buttonStyleSmall 0x7f040055
int attr buttonTint 0x7f040056
int attr buttonTintMode 0x7f040057
int attr cardBackgroundColor 0x7f040058
int attr cardCornerRadius 0x7f040059
int attr cardElevation 0x7f04005a
int attr cardMaxElevation 0x7f04005b
int attr cardPreventCornerOverlap 0x7f04005c
int attr cardUseCompatPadding 0x7f04005d
int attr cardViewStyle 0x7f04005e
int attr checkboxStyle 0x7f04005f
int attr checkedChip 0x7f040060
int attr checkedIcon 0x7f040061
int attr checkedIconEnabled 0x7f040062
int attr checkedIconVisible 0x7f040063
int attr checkedTextViewStyle 0x7f040064
int attr chipBackgroundColor 0x7f040065
int attr chipCornerRadius 0x7f040066
int attr chipEndPadding 0x7f040067
int attr chipGroupStyle 0x7f040068
int attr chipIcon 0x7f040069
int attr chipIconEnabled 0x7f04006a
int attr chipIconSize 0x7f04006b
int attr chipIconTint 0x7f04006c
int attr chipIconVisible 0x7f04006d
int attr chipMinHeight 0x7f04006e
int attr chipSpacing 0x7f04006f
int attr chipSpacingHorizontal 0x7f040070
int attr chipSpacingVertical 0x7f040071
int attr chipStandaloneStyle 0x7f040072
int attr chipStartPadding 0x7f040073
int attr chipStrokeColor 0x7f040074
int attr chipStrokeWidth 0x7f040075
int attr chipStyle 0x7f040076
int attr closeIcon 0x7f040077
int attr closeIconEnabled 0x7f040078
int attr closeIconEndPadding 0x7f040079
int attr closeIconSize 0x7f04007a
int attr closeIconStartPadding 0x7f04007b
int attr closeIconTint 0x7f04007c
int attr closeIconVisible 0x7f04007d
int attr closeItemLayout 0x7f04007e
int attr collapseContentDescription 0x7f04007f
int attr collapseIcon 0x7f040080
int attr collapsedTitleGravity 0x7f040081
int attr collapsedTitleTextAppearance 0x7f040082
int attr color 0x7f040083
int attr colorAccent 0x7f040084
int attr colorBackgroundFloating 0x7f040085
int attr colorButtonNormal 0x7f040086
int attr colorControlActivated 0x7f040087
int attr colorControlHighlight 0x7f040088
int attr colorControlNormal 0x7f040089
int attr colorError 0x7f04008a
int attr colorPrimary 0x7f04008b
int attr colorPrimaryDark 0x7f04008c
int attr colorSecondary 0x7f04008d
int attr colorSwitchThumbNormal 0x7f04008e
int attr commitIcon 0x7f04008f
int attr contentDescription 0x7f040090
int attr contentInsetEnd 0x7f040091
int attr contentInsetEndWithActions 0x7f040092
int attr contentInsetLeft 0x7f040093
int attr contentInsetRight 0x7f040094
int attr contentInsetStart 0x7f040095
int attr contentInsetStartWithNavigation 0x7f040096
int attr contentPadding 0x7f040097
int attr contentPaddingBottom 0x7f040098
int attr contentPaddingLeft 0x7f040099
int attr contentPaddingRight 0x7f04009a
int attr contentPaddingTop 0x7f04009b
int attr contentScrim 0x7f04009c
int attr controlBackground 0x7f04009d
int attr coordinatorLayoutStyle 0x7f04009e
int attr cornerRadius 0x7f04009f
int attr counterEnabled 0x7f0400a0
int attr counterMaxLength 0x7f0400a1
int attr counterOverflowTextAppearance 0x7f0400a2
int attr counterTextAppearance 0x7f0400a3
int attr customNavigationLayout 0x7f0400a4
int attr defaultQueryHint 0x7f0400a5
int attr dialogCornerRadius 0x7f0400a6
int attr dialogPreferredPadding 0x7f0400a7
int attr dialogTheme 0x7f0400a8
int attr displayOptions 0x7f0400a9
int attr divider 0x7f0400aa
int attr dividerHorizontal 0x7f0400ab
int attr dividerPadding 0x7f0400ac
int attr dividerVertical 0x7f0400ad
int attr drawableSize 0x7f0400ae
int attr drawerArrowStyle 0x7f0400af
int attr dropDownListViewStyle 0x7f0400b0
int attr dropdownListPreferredItemHeight 0x7f0400b1
int attr editTextBackground 0x7f0400b2
int attr editTextColor 0x7f0400b3
int attr editTextStyle 0x7f0400b4
int attr elevation 0x7f0400b5
int attr enforceMaterialTheme 0x7f0400b6
int attr enforceTextAppearance 0x7f0400b7
int attr errorEnabled 0x7f0400b8
int attr errorTextAppearance 0x7f0400b9
int attr expandActivityOverflowButtonDrawable 0x7f0400ba
int attr expanded 0x7f0400bb
int attr expandedTitleGravity 0x7f0400bc
int attr expandedTitleMargin 0x7f0400bd
int attr expandedTitleMarginBottom 0x7f0400be
int attr expandedTitleMarginEnd 0x7f0400bf
int attr expandedTitleMarginStart 0x7f0400c0
int attr expandedTitleMarginTop 0x7f0400c1
int attr expandedTitleTextAppearance 0x7f0400c2
int attr fabAlignmentMode 0x7f0400c3
int attr fabCradleMargin 0x7f0400c4
int attr fabCradleRoundedCornerRadius 0x7f0400c5
int attr fabCradleVerticalOffset 0x7f0400c6
int attr fabCustomSize 0x7f0400c7
int attr fabSize 0x7f0400c8
int attr fastScrollEnabled 0x7f0400c9
int attr fastScrollHorizontalThumbDrawable 0x7f0400ca
int attr fastScrollHorizontalTrackDrawable 0x7f0400cb
int attr fastScrollVerticalThumbDrawable 0x7f0400cc
int attr fastScrollVerticalTrackDrawable 0x7f0400cd
int attr firstBaselineToTopHeight 0x7f0400ce
int attr floatingActionButtonStyle 0x7f0400cf
int attr font 0x7f0400d0
int attr fontFamily 0x7f0400d1
int attr fontProviderAuthority 0x7f0400d2
int attr fontProviderCerts 0x7f0400d3
int attr fontProviderFetchStrategy 0x7f0400d4
int attr fontProviderFetchTimeout 0x7f0400d5
int attr fontProviderPackage 0x7f0400d6
int attr fontProviderQuery 0x7f0400d7
int attr fontStyle 0x7f0400d8
int attr fontVariationSettings 0x7f0400d9
int attr fontWeight 0x7f0400da
int attr foregroundInsidePadding 0x7f0400db
int attr gapBetweenBars 0x7f0400dc
int attr goIcon 0x7f0400dd
int attr headerLayout 0x7f0400de
int attr height 0x7f0400df
int attr helperText 0x7f0400e0
int attr helperTextEnabled 0x7f0400e1
int attr helperTextTextAppearance 0x7f0400e2
int attr hideMotionSpec 0x7f0400e3
int attr hideOnContentScroll 0x7f0400e4
int attr hideOnScroll 0x7f0400e5
int attr hintAnimationEnabled 0x7f0400e6
int attr hintEnabled 0x7f0400e7
int attr hintTextAppearance 0x7f0400e8
int attr homeAsUpIndicator 0x7f0400e9
int attr homeLayout 0x7f0400ea
int attr hoveredFocusedTranslationZ 0x7f0400eb
int attr icon 0x7f0400ec
int attr iconEndPadding 0x7f0400ed
int attr iconGravity 0x7f0400ee
int attr iconPadding 0x7f0400ef
int attr iconSize 0x7f0400f0
int attr iconStartPadding 0x7f0400f1
int attr iconTint 0x7f0400f2
int attr iconTintMode 0x7f0400f3
int attr iconifiedByDefault 0x7f0400f4
int attr imageButtonStyle 0x7f0400f5
int attr indeterminateProgressStyle 0x7f0400f6
int attr initialActivityCount 0x7f0400f7
int attr insetForeground 0x7f0400f8
int attr isLightTheme 0x7f0400f9
int attr itemBackground 0x7f0400fa
int attr itemHorizontalPadding 0x7f0400fb
int attr itemHorizontalTranslationEnabled 0x7f0400fc
int attr itemIconPadding 0x7f0400fd
int attr itemIconSize 0x7f0400fe
int attr itemIconTint 0x7f0400ff
int attr itemPadding 0x7f040100
int attr itemSpacing 0x7f040101
int attr itemTextAppearance 0x7f040102
int attr itemTextAppearanceActive 0x7f040103
int attr itemTextAppearanceInactive 0x7f040104
int attr itemTextColor 0x7f040105
int attr keylines 0x7f040106
int attr labelVisibilityMode 0x7f040107
int attr lastBaselineToBottomHeight 0x7f040108
int attr layout 0x7f040109
int attr layoutManager 0x7f04010a
int attr layout_anchor 0x7f04010b
int attr layout_anchorGravity 0x7f04010c
int attr layout_behavior 0x7f04010d
int attr layout_collapseMode 0x7f04010e
int attr layout_collapseParallaxMultiplier 0x7f04010f
int attr layout_dodgeInsetEdges 0x7f040110
int attr layout_insetEdge 0x7f040111
int attr layout_keyline 0x7f040112
int attr layout_scrollFlags 0x7f040113
int attr layout_scrollInterpolator 0x7f040114
int attr liftOnScroll 0x7f040115
int attr lineHeight 0x7f040116
int attr lineSpacing 0x7f040117
int attr listChoiceBackgroundIndicator 0x7f040118
int attr listDividerAlertDialog 0x7f040119
int attr listItemLayout 0x7f04011a
int attr listLayout 0x7f04011b
int attr listMenuViewStyle 0x7f04011c
int attr listPopupWindowStyle 0x7f04011d
int attr listPreferredItemHeight 0x7f04011e
int attr listPreferredItemHeightLarge 0x7f04011f
int attr listPreferredItemHeightSmall 0x7f040120
int attr listPreferredItemPaddingLeft 0x7f040121
int attr listPreferredItemPaddingRight 0x7f040122
int attr logo 0x7f040123
int attr logoDescription 0x7f040124
int attr materialButtonStyle 0x7f040125
int attr materialCardViewStyle 0x7f040126
int attr maxActionInlineWidth 0x7f040127
int attr maxButtonHeight 0x7f040128
int attr maxImageSize 0x7f040129
int attr measureWithLargestChild 0x7f04012a
int attr menu 0x7f04012b
int attr multiChoiceItemLayout 0x7f04012c
int attr navigationContentDescription 0x7f04012d
int attr navigationIcon 0x7f04012e
int attr navigationMode 0x7f04012f
int attr navigationViewStyle 0x7f040130
int attr numericModifiers 0x7f040131
int attr overlapAnchor 0x7f040132
int attr paddingBottomNoButtons 0x7f040133
int attr paddingEnd 0x7f040134
int attr paddingStart 0x7f040135
int attr paddingTopNoTitle 0x7f040136
int attr panelBackground 0x7f040137
int attr panelMenuListTheme 0x7f040138
int attr panelMenuListWidth 0x7f040139
int attr passwordToggleContentDescription 0x7f04013a
int attr passwordToggleDrawable 0x7f04013b
int attr passwordToggleEnabled 0x7f04013c
int attr passwordToggleTint 0x7f04013d
int attr passwordToggleTintMode 0x7f04013e
int attr popupMenuStyle 0x7f04013f
int attr popupTheme 0x7f040140
int attr popupWindowStyle 0x7f040141
int attr preserveIconSpacing 0x7f040142
int attr pressedTranslationZ 0x7f040143
int attr progressBarPadding 0x7f040144
int attr progressBarStyle 0x7f040145
int attr queryBackground 0x7f040146
int attr queryHint 0x7f040147
int attr radioButtonStyle 0x7f040148
int attr ratingBarStyle 0x7f040149
int attr ratingBarStyleIndicator 0x7f04014a
int attr ratingBarStyleSmall 0x7f04014b
int attr reverseLayout 0x7f04014c
int attr rippleColor 0x7f04014d
int attr scrimAnimationDuration 0x7f04014e
int attr scrimBackground 0x7f04014f
int attr scrimVisibleHeightTrigger 0x7f040150
int attr searchHintIcon 0x7f040151
int attr searchIcon 0x7f040152
int attr searchViewStyle 0x7f040153
int attr seekBarStyle 0x7f040154
int attr selectableItemBackground 0x7f040155
int attr selectableItemBackgroundBorderless 0x7f040156
int attr showAsAction 0x7f040157
int attr showDividers 0x7f040158
int attr showMotionSpec 0x7f040159
int attr showText 0x7f04015a
int attr showTitle 0x7f04015b
int attr singleChoiceItemLayout 0x7f04015c
int attr singleLine 0x7f04015d
int attr singleSelection 0x7f04015e
int attr snackbarButtonStyle 0x7f04015f
int attr snackbarStyle 0x7f040160
int attr spanCount 0x7f040161
int attr spinBars 0x7f040162
int attr spinnerDropDownItemStyle 0x7f040163
int attr spinnerStyle 0x7f040164
int attr splitTrack 0x7f040165
int attr srcCompat 0x7f040166
int attr stackFromEnd 0x7f040167
int attr state_above_anchor 0x7f040168
int attr state_collapsed 0x7f040169
int attr state_collapsible 0x7f04016a
int attr state_liftable 0x7f04016b
int attr state_lifted 0x7f04016c
int attr statusBarBackground 0x7f04016d
int attr statusBarScrim 0x7f04016e
int attr strokeColor 0x7f04016f
int attr strokeWidth 0x7f040170
int attr subMenuArrow 0x7f040171
int attr submitBackground 0x7f040172
int attr subtitle 0x7f040173
int attr subtitleTextAppearance 0x7f040174
int attr subtitleTextColor 0x7f040175
int attr subtitleTextStyle 0x7f040176
int attr suggestionRowLayout 0x7f040177
int attr switchMinWidth 0x7f040178
int attr switchPadding 0x7f040179
int attr switchStyle 0x7f04017a
int attr switchTextAppearance 0x7f04017b
int attr tabBackground 0x7f04017c
int attr tabContentStart 0x7f04017d
int attr tabGravity 0x7f04017e
int attr tabIconTint 0x7f04017f
int attr tabIconTintMode 0x7f040180
int attr tabIndicator 0x7f040181
int attr tabIndicatorAnimationDuration 0x7f040182
int attr tabIndicatorColor 0x7f040183
int attr tabIndicatorFullWidth 0x7f040184
int attr tabIndicatorGravity 0x7f040185
int attr tabIndicatorHeight 0x7f040186
int attr tabInlineLabel 0x7f040187
int attr tabMaxWidth 0x7f040188
int attr tabMinWidth 0x7f040189
int attr tabMode 0x7f04018a
int attr tabPadding 0x7f04018b
int attr tabPaddingBottom 0x7f04018c
int attr tabPaddingEnd 0x7f04018d
int attr tabPaddingStart 0x7f04018e
int attr tabPaddingTop 0x7f04018f
int attr tabRippleColor 0x7f040190
int attr tabSelectedTextColor 0x7f040191
int attr tabStyle 0x7f040192
int attr tabTextAppearance 0x7f040193
int attr tabTextColor 0x7f040194
int attr tabUnboundedRipple 0x7f040195
int attr textAllCaps 0x7f040196
int attr textAppearanceBody1 0x7f040197
int attr textAppearanceBody2 0x7f040198
int attr textAppearanceButton 0x7f040199
int attr textAppearanceCaption 0x7f04019a
int attr textAppearanceHeadline1 0x7f04019b
int attr textAppearanceHeadline2 0x7f04019c
int attr textAppearanceHeadline3 0x7f04019d
int attr textAppearanceHeadline4 0x7f04019e
int attr textAppearanceHeadline5 0x7f04019f
int attr textAppearanceHeadline6 0x7f0401a0
int attr textAppearanceLargePopupMenu 0x7f0401a1
int attr textAppearanceListItem 0x7f0401a2
int attr textAppearanceListItemSecondary 0x7f0401a3
int attr textAppearanceListItemSmall 0x7f0401a4
int attr textAppearanceOverline 0x7f0401a5
int attr textAppearancePopupMenuHeader 0x7f0401a6
int attr textAppearanceSearchResultSubtitle 0x7f0401a7
int attr textAppearanceSearchResultTitle 0x7f0401a8
int attr textAppearanceSmallPopupMenu 0x7f0401a9
int attr textAppearanceSubtitle1 0x7f0401aa
int attr textAppearanceSubtitle2 0x7f0401ab
int attr textColorAlertDialogListItem 0x7f0401ac
int attr textColorSearchUrl 0x7f0401ad
int attr textEndPadding 0x7f0401ae
int attr textInputStyle 0x7f0401af
int attr textStartPadding 0x7f0401b0
int attr theme 0x7f0401b1
int attr thickness 0x7f0401b2
int attr thumbTextPadding 0x7f0401b3
int attr thumbTint 0x7f0401b4
int attr thumbTintMode 0x7f0401b5
int attr tickMark 0x7f0401b6
int attr tickMarkTint 0x7f0401b7
int attr tickMarkTintMode 0x7f0401b8
int attr tint 0x7f0401b9
int attr tintMode 0x7f0401ba
int attr title 0x7f0401bb
int attr titleEnabled 0x7f0401bc
int attr titleMargin 0x7f0401bd
int attr titleMarginBottom 0x7f0401be
int attr titleMarginEnd 0x7f0401bf
int attr titleMarginStart 0x7f0401c0
int attr titleMarginTop 0x7f0401c1
int attr titleMargins 0x7f0401c2
int attr titleTextAppearance 0x7f0401c3
int attr titleTextColor 0x7f0401c4
int attr titleTextStyle 0x7f0401c5
int attr toolbarId 0x7f0401c6
int attr toolbarNavigationButtonStyle 0x7f0401c7
int attr toolbarStyle 0x7f0401c8
int attr tooltipForegroundColor 0x7f0401c9
int attr tooltipFrameBackground 0x7f0401ca
int attr tooltipText 0x7f0401cb
int attr track 0x7f0401cc
int attr trackTint 0x7f0401cd
int attr trackTintMode 0x7f0401ce
int attr ttcIndex 0x7f0401cf
int attr useCompatPadding 0x7f0401d0
int attr viewInflaterClass 0x7f0401d1
int attr voiceIcon 0x7f0401d2
int attr windowActionBar 0x7f0401d3
int attr windowActionBarOverlay 0x7f0401d4
int attr windowActionModeOverlay 0x7f0401d5
int attr windowFixedHeightMajor 0x7f0401d6
int attr windowFixedHeightMinor 0x7f0401d7
int attr windowFixedWidthMajor 0x7f0401d8
int attr windowFixedWidthMinor 0x7f0401d9
int attr windowMinWidthMajor 0x7f0401da
int attr windowMinWidthMinor 0x7f0401db
int attr windowNoTitle 0x7f0401dc
int bool abc_action_bar_embed_tabs 0x7f050000
int bool abc_allow_stacked_button_bar 0x7f050001
int bool abc_config_actionMenuItemAllCaps 0x7f050002
int bool mtrl_btn_textappearance_all_caps 0x7f050003
int bool sb__is_phone 0x7f050004
int bool sb__is_swipeable 0x7f050005
int color Gray 0x7f060000
int color Gray_divider 0x7f060001
int color LightGray 0x7f060002
int color Login_Split 0x7f060003
int color SubTitleBack 0x7f060004
int color SubTitleBackLine 0x7f060005
int color abc_background_cache_hint_selector_material_dark 0x7f060006
int color abc_background_cache_hint_selector_material_light 0x7f060007
int color abc_btn_colored_borderless_text_material 0x7f060008
int color abc_btn_colored_text_material 0x7f060009
int color abc_color_highlight_material 0x7f06000a
int color abc_hint_foreground_material_dark 0x7f06000b
int color abc_hint_foreground_material_light 0x7f06000c
int color abc_input_method_navigation_guard 0x7f06000d
int color abc_primary_text_disable_only_material_dark 0x7f06000e
int color abc_primary_text_disable_only_material_light 0x7f06000f
int color abc_primary_text_material_dark 0x7f060010
int color abc_primary_text_material_light 0x7f060011
int color abc_search_url_text 0x7f060012
int color abc_search_url_text_normal 0x7f060013
int color abc_search_url_text_pressed 0x7f060014
int color abc_search_url_text_selected 0x7f060015
int color abc_secondary_text_material_dark 0x7f060016
int color abc_secondary_text_material_light 0x7f060017
int color abc_tint_btn_checkable 0x7f060018
int color abc_tint_default 0x7f060019
int color abc_tint_edittext 0x7f06001a
int color abc_tint_seek_thumb 0x7f06001b
int color abc_tint_spinner 0x7f06001c
int color abc_tint_switch_track 0x7f06001d
int color accent_material_dark 0x7f06001e
int color accent_material_light 0x7f06001f
int color back_color 0x7f060020
int color back_color1 0x7f060021
int color back_color2 0x7f060022
int color back_color3 0x7f060023
int color background_floating_material_dark 0x7f060024
int color background_floating_material_light 0x7f060025
int color background_material_dark 0x7f060026
int color background_material_light 0x7f060027
int color black 0x7f060028
int color blue_default 0x7f060029
int color blue_light 0x7f06002a
int color bright_foreground_disabled_material_dark 0x7f06002b
int color bright_foreground_disabled_material_light 0x7f06002c
int color bright_foreground_inverse_material_dark 0x7f06002d
int color bright_foreground_inverse_material_light 0x7f06002e
int color bright_foreground_material_dark 0x7f06002f
int color bright_foreground_material_light 0x7f060030
int color button_material_dark 0x7f060031
int color button_material_light 0x7f060032
int color cardview_dark_background 0x7f060033
int color cardview_light_background 0x7f060034
int color cardview_shadow_end_color 0x7f060035
int color cardview_shadow_start_color 0x7f060036
int color colorAccent 0x7f060037
int color colorPrimary 0x7f060038
int color colorPrimaryDark 0x7f060039
int color color_sync0 0x7f06003a
int color color_sync1 0x7f06003b
int color color_sync2 0x7f06003c
int color color_sync3 0x7f06003d
int color color_sync4 0x7f06003e
int color color_sync5 0x7f06003f
int color design_bottom_navigation_shadow_color 0x7f060040
int color design_default_color_primary 0x7f060041
int color design_default_color_primary_dark 0x7f060042
int color design_error 0x7f060043
int color design_fab_shadow_end_color 0x7f060044
int color design_fab_shadow_mid_color 0x7f060045
int color design_fab_shadow_start_color 0x7f060046
int color design_fab_stroke_end_inner_color 0x7f060047
int color design_fab_stroke_end_outer_color 0x7f060048
int color design_fab_stroke_top_inner_color 0x7f060049
int color design_fab_stroke_top_outer_color 0x7f06004a
int color design_snackbar_background_color 0x7f06004b
int color design_tint_password_toggle 0x7f06004c
int color dim_foreground_disabled_material_dark 0x7f06004d
int color dim_foreground_disabled_material_light 0x7f06004e
int color dim_foreground_material_dark 0x7f06004f
int color dim_foreground_material_light 0x7f060050
int color divider_normal_color 0x7f060051
int color error_color_material_dark 0x7f060052
int color error_color_material_light 0x7f060053
int color foreground_material_dark 0x7f060054
int color foreground_material_light 0x7f060055
int color greenlight_alpha_color 0x7f060056
int color highlighted_text_material_dark 0x7f060057
int color highlighted_text_material_light 0x7f060058
int color line_color 0x7f060059
int color line_color1 0x7f06005a
int color material_blue_grey_800 0x7f06005b
int color material_blue_grey_900 0x7f06005c
int color material_blue_grey_950 0x7f06005d
int color material_deep_teal_200 0x7f06005e
int color material_deep_teal_500 0x7f06005f
int color material_grey_100 0x7f060060
int color material_grey_300 0x7f060061
int color material_grey_50 0x7f060062
int color material_grey_600 0x7f060063
int color material_grey_800 0x7f060064
int color material_grey_850 0x7f060065
int color material_grey_900 0x7f060066
int color menu_item_back_color 0x7f060067
int color menu_item_divider_color 0x7f060068
int color menu_item_sel_color 0x7f060069
int color mtrl_bottom_nav_colored_item_tint 0x7f06006a
int color mtrl_bottom_nav_item_tint 0x7f06006b
int color mtrl_btn_bg_color_disabled 0x7f06006c
int color mtrl_btn_bg_color_selector 0x7f06006d
int color mtrl_btn_ripple_color 0x7f06006e
int color mtrl_btn_stroke_color_selector 0x7f06006f
int color mtrl_btn_text_btn_ripple_color 0x7f060070
int color mtrl_btn_text_color_disabled 0x7f060071
int color mtrl_btn_text_color_selector 0x7f060072
int color mtrl_btn_transparent_bg_color 0x7f060073
int color mtrl_chip_background_color 0x7f060074
int color mtrl_chip_close_icon_tint 0x7f060075
int color mtrl_chip_ripple_color 0x7f060076
int color mtrl_chip_text_color 0x7f060077
int color mtrl_fab_ripple_color 0x7f060078
int color mtrl_scrim_color 0x7f060079
int color mtrl_tabs_colored_ripple_color 0x7f06007a
int color mtrl_tabs_icon_color_selector 0x7f06007b
int color mtrl_tabs_icon_color_selector_colored 0x7f06007c
int color mtrl_tabs_legacy_text_color_selector 0x7f06007d
int color mtrl_tabs_ripple_color 0x7f06007e
int color mtrl_text_btn_text_color_selector 0x7f06007f
int color mtrl_textinput_default_box_stroke_color 0x7f060080
int color mtrl_textinput_disabled_color 0x7f060081
int color mtrl_textinput_filled_box_default_background_color 0x7f060082
int color mtrl_textinput_hovered_box_stroke_color 0x7f060083
int color normal_text_color 0x7f060084
int color normal_text_color2 0x7f060085
int color normal_text_color3 0x7f060086
int color normal_text_color4 0x7f060087
int color normal_text_color5 0x7f060088
int color normal_text_color6 0x7f060089
int color notification_action_color_filter 0x7f06008a
int color notification_icon_bg_color 0x7f06008b
int color notification_material_background_media_default_color 0x7f06008c
int color primary_dark_material_dark 0x7f06008d
int color primary_dark_material_light 0x7f06008e
int color primary_material_dark 0x7f06008f
int color primary_material_light 0x7f060090
int color primary_text_default_material_dark 0x7f060091
int color primary_text_default_material_light 0x7f060092
int color primary_text_disabled_material_dark 0x7f060093
int color primary_text_disabled_material_light 0x7f060094
int color red 0x7f060095
int color ripple_material_dark 0x7f060096
int color ripple_material_light 0x7f060097
int color secondary_text_default_material_dark 0x7f060098
int color secondary_text_default_material_light 0x7f060099
int color secondary_text_disabled_material_dark 0x7f06009a
int color secondary_text_disabled_material_light 0x7f06009b
int color switch_thumb_disabled_material_dark 0x7f06009c
int color switch_thumb_disabled_material_light 0x7f06009d
int color switch_thumb_material_dark 0x7f06009e
int color switch_thumb_material_light 0x7f06009f
int color switch_thumb_normal_material_dark 0x7f0600a0
int color switch_thumb_normal_material_light 0x7f0600a1
int color tooltip_background_dark 0x7f0600a2
int color tooltip_background_light 0x7f0600a3
int color white 0x7f0600a4
int color white_alpha_color 0x7f0600a5
int dimen _100dp 0x7f070000
int dimen _10dp 0x7f070001
int dimen _10sp 0x7f070002
int dimen _110dp 0x7f070003
int dimen _11sp 0x7f070004
int dimen _120dp 0x7f070005
int dimen _12dp 0x7f070006
int dimen _12sp 0x7f070007
int dimen _13sp 0x7f070008
int dimen _140dp 0x7f070009
int dimen _14sp 0x7f07000a
int dimen _150dp 0x7f07000b
int dimen _15dp 0x7f07000c
int dimen _16dp 0x7f07000d
int dimen _16sp 0x7f07000e
int dimen _180dp 0x7f07000f
int dimen _18sp 0x7f070010
int dimen _1dp 0x7f070011
int dimen _200dp 0x7f070012
int dimen _20dp 0x7f070013
int dimen _20sp 0x7f070014
int dimen _22sp 0x7f070015
int dimen _240dp 0x7f070016
int dimen _25dp 0x7f070017
int dimen _260dp 0x7f070018
int dimen _270dp 0x7f070019
int dimen _280dp 0x7f07001a
int dimen _28sp 0x7f07001b
int dimen _2dp 0x7f07001c
int dimen _30dp 0x7f07001d
int dimen _35dp 0x7f07001e
int dimen _36sp 0x7f07001f
int dimen _38dp 0x7f070020
int dimen _3dp 0x7f070021
int dimen _40dp 0x7f070022
int dimen _4dp 0x7f070023
int dimen _50dp 0x7f070024
int dimen _50sp 0x7f070025
int dimen _5dp 0x7f070026
int dimen _60dp 0x7f070027
int dimen _64sp 0x7f070028
int dimen _6dp 0x7f070029
int dimen _70dp 0x7f07002a
int dimen _7dp 0x7f07002b
int dimen _80dp 0x7f07002c
int dimen _8dp 0x7f07002d
int dimen _90dp 0x7f07002e
int dimen _9sp 0x7f07002f
int dimen abc_action_bar_content_inset_material 0x7f070030
int dimen abc_action_bar_content_inset_with_nav 0x7f070031
int dimen abc_action_bar_default_height_material 0x7f070032
int dimen abc_action_bar_default_padding_end_material 0x7f070033
int dimen abc_action_bar_default_padding_start_material 0x7f070034
int dimen abc_action_bar_elevation_material 0x7f070035
int dimen abc_action_bar_icon_vertical_padding_material 0x7f070036
int dimen abc_action_bar_overflow_padding_end_material 0x7f070037
int dimen abc_action_bar_overflow_padding_start_material 0x7f070038
int dimen abc_action_bar_stacked_max_height 0x7f070039
int dimen abc_action_bar_stacked_tab_max_width 0x7f07003a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f07003b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f07003c
int dimen abc_action_button_min_height_material 0x7f07003d
int dimen abc_action_button_min_width_material 0x7f07003e
int dimen abc_action_button_min_width_overflow_material 0x7f07003f
int dimen abc_alert_dialog_button_bar_height 0x7f070040
int dimen abc_alert_dialog_button_dimen 0x7f070041
int dimen abc_button_inset_horizontal_material 0x7f070042
int dimen abc_button_inset_vertical_material 0x7f070043
int dimen abc_button_padding_horizontal_material 0x7f070044
int dimen abc_button_padding_vertical_material 0x7f070045
int dimen abc_cascading_menus_min_smallest_width 0x7f070046
int dimen abc_config_prefDialogWidth 0x7f070047
int dimen abc_control_corner_material 0x7f070048
int dimen abc_control_inset_material 0x7f070049
int dimen abc_control_padding_material 0x7f07004a
int dimen abc_dialog_corner_radius_material 0x7f07004b
int dimen abc_dialog_fixed_height_major 0x7f07004c
int dimen abc_dialog_fixed_height_minor 0x7f07004d
int dimen abc_dialog_fixed_width_major 0x7f07004e
int dimen abc_dialog_fixed_width_minor 0x7f07004f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f070050
int dimen abc_dialog_list_padding_top_no_title 0x7f070051
int dimen abc_dialog_min_width_major 0x7f070052
int dimen abc_dialog_min_width_minor 0x7f070053
int dimen abc_dialog_padding_material 0x7f070054
int dimen abc_dialog_padding_top_material 0x7f070055
int dimen abc_dialog_title_divider_material 0x7f070056
int dimen abc_disabled_alpha_material_dark 0x7f070057
int dimen abc_disabled_alpha_material_light 0x7f070058
int dimen abc_dropdownitem_icon_width 0x7f070059
int dimen abc_dropdownitem_text_padding_left 0x7f07005a
int dimen abc_dropdownitem_text_padding_right 0x7f07005b
int dimen abc_edit_text_inset_bottom_material 0x7f07005c
int dimen abc_edit_text_inset_horizontal_material 0x7f07005d
int dimen abc_edit_text_inset_top_material 0x7f07005e
int dimen abc_floating_window_z 0x7f07005f
int dimen abc_list_item_padding_horizontal_material 0x7f070060
int dimen abc_panel_menu_list_width 0x7f070061
int dimen abc_progress_bar_height_material 0x7f070062
int dimen abc_search_view_preferred_height 0x7f070063
int dimen abc_search_view_preferred_width 0x7f070064
int dimen abc_seekbar_track_background_height_material 0x7f070065
int dimen abc_seekbar_track_progress_height_material 0x7f070066
int dimen abc_select_dialog_padding_start_material 0x7f070067
int dimen abc_switch_padding 0x7f070068
int dimen abc_text_size_body_1_material 0x7f070069
int dimen abc_text_size_body_2_material 0x7f07006a
int dimen abc_text_size_button_material 0x7f07006b
int dimen abc_text_size_caption_material 0x7f07006c
int dimen abc_text_size_display_1_material 0x7f07006d
int dimen abc_text_size_display_2_material 0x7f07006e
int dimen abc_text_size_display_3_material 0x7f07006f
int dimen abc_text_size_display_4_material 0x7f070070
int dimen abc_text_size_headline_material 0x7f070071
int dimen abc_text_size_large_material 0x7f070072
int dimen abc_text_size_medium_material 0x7f070073
int dimen abc_text_size_menu_header_material 0x7f070074
int dimen abc_text_size_menu_material 0x7f070075
int dimen abc_text_size_small_material 0x7f070076
int dimen abc_text_size_subhead_material 0x7f070077
int dimen abc_text_size_subtitle_material_toolbar 0x7f070078
int dimen abc_text_size_title_material 0x7f070079
int dimen abc_text_size_title_material_toolbar 0x7f07007a
int dimen activity_horizontal_margin 0x7f07007b
int dimen activity_vertical_margin 0x7f07007c
int dimen appbar_padding 0x7f07007d
int dimen appbar_padding_top 0x7f07007e
int dimen cardview_compat_inset_shadow 0x7f07007f
int dimen cardview_default_elevation 0x7f070080
int dimen cardview_default_radius 0x7f070081
int dimen compat_button_inset_horizontal_material 0x7f070082
int dimen compat_button_inset_vertical_material 0x7f070083
int dimen compat_button_padding_horizontal_material 0x7f070084
int dimen compat_button_padding_vertical_material 0x7f070085
int dimen compat_control_corner_material 0x7f070086
int dimen compat_notification_large_icon_max_height 0x7f070087
int dimen compat_notification_large_icon_max_width 0x7f070088
int dimen design_appbar_elevation 0x7f070089
int dimen design_bottom_navigation_active_item_max_width 0x7f07008a
int dimen design_bottom_navigation_active_item_min_width 0x7f07008b
int dimen design_bottom_navigation_active_text_size 0x7f07008c
int dimen design_bottom_navigation_elevation 0x7f07008d
int dimen design_bottom_navigation_height 0x7f07008e
int dimen design_bottom_navigation_icon_size 0x7f07008f
int dimen design_bottom_navigation_item_max_width 0x7f070090
int dimen design_bottom_navigation_item_min_width 0x7f070091
int dimen design_bottom_navigation_margin 0x7f070092
int dimen design_bottom_navigation_shadow_height 0x7f070093
int dimen design_bottom_navigation_text_size 0x7f070094
int dimen design_bottom_sheet_modal_elevation 0x7f070095
int dimen design_bottom_sheet_peek_height_min 0x7f070096
int dimen design_fab_border_width 0x7f070097
int dimen design_fab_elevation 0x7f070098
int dimen design_fab_image_size 0x7f070099
int dimen design_fab_size_mini 0x7f07009a
int dimen design_fab_size_normal 0x7f07009b
int dimen design_fab_translation_z_hovered_focused 0x7f07009c
int dimen design_fab_translation_z_pressed 0x7f07009d
int dimen design_navigation_elevation 0x7f07009e
int dimen design_navigation_icon_padding 0x7f07009f
int dimen design_navigation_icon_size 0x7f0700a0
int dimen design_navigation_item_horizontal_padding 0x7f0700a1
int dimen design_navigation_item_icon_padding 0x7f0700a2
int dimen design_navigation_max_width 0x7f0700a3
int dimen design_navigation_padding_bottom 0x7f0700a4
int dimen design_navigation_separator_vertical_padding 0x7f0700a5
int dimen design_snackbar_action_inline_max_width 0x7f0700a6
int dimen design_snackbar_background_corner_radius 0x7f0700a7
int dimen design_snackbar_elevation 0x7f0700a8
int dimen design_snackbar_extra_spacing_horizontal 0x7f0700a9
int dimen design_snackbar_max_width 0x7f0700aa
int dimen design_snackbar_min_width 0x7f0700ab
int dimen design_snackbar_padding_horizontal 0x7f0700ac
int dimen design_snackbar_padding_vertical 0x7f0700ad
int dimen design_snackbar_padding_vertical_2lines 0x7f0700ae
int dimen design_snackbar_text_size 0x7f0700af
int dimen design_tab_max_width 0x7f0700b0
int dimen design_tab_scrollable_min_width 0x7f0700b1
int dimen design_tab_text_size 0x7f0700b2
int dimen design_tab_text_size_2line 0x7f0700b3
int dimen design_textinput_caption_translate_y 0x7f0700b4
int dimen disabled_alpha_material_dark 0x7f0700b5
int dimen disabled_alpha_material_light 0x7f0700b6
int dimen fab_margin 0x7f0700b7
int dimen fastscroll_default_thickness 0x7f0700b8
int dimen fastscroll_margin 0x7f0700b9
int dimen fastscroll_minimum_range 0x7f0700ba
int dimen highlight_alpha_material_colored 0x7f0700bb
int dimen highlight_alpha_material_dark 0x7f0700bc
int dimen highlight_alpha_material_light 0x7f0700bd
int dimen hint_alpha_material_dark 0x7f0700be
int dimen hint_alpha_material_light 0x7f0700bf
int dimen hint_pressed_alpha_material_dark 0x7f0700c0
int dimen hint_pressed_alpha_material_light 0x7f0700c1
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f0700c2
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f0700c3
int dimen item_touch_helper_swipe_escape_velocity 0x7f0700c4
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f0700c5
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f0700c6
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0700c7
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f0700c8
int dimen mtrl_bottomappbar_height 0x7f0700c9
int dimen mtrl_btn_corner_radius 0x7f0700ca
int dimen mtrl_btn_dialog_btn_min_width 0x7f0700cb
int dimen mtrl_btn_disabled_elevation 0x7f0700cc
int dimen mtrl_btn_disabled_z 0x7f0700cd
int dimen mtrl_btn_elevation 0x7f0700ce
int dimen mtrl_btn_focused_z 0x7f0700cf
int dimen mtrl_btn_hovered_z 0x7f0700d0
int dimen mtrl_btn_icon_btn_padding_left 0x7f0700d1
int dimen mtrl_btn_icon_padding 0x7f0700d2
int dimen mtrl_btn_inset 0x7f0700d3
int dimen mtrl_btn_letter_spacing 0x7f0700d4
int dimen mtrl_btn_padding_bottom 0x7f0700d5
int dimen mtrl_btn_padding_left 0x7f0700d6
int dimen mtrl_btn_padding_right 0x7f0700d7
int dimen mtrl_btn_padding_top 0x7f0700d8
int dimen mtrl_btn_pressed_z 0x7f0700d9
int dimen mtrl_btn_stroke_size 0x7f0700da
int dimen mtrl_btn_text_btn_icon_padding 0x7f0700db
int dimen mtrl_btn_text_btn_padding_left 0x7f0700dc
int dimen mtrl_btn_text_btn_padding_right 0x7f0700dd
int dimen mtrl_btn_text_size 0x7f0700de
int dimen mtrl_btn_z 0x7f0700df
int dimen mtrl_card_elevation 0x7f0700e0
int dimen mtrl_card_spacing 0x7f0700e1
int dimen mtrl_chip_pressed_translation_z 0x7f0700e2
int dimen mtrl_chip_text_size 0x7f0700e3
int dimen mtrl_fab_elevation 0x7f0700e4
int dimen mtrl_fab_translation_z_hovered_focused 0x7f0700e5
int dimen mtrl_fab_translation_z_pressed 0x7f0700e6
int dimen mtrl_navigation_elevation 0x7f0700e7
int dimen mtrl_navigation_item_horizontal_padding 0x7f0700e8
int dimen mtrl_navigation_item_icon_padding 0x7f0700e9
int dimen mtrl_snackbar_background_corner_radius 0x7f0700ea
int dimen mtrl_snackbar_margin 0x7f0700eb
int dimen mtrl_textinput_box_bottom_offset 0x7f0700ec
int dimen mtrl_textinput_box_corner_radius_medium 0x7f0700ed
int dimen mtrl_textinput_box_corner_radius_small 0x7f0700ee
int dimen mtrl_textinput_box_label_cutout_padding 0x7f0700ef
int dimen mtrl_textinput_box_padding_end 0x7f0700f0
int dimen mtrl_textinput_box_stroke_width_default 0x7f0700f1
int dimen mtrl_textinput_box_stroke_width_focused 0x7f0700f2
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f0700f3
int dimen mtrl_toolbar_default_height 0x7f0700f4
int dimen nav_header_height 0x7f0700f5
int dimen nav_header_vertical_spacing 0x7f0700f6
int dimen notification_action_icon_size 0x7f0700f7
int dimen notification_action_text_size 0x7f0700f8
int dimen notification_big_circle_margin 0x7f0700f9
int dimen notification_content_margin_start 0x7f0700fa
int dimen notification_large_icon_height 0x7f0700fb
int dimen notification_large_icon_width 0x7f0700fc
int dimen notification_main_column_padding_top 0x7f0700fd
int dimen notification_media_narrow_margin 0x7f0700fe
int dimen notification_right_icon_size 0x7f0700ff
int dimen notification_right_side_padding_top 0x7f070100
int dimen notification_small_icon_background_padding 0x7f070101
int dimen notification_small_icon_size_as_large 0x7f070102
int dimen notification_subtext_size 0x7f070103
int dimen notification_top_pad 0x7f070104
int dimen notification_top_pad_large_text 0x7f070105
int dimen subtitle_corner_radius 0x7f070106
int dimen subtitle_outline_width 0x7f070107
int dimen subtitle_shadow_offset 0x7f070108
int dimen subtitle_shadow_radius 0x7f070109
int dimen tooltip_corner_radius 0x7f07010a
int dimen tooltip_horizontal_padding 0x7f07010b
int dimen tooltip_margin 0x7f07010c
int dimen tooltip_precise_anchor_extra_offset 0x7f07010d
int dimen tooltip_precise_anchor_threshold 0x7f07010e
int dimen tooltip_vertical_padding 0x7f07010f
int dimen tooltip_y_offset_non_touch 0x7f070110
int dimen tooltip_y_offset_touch 0x7f070111
int drawable abc_ab_share_pack_mtrl_alpha 0x7f080006
int drawable abc_action_bar_item_background_material 0x7f080007
int drawable abc_btn_borderless_material 0x7f080008
int drawable abc_btn_check_material 0x7f080009
int drawable abc_btn_check_to_on_mtrl_000 0x7f08000a
int drawable abc_btn_check_to_on_mtrl_015 0x7f08000b
int drawable abc_btn_colored_material 0x7f08000c
int drawable abc_btn_default_mtrl_shape 0x7f08000d
int drawable abc_btn_radio_material 0x7f08000e
int drawable abc_btn_radio_to_on_mtrl_000 0x7f08000f
int drawable abc_btn_radio_to_on_mtrl_015 0x7f080010
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f080011
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f080012
int drawable abc_cab_background_internal_bg 0x7f080013
int drawable abc_cab_background_top_material 0x7f080014
int drawable abc_cab_background_top_mtrl_alpha 0x7f080015
int drawable abc_control_background_material 0x7f080016
int drawable abc_dialog_material_background 0x7f080017
int drawable abc_edit_text_material 0x7f080018
int drawable abc_ic_ab_back_material 0x7f080019
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f08001a
int drawable abc_ic_clear_material 0x7f08001b
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f08001c
int drawable abc_ic_go_search_api_material 0x7f08001d
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f08001e
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f08001f
int drawable abc_ic_menu_overflow_material 0x7f080020
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f080021
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f080022
int drawable abc_ic_menu_share_mtrl_alpha 0x7f080023
int drawable abc_ic_search_api_material 0x7f080024
int drawable abc_ic_star_black_16dp 0x7f080025
int drawable abc_ic_star_black_36dp 0x7f080026
int drawable abc_ic_star_black_48dp 0x7f080027
int drawable abc_ic_star_half_black_16dp 0x7f080028
int drawable abc_ic_star_half_black_36dp 0x7f080029
int drawable abc_ic_star_half_black_48dp 0x7f08002a
int drawable abc_ic_voice_search_api_material 0x7f08002b
int drawable abc_item_background_holo_dark 0x7f08002c
int drawable abc_item_background_holo_light 0x7f08002d
int drawable abc_list_divider_material 0x7f08002e
int drawable abc_list_divider_mtrl_alpha 0x7f08002f
int drawable abc_list_focused_holo 0x7f080030
int drawable abc_list_longpressed_holo 0x7f080031
int drawable abc_list_pressed_holo_dark 0x7f080032
int drawable abc_list_pressed_holo_light 0x7f080033
int drawable abc_list_selector_background_transition_holo_dark 0x7f080034
int drawable abc_list_selector_background_transition_holo_light 0x7f080035
int drawable abc_list_selector_disabled_holo_dark 0x7f080036
int drawable abc_list_selector_disabled_holo_light 0x7f080037
int drawable abc_list_selector_holo_dark 0x7f080038
int drawable abc_list_selector_holo_light 0x7f080039
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f08003a
int drawable abc_popup_background_mtrl_mult 0x7f08003b
int drawable abc_ratingbar_indicator_material 0x7f08003c
int drawable abc_ratingbar_material 0x7f08003d
int drawable abc_ratingbar_small_material 0x7f08003e
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f08003f
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f080040
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f080041
int drawable abc_scrubber_primary_mtrl_alpha 0x7f080042
int drawable abc_scrubber_track_mtrl_alpha 0x7f080043
int drawable abc_seekbar_thumb_material 0x7f080044
int drawable abc_seekbar_tick_mark_material 0x7f080045
int drawable abc_seekbar_track_material 0x7f080046
int drawable abc_spinner_mtrl_am_alpha 0x7f080047
int drawable abc_spinner_textfield_background_material 0x7f080048
int drawable abc_switch_thumb_material 0x7f080049
int drawable abc_switch_track_mtrl_alpha 0x7f08004a
int drawable abc_tab_indicator_material 0x7f08004b
int drawable abc_tab_indicator_mtrl_alpha 0x7f08004c
int drawable abc_text_cursor_material 0x7f08004d
int drawable abc_text_select_handle_left_mtrl_dark 0x7f08004e
int drawable abc_text_select_handle_left_mtrl_light 0x7f08004f
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f080050
int drawable abc_text_select_handle_middle_mtrl_light 0x7f080051
int drawable abc_text_select_handle_right_mtrl_dark 0x7f080052
int drawable abc_text_select_handle_right_mtrl_light 0x7f080053
int drawable abc_textfield_activated_mtrl_alpha 0x7f080054
int drawable abc_textfield_default_mtrl_alpha 0x7f080055
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f080056
int drawable abc_textfield_search_default_mtrl_alpha 0x7f080057
int drawable abc_textfield_search_material 0x7f080058
int drawable abc_vector_test 0x7f080059
int drawable avd_hide_password 0x7f08005a
int drawable avd_show_password 0x7f08005b
int drawable circle 0x7f08005c
int drawable course_button_style 0x7f08005d
int drawable default_board 0x7f08005e
int drawable default_editbox 0x7f08005f
int drawable design_bottom_navigation_item_background 0x7f080060
int drawable design_fab_background 0x7f080061
int drawable design_ic_visibility 0x7f080062
int drawable design_ic_visibility_off 0x7f080063
int drawable design_password_eye 0x7f080064
int drawable design_snackbar_background 0x7f080065
int drawable ic_menu_camera 0x7f080066
int drawable ic_menu_gallery 0x7f080067
int drawable ic_menu_manage 0x7f080068
int drawable ic_menu_send 0x7f080069
int drawable ic_menu_share 0x7f08006a
int drawable ic_menu_slideshow 0x7f08006b
int drawable ic_mtrl_chip_checked_black 0x7f08006c
int drawable ic_mtrl_chip_checked_circle 0x7f08006d
int drawable ic_mtrl_chip_close_circle 0x7f08006e
int drawable icon2 0x7f08006f
int drawable icon_accept 0x7f080070
int drawable icon_close 0x7f080071
int drawable icon_nodata 0x7f080072
int drawable icon_phone1 0x7f080073
int drawable icon_search_mark 0x7f080074
int drawable img_alpha 0x7f080075
int drawable img_alpha0 0x7f080076
int drawable img_alpha1 0x7f080077
int drawable img_alpha100 0x7f080078
int drawable img_alpha25 0x7f080079
int drawable img_alpha50 0x7f08007a
int drawable img_alpha75 0x7f08007b
int drawable img_back2 0x7f08007c
int drawable login_back 0x7f08007d
int drawable login_edit 0x7f08007e
int drawable login_mark_email 0x7f08007f
int drawable login_mark_pwd 0x7f080080
int drawable main_title 0x7f080081
int drawable mini_call_button 0x7f080082
int drawable mtrl_snackbar_background 0x7f080083
int drawable mtrl_tabs_default_indicator 0x7f080084
int drawable navigation_empty_icon 0x7f080085
int drawable normal_button_style 0x7f080086
int drawable notification_action_background 0x7f080087
int drawable notification_bg 0x7f080088
int drawable notification_bg_low 0x7f080089
int drawable notification_bg_low_normal 0x7f08008a
int drawable notification_bg_low_pressed 0x7f08008b
int drawable notification_bg_normal 0x7f08008c
int drawable notification_bg_normal_pressed 0x7f08008d
int drawable notification_icon_background 0x7f08008e
int drawable notification_template_icon_bg 0x7f08008f
int drawable notification_template_icon_low_bg 0x7f080090
int drawable notification_tile_bg 0x7f080091
int drawable notify_panel_notification_icon_bg 0x7f080092
int drawable pop_accept 0x7f080093
int drawable pop_back 0x7f080094
int drawable pop_background 0x7f080095
int drawable pop_close 0x7f080096
int drawable pop_close_gray 0x7f080097
int drawable pop_mark 0x7f080098
int drawable pop_reject 0x7f080099
int drawable pop_result 0x7f08009a
int drawable recall_button_style 0x7f08009b
int drawable round_button 0x7f08009c
int drawable search_back 0x7f08009d
int drawable search_back1 0x7f08009e
int drawable search_backicon 0x7f08009f
int drawable search_button 0x7f0800a0
int drawable search_icon 0x7f0800a1
int drawable shape_back_lightblue_stroke 0x7f0800a2
int drawable shape_back_white_stroke 0x7f0800a3
int drawable side_nav_bar 0x7f0800a4
int drawable signin_line 0x7f0800a5
int drawable social_button 0x7f0800a6
int drawable telegram_icon 0x7f0800a7
int drawable tooltip_frame_dark 0x7f0800a8
int drawable tooltip_frame_light 0x7f0800a9
int id ALT 0x7f090000
int id CTRL 0x7f090001
int id FUNCTION 0x7f090002
int id META 0x7f090003
int id SHIFT 0x7f090004
int id SYM 0x7f090005
int id action0 0x7f090006
int id action_bar 0x7f090007
int id action_bar_activity_content 0x7f090008
int id action_bar_container 0x7f090009
int id action_bar_root 0x7f09000a
int id action_bar_spinner 0x7f09000b
int id action_bar_subtitle 0x7f09000c
int id action_bar_title 0x7f09000d
int id action_container 0x7f09000e
int id action_context_bar 0x7f09000f
int id action_divider 0x7f090010
int id action_image 0x7f090011
int id action_menu_divider 0x7f090012
int id action_menu_presenter 0x7f090013
int id action_mode_bar 0x7f090014
int id action_mode_bar_stub 0x7f090015
int id action_mode_close_button 0x7f090016
int id action_text 0x7f090017
int id actions 0x7f090018
int id activity_chooser_view_content 0x7f090019
int id add 0x7f09001a
int id alertTitle 0x7f09001b
int id all 0x7f09001c
int id always 0x7f09001d
int id appBarLayout 0x7f09001e
int id async 0x7f09001f
int id auto 0x7f090020
int id beginning 0x7f090021
int id blockFragment 0x7f090022
int id block_content 0x7f090023
int id blocking 0x7f090024
int id bottom 0x7f090025
int id btnAccept 0x7f090026
int id btnBlockPrefNumber 0x7f090027
int id btnBlockSpecNumber 0x7f090028
int id btnBlockTodayCall 0x7f090029
int id btnCall 0x7f09002a
int id btnCallExplosion 0x7f09002b
int id btnCancel 0x7f09002c
int id btnClose 0x7f09002d
int id btnDelete 0x7f09002e
int id btnDetail 0x7f09002f
int id btnGoList 0x7f090030
int id btnLogin 0x7f090031
int id btnOk 0x7f090032
int id btnRefresh 0x7f090033
int id btnReject 0x7f090034
int id btnsearch 0x7f090035
int id buttonPanel 0x7f090036
int id cancel_action 0x7f090037
int id center 0x7f090038
int id center_horizontal 0x7f090039
int id center_vertical 0x7f09003a
int id checkbox 0x7f09003b
int id chkBottom 0x7f09003c
int id chkMiddle 0x7f09003d
int id chkTop 0x7f09003e
int id chronometer 0x7f09003f
int id clip_horizontal 0x7f090040
int id clip_vertical 0x7f090041
int id collapseActionView 0x7f090042
int id container 0x7f090043
int id contanerlayout 0x7f090044
int id content 0x7f090045
int id contentPanel 0x7f090046
int id control 0x7f090047
int id coordinator 0x7f090048
int id coordinatorLayout 0x7f090049
int id custom 0x7f09004a
int id customPanel 0x7f09004b
int id decor_content_parent 0x7f09004c
int id default_activity_button 0x7f09004d
int id design_bottom_sheet 0x7f09004e
int id design_menu_item_action_area 0x7f09004f
int id design_menu_item_action_area_stub 0x7f090050
int id design_menu_item_text 0x7f090051
int id design_navigation_view 0x7f090052
int id disableHome 0x7f090053
int id divider 0x7f090054
int id divider0 0x7f090055
int id divider1 0x7f090056
int id divider2 0x7f090057
int id divider3 0x7f090058
int id divider4 0x7f090059
int id divider5 0x7f09005a
int id divider_2 0x7f09005b
int id drawerLV 0x7f09005c
int id drawer_layout 0x7f09005d
int id edit_query 0x7f09005e
int id edtLogID 0x7f09005f
int id edtLogPass 0x7f090060
int id edtSearch 0x7f090061
int id end 0x7f090062
int id end_padder 0x7f090063
int id enterAlways 0x7f090064
int id enterAlwaysCollapsed 0x7f090065
int id exitUntilCollapsed 0x7f090066
int id expand_activities_button 0x7f090067
int id expanded_menu 0x7f090068
int id facebookBT 0x7f090069
int id fill 0x7f09006a
int id fill_horizontal 0x7f09006b
int id fill_vertical 0x7f09006c
int id filled 0x7f09006d
int id fixed 0x7f09006e
int id forever 0x7f09006f
int id ghost_view 0x7f090070
int id googleBT 0x7f090071
int id group_divider 0x7f090072
int id home 0x7f090073
int id homeAsUp 0x7f090074
int id icon 0x7f090075
int id iconIV 0x7f090076
int id icon_group 0x7f090077
int id ifRoom 0x7f090078
int id image 0x7f090079
int id imgAction 0x7f09007a
int id imgNew 0x7f09007b
int id imgPhone 0x7f09007c
int id incomeEmptyTV 0x7f09007d
int id info 0x7f09007e
int id italic 0x7f09007f
int id item 0x7f090080
int id item_touch_helper_previous_elevation 0x7f090081
int id labeled 0x7f090082
int id largeLabel 0x7f090083
int id layoutWatermark 0x7f090084
int id layout_close 0x7f090085
int id layout_nodata 0x7f090086
int id left 0x7f090087
int id line1 0x7f090088
int id line3 0x7f090089
int id lineBT 0x7f09008a
int id linear_manryo 0x7f09008b
int id listMode 0x7f09008c
int id list_item 0x7f09008d
int id lstBlockHistory 0x7f09008e
int id lstDetail 0x7f09008f
int id lstRecentCall 0x7f090090
int id lstReport 0x7f090091
int id mainFragment 0x7f090092
int id mainLayout 0x7f090093
int id makeCallBT 0x7f090094
int id masked 0x7f090095
int id media_actions 0x7f090096
int id menuLayout 0x7f090097
int id message 0x7f090098
int id middle 0x7f090099
int id mini 0x7f09009a
int id mtrl_child_content_container 0x7f09009b
int id mtrl_internal_children_alpha_tag 0x7f09009c
int id multiply 0x7f09009d
int id nameTV 0x7f09009e
int id nav_header 0x7f09009f
int id navigation_header_container 0x7f0900a0
int id never 0x7f0900a1
int id none 0x7f0900a2
int id normal 0x7f0900a3
int id notice_button 0x7f0900a4
int id notice_close 0x7f0900a5
int id notification_background 0x7f0900a6
int id notification_main_column 0x7f0900a7
int id notification_main_column_container 0x7f0900a8
int id outline 0x7f0900a9
int id parallax 0x7f0900aa
int id parentPanel 0x7f0900ab
int id parent_matrix 0x7f0900ac
int id pin 0x7f0900ad
int id progress_circular 0x7f0900ae
int id progress_horizontal 0x7f0900af
int id radio 0x7f0900b0
int id recentCallTV 0x7f0900b1
int id reportFragment 0x7f0900b2
int id right 0x7f0900b3
int id right_icon 0x7f0900b4
int id right_side 0x7f0900b5
int id save_image_matrix 0x7f0900b6
int id save_non_transition_alpha 0x7f0900b7
int id save_scale_type 0x7f0900b8
int id screen 0x7f0900b9
int id scroll 0x7f0900ba
int id scrollIndicatorDown 0x7f0900bb
int id scrollIndicatorUp 0x7f0900bc
int id scrollView 0x7f0900bd
int id scrollable 0x7f0900be
int id searchFrame 0x7f0900bf
int id searchResultCount 0x7f0900c0
int id searchResultEmptyTV 0x7f0900c1
int id searchResultLV 0x7f0900c2
int id search_badge 0x7f0900c3
int id search_bar 0x7f0900c4
int id search_button 0x7f0900c5
int id search_close_btn 0x7f0900c6
int id search_edit_frame 0x7f0900c7
int id search_go_btn 0x7f0900c8
int id search_mag_icon 0x7f0900c9
int id search_plate 0x7f0900ca
int id search_src_text 0x7f0900cb
int id search_voice_btn 0x7f0900cc
int id searchresultTX 0x7f0900cd
int id select_dialog_listview 0x7f0900ce
int id selected 0x7f0900cf
int id sendSMSBT 0x7f0900d0
int id settingFrame 0x7f0900d1
int id shortcut 0x7f0900d2
int id showCustom 0x7f0900d3
int id showHome 0x7f0900d4
int id showTitle 0x7f0900d5
int id smallLabel 0x7f0900d6
int id snackbar_action 0x7f0900d7
int id snackbar_text 0x7f0900d8
int id snap 0x7f0900d9
int id snapMargins 0x7f0900da
int id spacer 0x7f0900db
int id split_action_bar 0x7f0900dc
int id src_atop 0x7f0900dd
int id src_in 0x7f0900de
int id src_over 0x7f0900df
int id start 0x7f0900e0
int id status_bar_latest_event_content 0x7f0900e1
int id stretch 0x7f0900e2
int id submenuarrow 0x7f0900e3
int id submit_area 0x7f0900e4
int id swhBlockAllNumber 0x7f0900e5
int id swhBlockPrefixNumber 0x7f0900e6
int id swhBlockSpecNumber 0x7f0900e7
int id swhBlockTodayCall 0x7f0900e8
int id swhBlockUnknownNumber 0x7f0900e9
int id swhCallExplosion 0x7f0900ea
int id swhShowPopupRemain 0x7f0900eb
int id swhShowTodayCall 0x7f0900ec
int id tabBlockCtrl 0x7f0900ed
int id tabBlockHistory 0x7f0900ee
int id tabBlockNumbers 0x7f0900ef
int id tabBlockSetting 0x7f0900f0
int id tabMode 0x7f0900f1
int id tag_transition_group 0x7f0900f2
int id tag_unhandled_key_event_manager 0x7f0900f3
int id tag_unhandled_key_listeners 0x7f0900f4
int id talkBT 0x7f0900f5
int id text 0x7f0900f6
int id text2 0x7f0900f7
int id textSpacerNoButtons 0x7f0900f8
int id textSpacerNoTitle 0x7f0900f9
int id textStart 0x7f0900fa
int id textView 0x7f0900fb
int id text_input_password_toggle 0x7f0900fc
int id textinput_counter 0x7f0900fd
int id textinput_error 0x7f0900fe
int id textinput_helper_text 0x7f0900ff
int id time 0x7f090100
int id title 0x7f090101
int id titleDividerNoCustom 0x7f090102
int id titleTV 0x7f090103
int id title_template 0x7f090104
int id titlebar 0x7f090105
int id toolbar 0x7f090106
int id top 0x7f090107
int id topPanel 0x7f090108
int id touch_outside 0x7f090109
int id transition_current_scene 0x7f09010a
int id transition_layout_save 0x7f09010b
int id transition_position 0x7f09010c
int id transition_scene_layoutid_cache 0x7f09010d
int id transition_transform 0x7f09010e
int id txtBlockComment 0x7f09010f
int id txtBlockLimit 0x7f090110
int id txtCompany 0x7f090111
int id txtContact 0x7f090112
int id txtCount 0x7f090113
int id txtDate 0x7f090114
int id txtError 0x7f090115
int id txtMemo 0x7f090116
int id txtNew 0x7f090117
int id txtNewNotice 0x7f090118
int id txtNoticeContent 0x7f090119
int id txtNoticeDate 0x7f09011a
int id txtNoticeTitle 0x7f09011b
int id txtPhoneNumber 0x7f09011c
int id txtRemainDay 0x7f09011d
int id txtResultCount 0x7f09011e
int id txtResultPhoneNumber 0x7f09011f
int id txtTotalCount 0x7f090120
int id txtUpdateDate 0x7f090121
int id txtUpdateTime 0x7f090122
int id txt_blockcount 0x7f090123
int id txt_phone 0x7f090124
int id txt_phone2 0x7f090125
int id uniform 0x7f090126
int id unlabeled 0x7f090127
int id up 0x7f090128
int id useLogo 0x7f090129
int id viewDetail 0x7f09012a
int id view_offset_helper 0x7f09012b
int id visible 0x7f09012c
int id webSearch 0x7f09012d
int id webView 0x7f09012e
int id withText 0x7f09012f
int id wrap_content 0x7f090130
int integer abc_config_activityDefaultDur 0x7f0a0000
int integer abc_config_activityShortDur 0x7f0a0001
int integer app_bar_elevation_anim_duration 0x7f0a0002
int integer bottom_sheet_slide_duration 0x7f0a0003
int integer cancel_button_image_alpha 0x7f0a0004
int integer config_tooltipAnimTime 0x7f0a0005
int integer design_snackbar_text_max_lines 0x7f0a0006
int integer design_tab_indicator_anim_duration_ms 0x7f0a0007
int integer hide_password_duration 0x7f0a0008
int integer mtrl_btn_anim_delay_ms 0x7f0a0009
int integer mtrl_btn_anim_duration_ms 0x7f0a000a
int integer mtrl_chip_anim_duration 0x7f0a000b
int integer mtrl_tab_indicator_anim_duration_ms 0x7f0a000c
int integer show_password_duration 0x7f0a000d
int integer status_bar_notification_info_maxnum 0x7f0a000e
int interpolator mtrl_fast_out_linear_in 0x7f0b0000
int interpolator mtrl_fast_out_slow_in 0x7f0b0001
int interpolator mtrl_linear 0x7f0b0002
int interpolator mtrl_linear_out_slow_in 0x7f0b0003
int layout abc_action_bar_title_item 0x7f0c0000
int layout abc_action_bar_up_container 0x7f0c0001
int layout abc_action_menu_item_layout 0x7f0c0002
int layout abc_action_menu_layout 0x7f0c0003
int layout abc_action_mode_bar 0x7f0c0004
int layout abc_action_mode_close_item_material 0x7f0c0005
int layout abc_activity_chooser_view 0x7f0c0006
int layout abc_activity_chooser_view_list_item 0x7f0c0007
int layout abc_alert_dialog_button_bar_material 0x7f0c0008
int layout abc_alert_dialog_material 0x7f0c0009
int layout abc_alert_dialog_title_material 0x7f0c000a
int layout abc_cascading_menu_item_layout 0x7f0c000b
int layout abc_dialog_title_material 0x7f0c000c
int layout abc_expanded_menu_layout 0x7f0c000d
int layout abc_list_menu_item_checkbox 0x7f0c000e
int layout abc_list_menu_item_icon 0x7f0c000f
int layout abc_list_menu_item_layout 0x7f0c0010
int layout abc_list_menu_item_radio 0x7f0c0011
int layout abc_popup_menu_header_item_layout 0x7f0c0012
int layout abc_popup_menu_item_layout 0x7f0c0013
int layout abc_screen_content_include 0x7f0c0014
int layout abc_screen_simple 0x7f0c0015
int layout abc_screen_simple_overlay_action_mode 0x7f0c0016
int layout abc_screen_toolbar 0x7f0c0017
int layout abc_search_dropdown_item_icons_2line 0x7f0c0018
int layout abc_search_view 0x7f0c0019
int layout abc_select_dialog_material 0x7f0c001a
int layout abc_tooltip 0x7f0c001b
int layout activity_login 0x7f0c001c
int layout activity_main 0x7f0c001d
int layout adapter_blockhistory 0x7f0c001e
int layout adapter_blocklist 0x7f0c001f
int layout adapter_drawer 0x7f0c0020
int layout adapter_noticelist 0x7f0c0021
int layout adapter_phone 0x7f0c0022
int layout adapter_recentcalllist 0x7f0c0023
int layout app_bar_main 0x7f0c0024
int layout content_main 0x7f0c0025
int layout content_main_search 0x7f0c0026
int layout content_search_tabbar 0x7f0c0027
int layout design_bottom_navigation_item 0x7f0c0028
int layout design_bottom_sheet_dialog 0x7f0c0029
int layout design_layout_snackbar 0x7f0c002a
int layout design_layout_snackbar_include 0x7f0c002b
int layout design_layout_tab_icon 0x7f0c002c
int layout design_layout_tab_text 0x7f0c002d
int layout design_menu_item_action_area 0x7f0c002e
int layout design_navigation_item 0x7f0c002f
int layout design_navigation_item_header 0x7f0c0030
int layout design_navigation_item_separator 0x7f0c0031
int layout design_navigation_item_subheader 0x7f0c0032
int layout design_navigation_menu 0x7f0c0033
int layout design_navigation_menu_item 0x7f0c0034
int layout design_text_input_password_icon 0x7f0c0035
int layout fragment_block 0x7f0c0036
int layout fragment_block_history 0x7f0c0037
int layout fragment_block_numbers 0x7f0c0038
int layout fragment_block_setting 0x7f0c0039
int layout fragment_main 0x7f0c003a
int layout fragment_notice 0x7f0c003b
int layout fragment_search 0x7f0c003c
int layout fragment_setting 0x7f0c003d
int layout fragment_websearch 0x7f0c003e
int layout mtrl_layout_snackbar 0x7f0c003f
int layout mtrl_layout_snackbar_include 0x7f0c0040
int layout nav_header_main 0x7f0c0041
int layout notification_action 0x7f0c0042
int layout notification_action_tombstone 0x7f0c0043
int layout notification_media_action 0x7f0c0044
int layout notification_media_cancel_action 0x7f0c0045
int layout notification_template_big_media 0x7f0c0046
int layout notification_template_big_media_custom 0x7f0c0047
int layout notification_template_big_media_narrow 0x7f0c0048
int layout notification_template_big_media_narrow_custom 0x7f0c0049
int layout notification_template_custom_big 0x7f0c004a
int layout notification_template_icon_group 0x7f0c004b
int layout notification_template_lines_media 0x7f0c004c
int layout notification_template_media 0x7f0c004d
int layout notification_template_media_custom 0x7f0c004e
int layout notification_template_part_chronometer 0x7f0c004f
int layout notification_template_part_time 0x7f0c0050
int layout pop_up_notice 0x7f0c0051
int layout pop_up_window 0x7f0c0052
int layout popup_block_all 0x7f0c0053
int layout popup_blockcallexp 0x7f0c0054
int layout popup_blockprefnum 0x7f0c0055
int layout popup_blockspecnum 0x7f0c0056
int layout popup_blocktodaycall 0x7f0c0057
int layout popup_blockunknown 0x7f0c0058
int layout select_dialog_item_material 0x7f0c0059
int layout select_dialog_multichoice_material 0x7f0c005a
int layout select_dialog_singlechoice_material 0x7f0c005b
int layout support_simple_spinner_dropdown_item 0x7f0c005c
int menu main 0x7f0d0000
int mipmap android_call 0x7f0e0000
int mipmap android_question 0x7f0e0001
int mipmap arrow_right 0x7f0e0002
int mipmap call 0x7f0e0003
int mipmap email 0x7f0e0004
int mipmap ic_launcher 0x7f0e0005
int mipmap ic_launcher_round 0x7f0e0006
int mipmap line 0x7f0e0007
int mipmap logo 0x7f0e0008
int mipmap nav_cnt_back 0x7f0e0009
int mipmap settings 0x7f0e000a
int mipmap social_call 0x7f0e000b
int mipmap social_facebook 0x7f0e000c
int mipmap social_google 0x7f0e000d
int mipmap social_kakaotalk 0x7f0e000e
int mipmap social_line 0x7f0e000f
int mipmap social_sms 0x7f0e0010
int string abc_action_bar_home_description 0x7f0f0000
int string abc_action_bar_up_description 0x7f0f0001
int string abc_action_menu_overflow_description 0x7f0f0002
int string abc_action_mode_done 0x7f0f0003
int string abc_activity_chooser_view_see_all 0x7f0f0004
int string abc_activitychooserview_choose_application 0x7f0f0005
int string abc_capital_off 0x7f0f0006
int string abc_capital_on 0x7f0f0007
int string abc_font_family_body_1_material 0x7f0f0008
int string abc_font_family_body_2_material 0x7f0f0009
int string abc_font_family_button_material 0x7f0f000a
int string abc_font_family_caption_material 0x7f0f000b
int string abc_font_family_display_1_material 0x7f0f000c
int string abc_font_family_display_2_material 0x7f0f000d
int string abc_font_family_display_3_material 0x7f0f000e
int string abc_font_family_display_4_material 0x7f0f000f
int string abc_font_family_headline_material 0x7f0f0010
int string abc_font_family_menu_material 0x7f0f0011
int string abc_font_family_subhead_material 0x7f0f0012
int string abc_font_family_title_material 0x7f0f0013
int string abc_menu_alt_shortcut_label 0x7f0f0014
int string abc_menu_ctrl_shortcut_label 0x7f0f0015
int string abc_menu_delete_shortcut_label 0x7f0f0016
int string abc_menu_enter_shortcut_label 0x7f0f0017
int string abc_menu_function_shortcut_label 0x7f0f0018
int string abc_menu_meta_shortcut_label 0x7f0f0019
int string abc_menu_shift_shortcut_label 0x7f0f001a
int string abc_menu_space_shortcut_label 0x7f0f001b
int string abc_menu_sym_shortcut_label 0x7f0f001c
int string abc_prepend_shortcut_label 0x7f0f001d
int string abc_search_hint 0x7f0f001e
int string abc_searchview_description_clear 0x7f0f001f
int string abc_searchview_description_query 0x7f0f0020
int string abc_searchview_description_search 0x7f0f0021
int string abc_searchview_description_submit 0x7f0f0022
int string abc_searchview_description_voice 0x7f0f0023
int string abc_shareactionprovider_share_with 0x7f0f0024
int string abc_shareactionprovider_share_with_application 0x7f0f0025
int string abc_toolbar_collapse_description 0x7f0f0026
int string action_settings 0x7f0f0027
int string app_name 0x7f0f0028
int string appbar_scrolling_view_behavior 0x7f0f0029
int string blacklist_request_permissions 0x7f0f002a
int string bottom_sheet_behavior 0x7f0f002b
int string cancel 0x7f0f002c
int string character_counter_content_description 0x7f0f002d
int string character_counter_pattern 0x7f0f002e
int string empty_data 0x7f0f002f
int string fab_transformation_scrim_behavior 0x7f0f0030
int string fab_transformation_sheet_behavior 0x7f0f0031
int string hello_blank_fragment 0x7f0f0032
int string hide_bottom_view_on_scroll_behavior 0x7f0f0033
int string mtrl_chip_close_icon_content_description 0x7f0f0034
int string navigation_drawer_close 0x7f0f0035
int string navigation_drawer_open 0x7f0f0036
int string noCallHistory 0x7f0f0037
int string noData 0x7f0f0038
int string password_toggle_content_description 0x7f0f0039
int string path_password_eye 0x7f0f003a
int string path_password_eye_mask_strike_through 0x7f0f003b
int string path_password_eye_mask_visible 0x7f0f003c
int string path_password_strike_through 0x7f0f003d
int string permissions_required 0x7f0f003e
int string phonesearch_item 0x7f0f003f
int string popup_position 0x7f0f0040
int string question_item 0x7f0f0041
int string report_item 0x7f0f0042
int string search_hint 0x7f0f0043
int string search_menu_title 0x7f0f0044
int string service_fail 0x7f0f0045
int string setting 0x7f0f0046
int string setting_IPlabel 0x7f0f0047
int string setting_Portlabel 0x7f0f0048
int string setting_title 0x7f0f0049
int string status_bar_notification_info_overflow 0x7f0f004a
int string tab_txt_name 0x7f0f004b
int string tab_txt_number 0x7f0f004c
int string text_cancel 0x7f0f004d
int string text_ok 0x7f0f004e
int string title_phoneNumber 0x7f0f004f
int string today_call_request 0x7f0f0050
int string user_id_hint 0x7f0f0051
int string user_login 0x7f0f0052
int string user_login_title 0x7f0f0053
int string user_pass_hint 0x7f0f0054
int string wait 0x7f0f0055
int style AlertDialog_AppCompat 0x7f100000
int style AlertDialog_AppCompat_Light 0x7f100001
int style Animation_AppCompat_Dialog 0x7f100002
int style Animation_AppCompat_DropDownUp 0x7f100003
int style Animation_AppCompat_Tooltip 0x7f100004
int style Animation_Design_BottomSheetDialog 0x7f100005
int style AppTheme 0x7f100006
int style AppTheme_AppBarOverlay 0x7f100007
int style AppTheme_NoActionBar 0x7f100008
int style AppTheme_PopupOverlay 0x7f100009
int style Base_AlertDialog_AppCompat 0x7f10000a
int style Base_AlertDialog_AppCompat_Light 0x7f10000b
int style Base_Animation_AppCompat_Dialog 0x7f10000c
int style Base_Animation_AppCompat_DropDownUp 0x7f10000d
int style Base_Animation_AppCompat_Tooltip 0x7f10000e
int style Base_CardView 0x7f10000f
int style Base_DialogWindowTitle_AppCompat 0x7f100010
int style Base_DialogWindowTitleBackground_AppCompat 0x7f100011
int style Base_TextAppearance_AppCompat 0x7f100012
int style Base_TextAppearance_AppCompat_Body1 0x7f100013
int style Base_TextAppearance_AppCompat_Body2 0x7f100014
int style Base_TextAppearance_AppCompat_Button 0x7f100015
int style Base_TextAppearance_AppCompat_Caption 0x7f100016
int style Base_TextAppearance_AppCompat_Display1 0x7f100017
int style Base_TextAppearance_AppCompat_Display2 0x7f100018
int style Base_TextAppearance_AppCompat_Display3 0x7f100019
int style Base_TextAppearance_AppCompat_Display4 0x7f10001a
int style Base_TextAppearance_AppCompat_Headline 0x7f10001b
int style Base_TextAppearance_AppCompat_Inverse 0x7f10001c
int style Base_TextAppearance_AppCompat_Large 0x7f10001d
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f10001e
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f10001f
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f100020
int style Base_TextAppearance_AppCompat_Medium 0x7f100021
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f100022
int style Base_TextAppearance_AppCompat_Menu 0x7f100023
int style Base_TextAppearance_AppCompat_SearchResult 0x7f100024
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f100025
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f100026
int style Base_TextAppearance_AppCompat_Small 0x7f100027
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f100028
int style Base_TextAppearance_AppCompat_Subhead 0x7f100029
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f10002a
int style Base_TextAppearance_AppCompat_Title 0x7f10002b
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f10002c
int style Base_TextAppearance_AppCompat_Tooltip 0x7f10002d
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f10002e
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f10002f
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f100030
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f100031
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f100032
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f100033
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f100034
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f100035
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f100036
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f100037
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f100038
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f100039
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f10003a
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f10003b
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f10003c
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f10003d
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f10003e
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f10003f
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f100040
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f100041
int style Base_Theme_AppCompat 0x7f100042
int style Base_Theme_AppCompat_CompactMenu 0x7f100043
int style Base_Theme_AppCompat_Dialog 0x7f100044
int style Base_Theme_AppCompat_Dialog_Alert 0x7f100045
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f100046
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f100047
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f100048
int style Base_Theme_AppCompat_Light 0x7f100049
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f10004a
int style Base_Theme_AppCompat_Light_Dialog 0x7f10004b
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f10004c
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f10004d
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f10004e
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f10004f
int style Base_Theme_MaterialComponents 0x7f100050
int style Base_Theme_MaterialComponents_Bridge 0x7f100051
int style Base_Theme_MaterialComponents_CompactMenu 0x7f100052
int style Base_Theme_MaterialComponents_Dialog 0x7f100053
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f100054
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f100055
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f100056
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f100057
int style Base_Theme_MaterialComponents_Light 0x7f100058
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f100059
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f10005a
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f10005b
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f10005c
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f10005d
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f10005e
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f10005f
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f100060
int style Base_ThemeOverlay_AppCompat 0x7f100061
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f100062
int style Base_ThemeOverlay_AppCompat_Dark 0x7f100063
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f100064
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f100065
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f100066
int style Base_ThemeOverlay_AppCompat_Light 0x7f100067
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f100068
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f100069
int style Base_V14_Theme_MaterialComponents 0x7f10006a
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f10006b
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f10006c
int style Base_V14_Theme_MaterialComponents_Light 0x7f10006d
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f10006e
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f10006f
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f100070
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f100071
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f100072
int style Base_V21_Theme_AppCompat 0x7f100073
int style Base_V21_Theme_AppCompat_Dialog 0x7f100074
int style Base_V21_Theme_AppCompat_Light 0x7f100075
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f100076
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f100077
int style Base_V22_Theme_AppCompat 0x7f100078
int style Base_V22_Theme_AppCompat_Light 0x7f100079
int style Base_V23_Theme_AppCompat 0x7f10007a
int style Base_V23_Theme_AppCompat_Light 0x7f10007b
int style Base_V26_Theme_AppCompat 0x7f10007c
int style Base_V26_Theme_AppCompat_Light 0x7f10007d
int style Base_V26_Widget_AppCompat_Toolbar 0x7f10007e
int style Base_V28_Theme_AppCompat 0x7f10007f
int style Base_V28_Theme_AppCompat_Light 0x7f100080
int style Base_V7_Theme_AppCompat 0x7f100081
int style Base_V7_Theme_AppCompat_Dialog 0x7f100082
int style Base_V7_Theme_AppCompat_Light 0x7f100083
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f100084
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f100085
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f100086
int style Base_V7_Widget_AppCompat_EditText 0x7f100087
int style Base_V7_Widget_AppCompat_Toolbar 0x7f100088
int style Base_Widget_AppCompat_ActionBar 0x7f100089
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f10008a
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f10008b
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f10008c
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f10008d
int style Base_Widget_AppCompat_ActionButton 0x7f10008e
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f10008f
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f100090
int style Base_Widget_AppCompat_ActionMode 0x7f100091
int style Base_Widget_AppCompat_ActivityChooserView 0x7f100092
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f100093
int style Base_Widget_AppCompat_Button 0x7f100094
int style Base_Widget_AppCompat_Button_Borderless 0x7f100095
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f100096
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f100097
int style Base_Widget_AppCompat_Button_Colored 0x7f100098
int style Base_Widget_AppCompat_Button_Small 0x7f100099
int style Base_Widget_AppCompat_ButtonBar 0x7f10009a
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f10009b
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f10009c
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f10009d
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f10009e
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f10009f
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1000a0
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1000a1
int style Base_Widget_AppCompat_EditText 0x7f1000a2
int style Base_Widget_AppCompat_ImageButton 0x7f1000a3
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1000a4
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1000a5
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1000a6
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1000a7
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1000a8
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1000a9
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1000aa
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1000ab
int style Base_Widget_AppCompat_ListMenuView 0x7f1000ac
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1000ad
int style Base_Widget_AppCompat_ListView 0x7f1000ae
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1000af
int style Base_Widget_AppCompat_ListView_Menu 0x7f1000b0
int style Base_Widget_AppCompat_PopupMenu 0x7f1000b1
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1000b2
int style Base_Widget_AppCompat_PopupWindow 0x7f1000b3
int style Base_Widget_AppCompat_ProgressBar 0x7f1000b4
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1000b5
int style Base_Widget_AppCompat_RatingBar 0x7f1000b6
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1000b7
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1000b8
int style Base_Widget_AppCompat_SearchView 0x7f1000b9
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1000ba
int style Base_Widget_AppCompat_SeekBar 0x7f1000bb
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1000bc
int style Base_Widget_AppCompat_Spinner 0x7f1000bd
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1000be
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1000bf
int style Base_Widget_AppCompat_Toolbar 0x7f1000c0
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1000c1
int style Base_Widget_Design_TabLayout 0x7f1000c2
int style Base_Widget_MaterialComponents_Chip 0x7f1000c3
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f1000c4
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f1000c5
int style CardView 0x7f1000c6
int style CardView_Dark 0x7f1000c7
int style CardView_Light 0x7f1000c8
int style Platform_AppCompat 0x7f1000c9
int style Platform_AppCompat_Light 0x7f1000ca
int style Platform_MaterialComponents 0x7f1000cb
int style Platform_MaterialComponents_Dialog 0x7f1000cc
int style Platform_MaterialComponents_Light 0x7f1000cd
int style Platform_MaterialComponents_Light_Dialog 0x7f1000ce
int style Platform_ThemeOverlay_AppCompat 0x7f1000cf
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f1000d0
int style Platform_ThemeOverlay_AppCompat_Light 0x7f1000d1
int style Platform_V21_AppCompat 0x7f1000d2
int style Platform_V21_AppCompat_Light 0x7f1000d3
int style Platform_V25_AppCompat 0x7f1000d4
int style Platform_V25_AppCompat_Light 0x7f1000d5
int style Platform_Widget_AppCompat_Spinner 0x7f1000d6
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f1000d7
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f1000d8
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f1000d9
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f1000da
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f1000db
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f1000dc
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f1000dd
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f1000de
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f1000df
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f1000e0
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f1000e1
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f1000e2
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f1000e3
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f1000e4
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f1000e5
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f1000e6
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f1000e7
int style TextAppearance_AppCompat 0x7f1000e8
int style TextAppearance_AppCompat_Body1 0x7f1000e9
int style TextAppearance_AppCompat_Body2 0x7f1000ea
int style TextAppearance_AppCompat_Button 0x7f1000eb
int style TextAppearance_AppCompat_Caption 0x7f1000ec
int style TextAppearance_AppCompat_Display1 0x7f1000ed
int style TextAppearance_AppCompat_Display2 0x7f1000ee
int style TextAppearance_AppCompat_Display3 0x7f1000ef
int style TextAppearance_AppCompat_Display4 0x7f1000f0
int style TextAppearance_AppCompat_Headline 0x7f1000f1
int style TextAppearance_AppCompat_Inverse 0x7f1000f2
int style TextAppearance_AppCompat_Large 0x7f1000f3
int style TextAppearance_AppCompat_Large_Inverse 0x7f1000f4
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f1000f5
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f1000f6
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f1000f7
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f1000f8
int style TextAppearance_AppCompat_Medium 0x7f1000f9
int style TextAppearance_AppCompat_Medium_Inverse 0x7f1000fa
int style TextAppearance_AppCompat_Menu 0x7f1000fb
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f1000fc
int style TextAppearance_AppCompat_SearchResult_Title 0x7f1000fd
int style TextAppearance_AppCompat_Small 0x7f1000fe
int style TextAppearance_AppCompat_Small_Inverse 0x7f1000ff
int style TextAppearance_AppCompat_Subhead 0x7f100100
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f100101
int style TextAppearance_AppCompat_Title 0x7f100102
int style TextAppearance_AppCompat_Title_Inverse 0x7f100103
int style TextAppearance_AppCompat_Tooltip 0x7f100104
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f100105
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f100106
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f100107
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f100108
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f100109
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f10010a
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f10010b
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f10010c
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f10010d
int style TextAppearance_AppCompat_Widget_Button 0x7f10010e
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f10010f
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f100110
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f100111
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f100112
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f100113
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f100114
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f100115
int style TextAppearance_AppCompat_Widget_Switch 0x7f100116
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f100117
int style TextAppearance_Compat_Notification 0x7f100118
int style TextAppearance_Compat_Notification_Info 0x7f100119
int style TextAppearance_Compat_Notification_Info_Media 0x7f10011a
int style TextAppearance_Compat_Notification_Line2 0x7f10011b
int style TextAppearance_Compat_Notification_Line2_Media 0x7f10011c
int style TextAppearance_Compat_Notification_Media 0x7f10011d
int style TextAppearance_Compat_Notification_Time 0x7f10011e
int style TextAppearance_Compat_Notification_Time_Media 0x7f10011f
int style TextAppearance_Compat_Notification_Title 0x7f100120
int style TextAppearance_Compat_Notification_Title_Media 0x7f100121
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f100122
int style TextAppearance_Design_Counter 0x7f100123
int style TextAppearance_Design_Counter_Overflow 0x7f100124
int style TextAppearance_Design_Error 0x7f100125
int style TextAppearance_Design_HelperText 0x7f100126
int style TextAppearance_Design_Hint 0x7f100127
int style TextAppearance_Design_Snackbar_Message 0x7f100128
int style TextAppearance_Design_Tab 0x7f100129
int style TextAppearance_MaterialComponents_Body1 0x7f10012a
int style TextAppearance_MaterialComponents_Body2 0x7f10012b
int style TextAppearance_MaterialComponents_Button 0x7f10012c
int style TextAppearance_MaterialComponents_Caption 0x7f10012d
int style TextAppearance_MaterialComponents_Chip 0x7f10012e
int style TextAppearance_MaterialComponents_Headline1 0x7f10012f
int style TextAppearance_MaterialComponents_Headline2 0x7f100130
int style TextAppearance_MaterialComponents_Headline3 0x7f100131
int style TextAppearance_MaterialComponents_Headline4 0x7f100132
int style TextAppearance_MaterialComponents_Headline5 0x7f100133
int style TextAppearance_MaterialComponents_Headline6 0x7f100134
int style TextAppearance_MaterialComponents_Overline 0x7f100135
int style TextAppearance_MaterialComponents_Subtitle1 0x7f100136
int style TextAppearance_MaterialComponents_Subtitle2 0x7f100137
int style TextAppearance_MaterialComponents_Tab 0x7f100138
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f100139
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f10013a
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f10013b
int style Theme_AppCompat 0x7f10013c
int style Theme_AppCompat_CompactMenu 0x7f10013d
int style Theme_AppCompat_DayNight 0x7f10013e
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f10013f
int style Theme_AppCompat_DayNight_Dialog 0x7f100140
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f100141
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f100142
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f100143
int style Theme_AppCompat_DayNight_NoActionBar 0x7f100144
int style Theme_AppCompat_Dialog 0x7f100145
int style Theme_AppCompat_Dialog_Alert 0x7f100146
int style Theme_AppCompat_Dialog_MinWidth 0x7f100147
int style Theme_AppCompat_DialogWhenLarge 0x7f100148
int style Theme_AppCompat_Light 0x7f100149
int style Theme_AppCompat_Light_DarkActionBar 0x7f10014a
int style Theme_AppCompat_Light_Dialog 0x7f10014b
int style Theme_AppCompat_Light_Dialog_Alert 0x7f10014c
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f10014d
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f10014e
int style Theme_AppCompat_Light_NoActionBar 0x7f10014f
int style Theme_AppCompat_NoActionBar 0x7f100150
int style Theme_Design 0x7f100151
int style Theme_Design_BottomSheetDialog 0x7f100152
int style Theme_Design_Light 0x7f100153
int style Theme_Design_Light_BottomSheetDialog 0x7f100154
int style Theme_Design_Light_NoActionBar 0x7f100155
int style Theme_Design_NoActionBar 0x7f100156
int style Theme_MaterialComponents 0x7f100157
int style Theme_MaterialComponents_BottomSheetDialog 0x7f100158
int style Theme_MaterialComponents_Bridge 0x7f100159
int style Theme_MaterialComponents_CompactMenu 0x7f10015a
int style Theme_MaterialComponents_Dialog 0x7f10015b
int style Theme_MaterialComponents_Dialog_Alert 0x7f10015c
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f10015d
int style Theme_MaterialComponents_DialogWhenLarge 0x7f10015e
int style Theme_MaterialComponents_Light 0x7f10015f
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f100160
int style Theme_MaterialComponents_Light_Bridge 0x7f100161
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f100162
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f100163
int style Theme_MaterialComponents_Light_Dialog 0x7f100164
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f100165
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f100166
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f100167
int style Theme_MaterialComponents_Light_NoActionBar 0x7f100168
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f100169
int style Theme_MaterialComponents_NoActionBar 0x7f10016a
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f10016b
int style ThemeOverlay_AppCompat 0x7f10016c
int style ThemeOverlay_AppCompat_ActionBar 0x7f10016d
int style ThemeOverlay_AppCompat_Dark 0x7f10016e
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f10016f
int style ThemeOverlay_AppCompat_Dialog 0x7f100170
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f100171
int style ThemeOverlay_AppCompat_Light 0x7f100172
int style ThemeOverlay_MaterialComponents 0x7f100173
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f100174
int style ThemeOverlay_MaterialComponents_Dark 0x7f100175
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f100176
int style ThemeOverlay_MaterialComponents_Dialog 0x7f100177
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f100178
int style ThemeOverlay_MaterialComponents_Light 0x7f100179
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f10017a
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f10017b
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f10017c
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f10017d
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f10017e
int style ToolbarColoredBackArrow 0x7f10017f
int style Widget_AppCompat_ActionBar 0x7f100180
int style Widget_AppCompat_ActionBar_Solid 0x7f100181
int style Widget_AppCompat_ActionBar_TabBar 0x7f100182
int style Widget_AppCompat_ActionBar_TabText 0x7f100183
int style Widget_AppCompat_ActionBar_TabView 0x7f100184
int style Widget_AppCompat_ActionButton 0x7f100185
int style Widget_AppCompat_ActionButton_CloseMode 0x7f100186
int style Widget_AppCompat_ActionButton_Overflow 0x7f100187
int style Widget_AppCompat_ActionMode 0x7f100188
int style Widget_AppCompat_ActivityChooserView 0x7f100189
int style Widget_AppCompat_AutoCompleteTextView 0x7f10018a
int style Widget_AppCompat_Button 0x7f10018b
int style Widget_AppCompat_Button_Borderless 0x7f10018c
int style Widget_AppCompat_Button_Borderless_Colored 0x7f10018d
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f10018e
int style Widget_AppCompat_Button_Colored 0x7f10018f
int style Widget_AppCompat_Button_Small 0x7f100190
int style Widget_AppCompat_ButtonBar 0x7f100191
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f100192
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f100193
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f100194
int style Widget_AppCompat_CompoundButton_Switch 0x7f100195
int style Widget_AppCompat_DrawerArrowToggle 0x7f100196
int style Widget_AppCompat_DropDownItem_Spinner 0x7f100197
int style Widget_AppCompat_EditText 0x7f100198
int style Widget_AppCompat_ImageButton 0x7f100199
int style Widget_AppCompat_Light_ActionBar 0x7f10019a
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f10019b
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f10019c
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f10019d
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f10019e
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f10019f
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1001a0
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f1001a1
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f1001a2
int style Widget_AppCompat_Light_ActionButton 0x7f1001a3
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f1001a4
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f1001a5
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f1001a6
int style Widget_AppCompat_Light_ActivityChooserView 0x7f1001a7
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f1001a8
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f1001a9
int style Widget_AppCompat_Light_ListPopupWindow 0x7f1001aa
int style Widget_AppCompat_Light_ListView_DropDown 0x7f1001ab
int style Widget_AppCompat_Light_PopupMenu 0x7f1001ac
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1001ad
int style Widget_AppCompat_Light_SearchView 0x7f1001ae
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f1001af
int style Widget_AppCompat_ListMenuView 0x7f1001b0
int style Widget_AppCompat_ListPopupWindow 0x7f1001b1
int style Widget_AppCompat_ListView 0x7f1001b2
int style Widget_AppCompat_ListView_DropDown 0x7f1001b3
int style Widget_AppCompat_ListView_Menu 0x7f1001b4
int style Widget_AppCompat_PopupMenu 0x7f1001b5
int style Widget_AppCompat_PopupMenu_Overflow 0x7f1001b6
int style Widget_AppCompat_PopupWindow 0x7f1001b7
int style Widget_AppCompat_ProgressBar 0x7f1001b8
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f1001b9
int style Widget_AppCompat_RatingBar 0x7f1001ba
int style Widget_AppCompat_RatingBar_Indicator 0x7f1001bb
int style Widget_AppCompat_RatingBar_Small 0x7f1001bc
int style Widget_AppCompat_SearchView 0x7f1001bd
int style Widget_AppCompat_SearchView_ActionBar 0x7f1001be
int style Widget_AppCompat_SeekBar 0x7f1001bf
int style Widget_AppCompat_SeekBar_Discrete 0x7f1001c0
int style Widget_AppCompat_Spinner 0x7f1001c1
int style Widget_AppCompat_Spinner_DropDown 0x7f1001c2
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f1001c3
int style Widget_AppCompat_Spinner_Underlined 0x7f1001c4
int style Widget_AppCompat_TextView_SpinnerItem 0x7f1001c5
int style Widget_AppCompat_Toolbar 0x7f1001c6
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f1001c7
int style Widget_Compat_NotificationActionContainer 0x7f1001c8
int style Widget_Compat_NotificationActionText 0x7f1001c9
int style Widget_Design_AppBarLayout 0x7f1001ca
int style Widget_Design_BottomNavigationView 0x7f1001cb
int style Widget_Design_BottomSheet_Modal 0x7f1001cc
int style Widget_Design_CollapsingToolbar 0x7f1001cd
int style Widget_Design_FloatingActionButton 0x7f1001ce
int style Widget_Design_NavigationView 0x7f1001cf
int style Widget_Design_ScrimInsetsFrameLayout 0x7f1001d0
int style Widget_Design_Snackbar 0x7f1001d1
int style Widget_Design_TabLayout 0x7f1001d2
int style Widget_Design_TextInputLayout 0x7f1001d3
int style Widget_MaterialComponents_BottomAppBar 0x7f1001d4
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f1001d5
int style Widget_MaterialComponents_BottomNavigationView 0x7f1001d6
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f1001d7
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f1001d8
int style Widget_MaterialComponents_Button 0x7f1001d9
int style Widget_MaterialComponents_Button_Icon 0x7f1001da
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f1001db
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f1001dc
int style Widget_MaterialComponents_Button_TextButton 0x7f1001dd
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f1001de
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f1001df
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f1001e0
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f1001e1
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f1001e2
int style Widget_MaterialComponents_CardView 0x7f1001e3
int style Widget_MaterialComponents_Chip_Action 0x7f1001e4
int style Widget_MaterialComponents_Chip_Choice 0x7f1001e5
int style Widget_MaterialComponents_Chip_Entry 0x7f1001e6
int style Widget_MaterialComponents_Chip_Filter 0x7f1001e7
int style Widget_MaterialComponents_ChipGroup 0x7f1001e8
int style Widget_MaterialComponents_FloatingActionButton 0x7f1001e9
int style Widget_MaterialComponents_NavigationView 0x7f1001ea
int style Widget_MaterialComponents_Snackbar 0x7f1001eb
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f1001ec
int style Widget_MaterialComponents_TabLayout 0x7f1001ed
int style Widget_MaterialComponents_TabLayout_Colored 0x7f1001ee
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f1001ef
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f1001f0
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f1001f1
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f1001f2
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f1001f3
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f1001f4
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f1001f5
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f1001f6
int style Widget_MaterialComponents_Toolbar 0x7f1001f7
int style Widget_Support_CoordinatorLayout 0x7f1001f8
int style courseDuration 0x7f1001f9
int style course_button_style 0x7f1001fa
int style login_edit 0x7f1001fb
int style mini_call_button 0x7f1001fc
int style normal_button_style 0x7f1001fd
int style recall_button_style 0x7f1001fe
int style search_button 0x7f1001ff
int style social_button 0x7f100200
int style toolbarWhiteText 0x7f100201
int[] styleable ActionBar { 0x7f040031, 0x7f040032, 0x7f040033, 0x7f040091, 0x7f040092, 0x7f040093, 0x7f040094, 0x7f040095, 0x7f040096, 0x7f0400a4, 0x7f0400a9, 0x7f0400aa, 0x7f0400b5, 0x7f0400df, 0x7f0400e4, 0x7f0400e9, 0x7f0400ea, 0x7f0400ec, 0x7f0400f6, 0x7f040100, 0x7f040123, 0x7f04012f, 0x7f040140, 0x7f040144, 0x7f040145, 0x7f040173, 0x7f040176, 0x7f0401bb, 0x7f0401c5 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f040031, 0x7f040032, 0x7f04007e, 0x7f0400df, 0x7f040176, 0x7f0401c5 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0400ba, 0x7f0400f7 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AlertDialog { 0x010100f2, 0x7f040052, 0x7f040053, 0x7f04011a, 0x7f04011b, 0x7f04012c, 0x7f04015b, 0x7f04015c }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f0400b5, 0x7f0400bb, 0x7f040115 }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int[] styleable AppBarLayoutStates { 0x7f040169, 0x7f04016a, 0x7f04016b, 0x7f04016c }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f040113, 0x7f040114 }
int styleable AppBarLayout_Layout_layout_scrollFlags 0
int styleable AppBarLayout_Layout_layout_scrollInterpolator 1
int[] styleable AppCompatImageView { 0x01010119, 0x7f040166, 0x7f0401b9, 0x7f0401ba }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f0401b6, 0x7f0401b7, 0x7f0401b8 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f04002c, 0x7f04002d, 0x7f04002e, 0x7f04002f, 0x7f040030, 0x7f0400ce, 0x7f0400d1, 0x7f040108, 0x7f040116, 0x7f040196 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_firstBaselineToTopHeight 6
int styleable AppCompatTextView_fontFamily 7
int styleable AppCompatTextView_lastBaselineToBottomHeight 8
int styleable AppCompatTextView_lineHeight 9
int styleable AppCompatTextView_textAllCaps 10
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f040000, 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000e, 0x7f04000f, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f040021, 0x7f040022, 0x7f040023, 0x7f040024, 0x7f040025, 0x7f04002b, 0x7f04003e, 0x7f04004c, 0x7f04004d, 0x7f04004e, 0x7f04004f, 0x7f040050, 0x7f040054, 0x7f040055, 0x7f04005f, 0x7f040064, 0x7f040084, 0x7f040085, 0x7f040086, 0x7f040087, 0x7f040088, 0x7f040089, 0x7f04008a, 0x7f04008b, 0x7f04008c, 0x7f04008e, 0x7f04009d, 0x7f0400a6, 0x7f0400a7, 0x7f0400a8, 0x7f0400ab, 0x7f0400ad, 0x7f0400b0, 0x7f0400b1, 0x7f0400b2, 0x7f0400b3, 0x7f0400b4, 0x7f0400e9, 0x7f0400f5, 0x7f040118, 0x7f040119, 0x7f04011c, 0x7f04011d, 0x7f04011e, 0x7f04011f, 0x7f040120, 0x7f040121, 0x7f040122, 0x7f040137, 0x7f040138, 0x7f040139, 0x7f04013f, 0x7f040141, 0x7f040148, 0x7f040149, 0x7f04014a, 0x7f04014b, 0x7f040153, 0x7f040154, 0x7f040155, 0x7f040156, 0x7f040163, 0x7f040164, 0x7f04017a, 0x7f0401a1, 0x7f0401a2, 0x7f0401a3, 0x7f0401a4, 0x7f0401a6, 0x7f0401a7, 0x7f0401a8, 0x7f0401a9, 0x7f0401ac, 0x7f0401ad, 0x7f0401c7, 0x7f0401c8, 0x7f0401c9, 0x7f0401ca, 0x7f0401d1, 0x7f0401d3, 0x7f0401d4, 0x7f0401d5, 0x7f0401d6, 0x7f0401d7, 0x7f0401d8, 0x7f0401d9, 0x7f0401da, 0x7f0401db, 0x7f0401dc }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseDrawable 19
int styleable AppCompatTheme_actionModeCopyDrawable 20
int styleable AppCompatTheme_actionModeCutDrawable 21
int styleable AppCompatTheme_actionModeFindDrawable 22
int styleable AppCompatTheme_actionModePasteDrawable 23
int styleable AppCompatTheme_actionModePopupWindowStyle 24
int styleable AppCompatTheme_actionModeSelectAllDrawable 25
int styleable AppCompatTheme_actionModeShareDrawable 26
int styleable AppCompatTheme_actionModeSplitBackground 27
int styleable AppCompatTheme_actionModeStyle 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listDividerAlertDialog 72
int styleable AppCompatTheme_listMenuViewStyle 73
int styleable AppCompatTheme_listPopupWindowStyle 74
int styleable AppCompatTheme_listPreferredItemHeight 75
int styleable AppCompatTheme_listPreferredItemHeightLarge 76
int styleable AppCompatTheme_listPreferredItemHeightSmall 77
int styleable AppCompatTheme_listPreferredItemPaddingLeft 78
int styleable AppCompatTheme_listPreferredItemPaddingRight 79
int styleable AppCompatTheme_panelBackground 80
int styleable AppCompatTheme_panelMenuListTheme 81
int styleable AppCompatTheme_panelMenuListWidth 82
int styleable AppCompatTheme_popupMenuStyle 83
int styleable AppCompatTheme_popupWindowStyle 84
int styleable AppCompatTheme_radioButtonStyle 85
int styleable AppCompatTheme_ratingBarStyle 86
int styleable AppCompatTheme_ratingBarStyleIndicator 87
int styleable AppCompatTheme_ratingBarStyleSmall 88
int styleable AppCompatTheme_searchViewStyle 89
int styleable AppCompatTheme_seekBarStyle 90
int styleable AppCompatTheme_selectableItemBackground 91
int styleable AppCompatTheme_selectableItemBackgroundBorderless 92
int styleable AppCompatTheme_spinnerDropDownItemStyle 93
int styleable AppCompatTheme_spinnerStyle 94
int styleable AppCompatTheme_switchStyle 95
int styleable AppCompatTheme_textAppearanceLargePopupMenu 96
int styleable AppCompatTheme_textAppearanceListItem 97
int styleable AppCompatTheme_textAppearanceListItemSecondary 98
int styleable AppCompatTheme_textAppearanceListItemSmall 99
int styleable AppCompatTheme_textAppearancePopupMenuHeader 100
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 101
int styleable AppCompatTheme_textAppearanceSearchResultTitle 102
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 103
int styleable AppCompatTheme_textColorAlertDialogListItem 104
int styleable AppCompatTheme_textColorSearchUrl 105
int styleable AppCompatTheme_toolbarNavigationButtonStyle 106
int styleable AppCompatTheme_toolbarStyle 107
int styleable AppCompatTheme_tooltipForegroundColor 108
int styleable AppCompatTheme_tooltipFrameBackground 109
int styleable AppCompatTheme_viewInflaterClass 110
int styleable AppCompatTheme_windowActionBar 111
int styleable AppCompatTheme_windowActionBarOverlay 112
int styleable AppCompatTheme_windowActionModeOverlay 113
int styleable AppCompatTheme_windowFixedHeightMajor 114
int styleable AppCompatTheme_windowFixedHeightMinor 115
int styleable AppCompatTheme_windowFixedWidthMajor 116
int styleable AppCompatTheme_windowFixedWidthMinor 117
int styleable AppCompatTheme_windowMinWidthMajor 118
int styleable AppCompatTheme_windowMinWidthMinor 119
int styleable AppCompatTheme_windowNoTitle 120
int[] styleable BottomAppBar { 0x7f040034, 0x7f0400c3, 0x7f0400c4, 0x7f0400c5, 0x7f0400c6, 0x7f0400e5 }
int styleable BottomAppBar_backgroundTint 0
int styleable BottomAppBar_fabAlignmentMode 1
int styleable BottomAppBar_fabCradleMargin 2
int styleable BottomAppBar_fabCradleRoundedCornerRadius 3
int styleable BottomAppBar_fabCradleVerticalOffset 4
int styleable BottomAppBar_hideOnScroll 5
int[] styleable BottomNavigationView { 0x7f0400b5, 0x7f0400fa, 0x7f0400fc, 0x7f0400fe, 0x7f0400ff, 0x7f040103, 0x7f040104, 0x7f040105, 0x7f040107, 0x7f04012b }
int styleable BottomNavigationView_elevation 0
int styleable BottomNavigationView_itemBackground 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int styleable BottomNavigationView_itemIconSize 3
int styleable BottomNavigationView_itemIconTint 4
int styleable BottomNavigationView_itemTextAppearanceActive 5
int styleable BottomNavigationView_itemTextAppearanceInactive 6
int styleable BottomNavigationView_itemTextColor 7
int styleable BottomNavigationView_labelVisibilityMode 8
int styleable BottomNavigationView_menu 9
int[] styleable BottomSheetBehavior_Layout { 0x7f040038, 0x7f040039, 0x7f04003b, 0x7f04003c }
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 0
int styleable BottomSheetBehavior_Layout_behavior_hideable 1
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 2
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 3
int[] styleable ButtonBarLayout { 0x7f040026 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f040058, 0x7f040059, 0x7f04005a, 0x7f04005b, 0x7f04005c, 0x7f04005d, 0x7f040097, 0x7f040098, 0x7f040099, 0x7f04009a, 0x7f04009b }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Chip { 0x01010034, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f040061, 0x7f040062, 0x7f040063, 0x7f040065, 0x7f040066, 0x7f040067, 0x7f040069, 0x7f04006a, 0x7f04006b, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f040073, 0x7f040074, 0x7f040075, 0x7f040077, 0x7f040078, 0x7f040079, 0x7f04007a, 0x7f04007b, 0x7f04007c, 0x7f04007d, 0x7f0400e3, 0x7f0400ed, 0x7f0400f1, 0x7f04014d, 0x7f040159, 0x7f0401ae, 0x7f0401b0 }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_ellipsize 1
int styleable Chip_android_maxWidth 2
int styleable Chip_android_text 3
int styleable Chip_android_checkable 4
int styleable Chip_checkedIcon 5
int styleable Chip_checkedIconEnabled 6
int styleable Chip_checkedIconVisible 7
int styleable Chip_chipBackgroundColor 8
int styleable Chip_chipCornerRadius 9
int styleable Chip_chipEndPadding 10
int styleable Chip_chipIcon 11
int styleable Chip_chipIconEnabled 12
int styleable Chip_chipIconSize 13
int styleable Chip_chipIconTint 14
int styleable Chip_chipIconVisible 15
int styleable Chip_chipMinHeight 16
int styleable Chip_chipStartPadding 17
int styleable Chip_chipStrokeColor 18
int styleable Chip_chipStrokeWidth 19
int styleable Chip_closeIcon 20
int styleable Chip_closeIconEnabled 21
int styleable Chip_closeIconEndPadding 22
int styleable Chip_closeIconSize 23
int styleable Chip_closeIconStartPadding 24
int styleable Chip_closeIconTint 25
int styleable Chip_closeIconVisible 26
int styleable Chip_hideMotionSpec 27
int styleable Chip_iconEndPadding 28
int styleable Chip_iconStartPadding 29
int styleable Chip_rippleColor 30
int styleable Chip_showMotionSpec 31
int styleable Chip_textEndPadding 32
int styleable Chip_textStartPadding 33
int[] styleable ChipGroup { 0x7f040060, 0x7f04006f, 0x7f040070, 0x7f040071, 0x7f04015d, 0x7f04015e }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_singleLine 4
int styleable ChipGroup_singleSelection 5
int[] styleable CollapsingToolbarLayout { 0x7f040081, 0x7f040082, 0x7f04009c, 0x7f0400bc, 0x7f0400bd, 0x7f0400be, 0x7f0400bf, 0x7f0400c0, 0x7f0400c1, 0x7f0400c2, 0x7f04014e, 0x7f040150, 0x7f04016e, 0x7f0401bb, 0x7f0401bc, 0x7f0401c6 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_contentScrim 2
int styleable CollapsingToolbarLayout_expandedTitleGravity 3
int styleable CollapsingToolbarLayout_expandedTitleMargin 4
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 5
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 6
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 7
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 8
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 9
int styleable CollapsingToolbarLayout_scrimAnimationDuration 10
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 11
int styleable CollapsingToolbarLayout_statusBarScrim 12
int styleable CollapsingToolbarLayout_title 13
int styleable CollapsingToolbarLayout_titleEnabled 14
int styleable CollapsingToolbarLayout_toolbarId 15
int[] styleable CollapsingToolbarLayout_Layout { 0x7f04010e, 0x7f04010f }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x7f040027 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_alpha 2
int[] styleable CompoundButton { 0x01010107, 0x7f040056, 0x7f040057 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonTint 1
int styleable CompoundButton_buttonTintMode 2
int[] styleable CoordinatorLayout { 0x7f040106, 0x7f04016d }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f04010b, 0x7f04010c, 0x7f04010d, 0x7f040110, 0x7f040111, 0x7f040112 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DesignTheme { 0x7f040041, 0x7f040042 }
int styleable DesignTheme_bottomSheetDialogTheme 0
int styleable DesignTheme_bottomSheetStyle 1
int[] styleable DrawerArrowToggle { 0x7f040029, 0x7f04002a, 0x7f040036, 0x7f040083, 0x7f0400ae, 0x7f0400dc, 0x7f040162, 0x7f0401b2 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FloatingActionButton { 0x7f040034, 0x7f040035, 0x7f04003d, 0x7f0400b5, 0x7f0400c7, 0x7f0400c8, 0x7f0400e3, 0x7f0400eb, 0x7f040129, 0x7f040143, 0x7f04014d, 0x7f040159, 0x7f0401d0 }
int styleable FloatingActionButton_backgroundTint 0
int styleable FloatingActionButton_backgroundTintMode 1
int styleable FloatingActionButton_borderWidth 2
int styleable FloatingActionButton_elevation 3
int styleable FloatingActionButton_fabCustomSize 4
int styleable FloatingActionButton_fabSize 5
int styleable FloatingActionButton_hideMotionSpec 6
int styleable FloatingActionButton_hoveredFocusedTranslationZ 7
int styleable FloatingActionButton_maxImageSize 8
int styleable FloatingActionButton_pressedTranslationZ 9
int styleable FloatingActionButton_rippleColor 10
int styleable FloatingActionButton_showMotionSpec 11
int styleable FloatingActionButton_useCompatPadding 12
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f040037 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f040101, 0x7f040117 }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f0400d2, 0x7f0400d3, 0x7f0400d4, 0x7f0400d5, 0x7f0400d6, 0x7f0400d7 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0400d0, 0x7f0400d8, 0x7f0400d9, 0x7f0400da, 0x7f0401cf }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f0400db }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f0400aa, 0x7f0400ac, 0x7f04012a, 0x7f040158 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MaterialButton { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f040034, 0x7f040035, 0x7f04009f, 0x7f0400ec, 0x7f0400ee, 0x7f0400ef, 0x7f0400f0, 0x7f0400f2, 0x7f0400f3, 0x7f04014d, 0x7f04016f, 0x7f040170 }
int styleable MaterialButton_android_insetLeft 0
int styleable MaterialButton_android_insetRight 1
int styleable MaterialButton_android_insetTop 2
int styleable MaterialButton_android_insetBottom 3
int styleable MaterialButton_backgroundTint 4
int styleable MaterialButton_backgroundTintMode 5
int styleable MaterialButton_cornerRadius 6
int styleable MaterialButton_icon 7
int styleable MaterialButton_iconGravity 8
int styleable MaterialButton_iconPadding 9
int styleable MaterialButton_iconSize 10
int styleable MaterialButton_iconTint 11
int styleable MaterialButton_iconTintMode 12
int styleable MaterialButton_rippleColor 13
int styleable MaterialButton_strokeColor 14
int styleable MaterialButton_strokeWidth 15
int[] styleable MaterialCardView { 0x7f04016f, 0x7f040170 }
int styleable MaterialCardView_strokeColor 0
int styleable MaterialCardView_strokeWidth 1
int[] styleable MaterialComponentsTheme { 0x7f040041, 0x7f040042, 0x7f040068, 0x7f040072, 0x7f040076, 0x7f040084, 0x7f040085, 0x7f04008b, 0x7f04008c, 0x7f04008d, 0x7f0400b4, 0x7f0400cf, 0x7f040125, 0x7f040126, 0x7f040130, 0x7f04014f, 0x7f04015f, 0x7f040192, 0x7f040197, 0x7f040198, 0x7f040199, 0x7f04019a, 0x7f04019b, 0x7f04019c, 0x7f04019d, 0x7f04019e, 0x7f04019f, 0x7f0401a0, 0x7f0401a5, 0x7f0401aa, 0x7f0401ab, 0x7f0401af }
int styleable MaterialComponentsTheme_bottomSheetDialogTheme 0
int styleable MaterialComponentsTheme_bottomSheetStyle 1
int styleable MaterialComponentsTheme_chipGroupStyle 2
int styleable MaterialComponentsTheme_chipStandaloneStyle 3
int styleable MaterialComponentsTheme_chipStyle 4
int styleable MaterialComponentsTheme_colorAccent 5
int styleable MaterialComponentsTheme_colorBackgroundFloating 6
int styleable MaterialComponentsTheme_colorPrimary 7
int styleable MaterialComponentsTheme_colorPrimaryDark 8
int styleable MaterialComponentsTheme_colorSecondary 9
int styleable MaterialComponentsTheme_editTextStyle 10
int styleable MaterialComponentsTheme_floatingActionButtonStyle 11
int styleable MaterialComponentsTheme_materialButtonStyle 12
int styleable MaterialComponentsTheme_materialCardViewStyle 13
int styleable MaterialComponentsTheme_navigationViewStyle 14
int styleable MaterialComponentsTheme_scrimBackground 15
int styleable MaterialComponentsTheme_snackbarButtonStyle 16
int styleable MaterialComponentsTheme_tabStyle 17
int styleable MaterialComponentsTheme_textAppearanceBody1 18
int styleable MaterialComponentsTheme_textAppearanceBody2 19
int styleable MaterialComponentsTheme_textAppearanceButton 20
int styleable MaterialComponentsTheme_textAppearanceCaption 21
int styleable MaterialComponentsTheme_textAppearanceHeadline1 22
int styleable MaterialComponentsTheme_textAppearanceHeadline2 23
int styleable MaterialComponentsTheme_textAppearanceHeadline3 24
int styleable MaterialComponentsTheme_textAppearanceHeadline4 25
int styleable MaterialComponentsTheme_textAppearanceHeadline5 26
int styleable MaterialComponentsTheme_textAppearanceHeadline6 27
int styleable MaterialComponentsTheme_textAppearanceOverline 28
int styleable MaterialComponentsTheme_textAppearanceSubtitle1 29
int styleable MaterialComponentsTheme_textAppearanceSubtitle2 30
int styleable MaterialComponentsTheme_textInputStyle 31
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f04000d, 0x7f04001f, 0x7f040020, 0x7f040028, 0x7f040090, 0x7f0400f2, 0x7f0400f3, 0x7f040131, 0x7f040157, 0x7f0401cb }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f040142, 0x7f040171 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable NavigationView { 0x010100d4, 0x010100dd, 0x0101011f, 0x7f0400b5, 0x7f0400de, 0x7f0400fa, 0x7f0400fb, 0x7f0400fd, 0x7f0400ff, 0x7f040102, 0x7f040105, 0x7f04012b }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_maxWidth 2
int styleable NavigationView_elevation 3
int styleable NavigationView_headerLayout 4
int styleable NavigationView_itemBackground 5
int styleable NavigationView_itemHorizontalPadding 6
int styleable NavigationView_itemIconPadding 7
int styleable NavigationView_itemIconTint 8
int styleable NavigationView_itemTextAppearance 9
int styleable NavigationView_itemTextColor 10
int styleable NavigationView_menu 11
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f040132 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f040168 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable RecycleListView { 0x7f040133, 0x7f040136 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100f1, 0x7f0400c9, 0x7f0400ca, 0x7f0400cb, 0x7f0400cc, 0x7f0400cd, 0x7f04010a, 0x7f04014c, 0x7f040161, 0x7f040167 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_descendantFocusability 1
int styleable RecyclerView_fastScrollEnabled 2
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 3
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 4
int styleable RecyclerView_fastScrollVerticalThumbDrawable 5
int styleable RecyclerView_fastScrollVerticalTrackDrawable 6
int styleable RecyclerView_layoutManager 7
int styleable RecyclerView_reverseLayout 8
int styleable RecyclerView_spanCount 9
int styleable RecyclerView_stackFromEnd 10
int[] styleable ScrimInsetsFrameLayout { 0x7f0400f8 }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f04003a }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f040077, 0x7f04008f, 0x7f0400a5, 0x7f0400dd, 0x7f0400f4, 0x7f040109, 0x7f040146, 0x7f040147, 0x7f040151, 0x7f040152, 0x7f040172, 0x7f040177, 0x7f0401d2 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable Snackbar { 0x7f04015f, 0x7f040160 }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int[] styleable SnackbarLayout { 0x0101011f, 0x7f0400b5, 0x7f040127 }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_elevation 1
int styleable SnackbarLayout_maxActionInlineWidth 2
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f040140 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f04015a, 0x7f040165, 0x7f040178, 0x7f040179, 0x7f04017b, 0x7f0401b3, 0x7f0401b4, 0x7f0401b5, 0x7f0401cc, 0x7f0401cd, 0x7f0401ce }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f04017c, 0x7f04017d, 0x7f04017e, 0x7f04017f, 0x7f040180, 0x7f040181, 0x7f040182, 0x7f040183, 0x7f040184, 0x7f040185, 0x7f040186, 0x7f040187, 0x7f040188, 0x7f040189, 0x7f04018a, 0x7f04018b, 0x7f04018c, 0x7f04018d, 0x7f04018e, 0x7f04018f, 0x7f040190, 0x7f040191, 0x7f040193, 0x7f040194, 0x7f040195 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorColor 7
int styleable TabLayout_tabIndicatorFullWidth 8
int styleable TabLayout_tabIndicatorGravity 9
int styleable TabLayout_tabIndicatorHeight 10
int styleable TabLayout_tabInlineLabel 11
int styleable TabLayout_tabMaxWidth 12
int styleable TabLayout_tabMinWidth 13
int styleable TabLayout_tabMode 14
int styleable TabLayout_tabPadding 15
int styleable TabLayout_tabPaddingBottom 16
int styleable TabLayout_tabPaddingEnd 17
int styleable TabLayout_tabPaddingStart 18
int styleable TabLayout_tabPaddingTop 19
int styleable TabLayout_tabRippleColor 20
int styleable TabLayout_tabSelectedTextColor 21
int styleable TabLayout_tabTextAppearance 22
int styleable TabLayout_tabTextColor 23
int styleable TabLayout_tabUnboundedRipple 24
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f0400d1, 0x7f040196 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_fontFamily 11
int styleable TextAppearance_textAllCaps 12
int[] styleable TextInputLayout { 0x0101009a, 0x01010150, 0x7f040043, 0x7f040044, 0x7f040045, 0x7f040046, 0x7f040047, 0x7f040048, 0x7f040049, 0x7f04004a, 0x7f04004b, 0x7f0400a0, 0x7f0400a1, 0x7f0400a2, 0x7f0400a3, 0x7f0400b8, 0x7f0400b9, 0x7f0400e0, 0x7f0400e1, 0x7f0400e2, 0x7f0400e6, 0x7f0400e7, 0x7f0400e8, 0x7f04013a, 0x7f04013b, 0x7f04013c, 0x7f04013d, 0x7f04013e }
int styleable TextInputLayout_android_textColorHint 0
int styleable TextInputLayout_android_hint 1
int styleable TextInputLayout_boxBackgroundColor 2
int styleable TextInputLayout_boxBackgroundMode 3
int styleable TextInputLayout_boxCollapsedPaddingTop 4
int styleable TextInputLayout_boxCornerRadiusBottomEnd 5
int styleable TextInputLayout_boxCornerRadiusBottomStart 6
int styleable TextInputLayout_boxCornerRadiusTopEnd 7
int styleable TextInputLayout_boxCornerRadiusTopStart 8
int styleable TextInputLayout_boxStrokeColor 9
int styleable TextInputLayout_boxStrokeWidth 10
int styleable TextInputLayout_counterEnabled 11
int styleable TextInputLayout_counterMaxLength 12
int styleable TextInputLayout_counterOverflowTextAppearance 13
int styleable TextInputLayout_counterTextAppearance 14
int styleable TextInputLayout_errorEnabled 15
int styleable TextInputLayout_errorTextAppearance 16
int styleable TextInputLayout_helperText 17
int styleable TextInputLayout_helperTextEnabled 18
int styleable TextInputLayout_helperTextTextAppearance 19
int styleable TextInputLayout_hintAnimationEnabled 20
int styleable TextInputLayout_hintEnabled 21
int styleable TextInputLayout_hintTextAppearance 22
int styleable TextInputLayout_passwordToggleContentDescription 23
int styleable TextInputLayout_passwordToggleDrawable 24
int styleable TextInputLayout_passwordToggleEnabled 25
int styleable TextInputLayout_passwordToggleTint 26
int styleable TextInputLayout_passwordToggleTintMode 27
int[] styleable ThemeEnforcement { 0x01010034, 0x7f0400b6, 0x7f0400b7 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f040051, 0x7f04007f, 0x7f040080, 0x7f040091, 0x7f040092, 0x7f040093, 0x7f040094, 0x7f040095, 0x7f040096, 0x7f040123, 0x7f040124, 0x7f040128, 0x7f04012d, 0x7f04012e, 0x7f040140, 0x7f040173, 0x7f040174, 0x7f040175, 0x7f0401bb, 0x7f0401bd, 0x7f0401be, 0x7f0401bf, 0x7f0401c0, 0x7f0401c1, 0x7f0401c2, 0x7f0401c3, 0x7f0401c4 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_navigationContentDescription 14
int styleable Toolbar_navigationIcon 15
int styleable Toolbar_popupTheme 16
int styleable Toolbar_subtitle 17
int styleable Toolbar_subtitleTextAppearance 18
int styleable Toolbar_subtitleTextColor 19
int styleable Toolbar_title 20
int styleable Toolbar_titleMargin 21
int styleable Toolbar_titleMarginBottom 22
int styleable Toolbar_titleMarginEnd 23
int styleable Toolbar_titleMarginStart 24
int styleable Toolbar_titleMarginTop 25
int styleable Toolbar_titleMargins 26
int styleable Toolbar_titleTextAppearance 27
int styleable Toolbar_titleTextColor 28
int[] styleable View { 0x01010000, 0x010100da, 0x7f040134, 0x7f040135, 0x7f0401b1 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f040034, 0x7f040035 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int xml provider_paths 0x7f120000
