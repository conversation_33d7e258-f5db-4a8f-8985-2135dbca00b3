1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.developer.faker"
4    android:versionCode="1029"
5    android:versionName="1029" >
6
7    <uses-sdk
8        android:minSdkVersion="19"
8-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml
9        android:targetSdkVersion="28" />
9-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.READ_LOGS" />
11-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:5:5-67
11-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:5:22-65
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:6:5-80
12-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:7:5-79
13-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
14-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:8:5-80
14-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:9:5-66
15-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:9:22-64
16    <uses-permission android:name="android.permission.READ_CONTACTS" />
16-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:10:5-71
16-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:10:22-69
17    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
17-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:11:5-72
17-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:11:22-70
18    <uses-permission android:name="android.permission.RECEIVE_SMS" />
18-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:12:5-69
18-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:12:22-67
19    <uses-permission android:name="android.permission.READ_SMS" />
19-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:13:5-66
19-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:13:22-64
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:14:5-78
20-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:14:22-76
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:15:5-77
21-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:15:22-75
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:16:5-80
22-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:16:22-78
23    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
23-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:17:5-76
23-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:17:22-74
24    <uses-permission android:name="android.permission.CALL_PHONE" />
24-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:18:5-68
24-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:18:22-66
25    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
25-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:19:5-76
25-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:19:22-74
26    <uses-permission android:name="android.permission.READ_CALL_LOG" />
26-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:20:5-71
26-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:20:22-69
27    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
27-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:21:5-72
27-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:21:22-70
28    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
28-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:22:5-74
28-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:22:22-72
29    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
29-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:23:5-76
29-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:23:22-74
30
31    <application
31-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:24:5-82:19
32        android:allowBackup="true"
32-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:28:9-35
33        android:appComponentFactory="android.support.v4.app.CoreComponentFactory"
33-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:31:9-82
34        android:icon="@mipmap/logo"
34-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:27:9-36
35        android:label="@string/app_name"
35-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:26:9-41
36        android:roundIcon="@mipmap/logo"
36-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:30:9-41
37        android:supportsRtl="true"
37-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:29:9-35
38        android:theme="@style/AppTheme" >
38-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:25:9-40
39        <activity
39-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:32:9-41:20
40            android:name="com.developer.faker.Activity.LoginActivity"
40-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:34:13-70
41            android:screenOrientation="portrait"
41-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:35:13-49
42            android:theme="@style/AppTheme.NoActionBar" >
42-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:33:13-56
43            <intent-filter>
43-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:36:13-40:29
44                <action android:name="android.intent.action.MAIN" />
44-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:37:17-68
44-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:37:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:38:17-76
46-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:38:27-74
47                <category android:name="android.intent.category.DEFAULT" />
47-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:39:17-75
47-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:39:27-73
48            </intent-filter>
49        </activity>
50        <activity
50-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:42:9-45:51
51            android:name="com.developer.faker.Activity.MainActivity"
51-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:44:13-69
52            android:screenOrientation="portrait"
52-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:45:13-49
53            android:theme="@style/AppTheme.NoActionBar" />
53-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:43:13-56
54
55        <service
55-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:46:9-53:19
56            android:name="com.developer.faker.Service.MainService"
56-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:47:13-67
57            android:enabled="true"
57-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:49:13-35
58            android:permission="android.permission.SYSTEM_ALERT_WINDOW" >
58-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:48:13-72
59            <intent-filter>
59-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:50:13-52:29
60                <action android:name="com.developer.faker.Service.MainService" />
60-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:51:17-81
60-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:51:25-79
61            </intent-filter>
62        </service>
63
64        <receiver android:name="com.developer.faker.Service.RestartReceiver" />
64-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:54:9-79
64-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:54:19-77
65        <receiver android:name="com.developer.faker.Service.BootReceiver" >
65-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:55:9-59:20
65-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:55:19-74
66            <intent-filter>
66-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:56:13-58:29
67                <action android:name="android.intent.action.BOOT_COMPLETED" />
67-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:57:17-78
67-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:57:25-76
68            </intent-filter>
69        </receiver>
70
71        <service
71-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:60:9-63:39
72            android:name="com.developer.faker.Service.FloatingViewService"
72-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:61:13-75
73            android:enabled="true"
73-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:62:13-35
74            android:exported="false" />
74-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:63:13-37
75
76        <receiver
76-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:64:9-72:20
77            android:name="com.developer.faker.Service.NewPhonecallReceiver"
77-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:65:13-76
78            android:enabled="true" >
78-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:66:13-35
79            <intent-filter android:priority="2147483647" >
79-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:67:13-71:29
79-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:67:28-57
80                <action android:name="android.intent.action.PHONE_STATE" />
80-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:68:17-75
80-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:68:25-73
81                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
81-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:69:17-81
81-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:69:25-79
82                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
82-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:70:17-81
82-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:70:25-79
83            </intent-filter>
84        </receiver>
85
86        <provider
86-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:73:9-81:20
87            android:name="android.support.v4.content.FileProvider"
87-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:74:13-67
88            android:authorities="com.developer.faker"
88-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:76:13-54
89            android:exported="false"
89-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:75:13-37
90            android:grantUriPermissions="true" >
90-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:77:13-47
91            <meta-data
91-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:78:13-80:57
92                android:name="android.support.FILE_PROVIDER_PATHS"
92-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:79:17-67
93                android:resource="@xml/provider_paths" />
93-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:80:17-55
94        </provider>
95    </application>
96
97</manifest>
