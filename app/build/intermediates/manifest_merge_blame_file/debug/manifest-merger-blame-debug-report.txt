1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.developer.faker"
4    android:versionCode="1029"
5    android:versionName="1029" >
6
7    <uses-sdk
7-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:4:5-6:40
8        android:minSdkVersion="19"
8-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:5:9-35
9        android:targetSdkVersion="28" />
9-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:6:9-38
10
11    <uses-permission android:name="android.permission.READ_LOGS" />
11-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:7:5-67
11-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:7:22-65
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:8:5-80
12-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:8:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:9:5-79
13-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:9:22-77
14    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
14-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:10:5-80
14-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:10:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:11:5-66
15-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:11:22-64
16    <uses-permission android:name="android.permission.READ_CONTACTS" />
16-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:12:5-71
16-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:12:22-69
17    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
17-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:13:5-72
17-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:13:22-70
18    <uses-permission android:name="android.permission.RECEIVE_SMS" />
18-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:14:5-69
18-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:14:22-67
19    <uses-permission android:name="android.permission.READ_SMS" />
19-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:15:5-66
19-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:15:22-64
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:16:5-78
20-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:16:22-76
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:17:5-77
21-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:17:22-75
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:18:5-80
22-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:18:22-78
23    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
23-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:19:5-76
23-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:19:22-74
24    <uses-permission android:name="android.permission.CALL_PHONE" />
24-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:20:5-68
24-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:20:22-66
25    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
25-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:21:5-76
25-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:21:22-74
26    <uses-permission android:name="android.permission.READ_CALL_LOG" />
26-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:22:5-71
26-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:22:22-69
27    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
27-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:23:5-72
27-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:23:22-70
28    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
28-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:24:5-74
28-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:24:22-72
29    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
29-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:25:5-76
29-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:25:22-74
30
31    <application
31-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:26:5-84:19
32        android:allowBackup="true"
32-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:30:9-35
33        android:appComponentFactory="android.support.v4.app.CoreComponentFactory"
33-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:33:9-82
34        android:debuggable="true"
35        android:icon="@mipmap/logo"
35-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:29:9-36
36        android:label="@string/app_name"
36-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:28:9-41
37        android:roundIcon="@mipmap/logo"
37-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:32:9-41
38        android:supportsRtl="true"
38-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:31:9-35
39        android:theme="@style/AppTheme" >
39-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:27:9-40
40        <activity
40-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:34:9-43:20
41            android:name="com.developer.faker.Activity.LoginActivity"
41-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:36:13-70
42            android:screenOrientation="portrait"
42-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:37:13-49
43            android:theme="@style/AppTheme.NoActionBar" >
43-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:35:13-56
44            <intent-filter>
44-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:38:13-42:29
45                <action android:name="android.intent.action.MAIN" />
45-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:39:17-68
45-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:39:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:40:17-76
47-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:40:27-74
48                <category android:name="android.intent.category.DEFAULT" />
48-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:41:17-75
48-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:41:27-73
49            </intent-filter>
50        </activity>
51        <activity
51-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:44:9-47:51
52            android:name="com.developer.faker.Activity.MainActivity"
52-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:46:13-69
53            android:screenOrientation="portrait"
53-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:47:13-49
54            android:theme="@style/AppTheme.NoActionBar" />
54-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:45:13-56
55
56        <service
56-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:48:9-55:19
57            android:name="com.developer.faker.Service.MainService"
57-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:49:13-67
58            android:enabled="true"
58-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:51:13-35
59            android:permission="android.permission.SYSTEM_ALERT_WINDOW" >
59-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:50:13-72
60            <intent-filter>
60-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:52:13-54:29
61                <action android:name="com.developer.faker.Service.MainService" />
61-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:53:17-81
61-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:53:25-79
62            </intent-filter>
63        </service>
64
65        <receiver android:name="com.developer.faker.Service.RestartReceiver" />
65-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:56:9-79
65-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:56:19-77
66        <receiver android:name="com.developer.faker.Service.BootReceiver" >
66-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:57:9-61:20
66-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:57:19-74
67            <intent-filter>
67-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:58:13-60:29
68                <action android:name="android.intent.action.BOOT_COMPLETED" />
68-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:59:17-78
68-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:59:25-76
69            </intent-filter>
70        </receiver>
71
72        <service
72-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:62:9-65:39
73            android:name="com.developer.faker.Service.FloatingViewService"
73-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:63:13-75
74            android:enabled="true"
74-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:64:13-35
75            android:exported="false" />
75-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:65:13-37
76
77        <receiver
77-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:66:9-74:20
78            android:name="com.developer.faker.Service.NewPhonecallReceiver"
78-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:67:13-76
79            android:enabled="true" >
79-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:68:13-35
80            <intent-filter android:priority="2147483647" >
80-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:69:13-73:29
80-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:69:28-57
81                <action android:name="android.intent.action.PHONE_STATE" />
81-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:70:17-75
81-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:70:25-73
82                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
82-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:71:17-81
82-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:71:25-79
83                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
83-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:72:17-81
83-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:72:25-79
84            </intent-filter>
85        </receiver>
86
87        <provider
87-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:75:9-83:20
88            android:name="android.support.v4.content.FileProvider"
88-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:76:13-67
89            android:authorities="com.developer.faker"
89-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:78:13-54
90            android:exported="false"
90-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:77:13-37
91            android:grantUriPermissions="true" >
91-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:79:13-47
92            <meta-data
92-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:80:13-82:57
93                android:name="android.support.FILE_PROVIDER_PATHS"
93-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:81:17-67
94                android:resource="@xml/provider_paths" />
94-->/Users/<USER>/paker/app/src/main/AndroidManifest.xml:82:17-55
95        </provider>
96    </application>
97
98</manifest>
