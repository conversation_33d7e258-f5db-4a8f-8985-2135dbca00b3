-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
MERGED from [com.android.support:design:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.android.support:appcompat-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-v4:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d12d8297e54055d8c8fd7bcfbdd0aaba/support-v4-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:recyclerview-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/96d6faed5a5735fd14bdec9d38b8895e/recyclerview-v7-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:cardview-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e476e0dd04b5343b91450d154c8a112f/cardview-v7-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:animated-vector-drawable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e9624393a49dd94b2ca40a9ffdf35a3e/animated-vector-drawable-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-vector-drawable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/37e865960876485af47f95eda2fb5483/support-vector-drawable-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-fragment:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/afecda216b6c983155685d2561c96315/support-fragment-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-ui:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8e2be69417a5434409aff2e241559cd2/support-core-ui-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-core-utils:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/a132f49195b33e6b679761c7a033334d/support-core-utils-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:transition:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c8781cf76c3d46af7130a3e9cb97651e/transition-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-media-compat:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8fd5d53888178dd9013834ef7b031501/support-media-compat-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:loader:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/015b56bcd73cc826db8dbdf9a2aba918/loader-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:viewpager:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/03c282f76fad1e5d11a5e46adeaecf59/viewpager-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:coordinatorlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/a431d7a91ec2d1f028c3cc59a84cc899/coordinatorlayout-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:drawerlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1986b45cb766081b34849193e6950bdd/drawerlayout-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:slidingpanelayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/0d1c5c6ca1bcd9b48dbd540322103c10/slidingpanelayout-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:customview:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/fa6e4af4a16ea0dc41d191cc8da93421/customview-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:swiperefreshlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/37f8a6d72b11c046ce4d815a99520da7/swiperefreshlayout-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:asynclayoutinflater:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/7288d2d6d90f2c01f96d2e994c124206/asynclayoutinflater-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-compat:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [android.arch.lifecycle:runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/4518bb44f6ad6e1e929633d50f257c0d/runtime-1.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ccdfb67e718a1978464704598ce1ec17/viewmodel-1.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:livedata:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9fb27d4ae00926bcad6f38c8c957ce27/livedata-1.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:versionedparcelable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/2c8daf115cfeeeebdc4e90a34886e881/versionedparcelable-28.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.android.support:cursoradapter:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/01f9028635fdcebf8f8dbceab8b8edb0/cursoradapter-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/be0964a4bb53975424fb17c579ce79a2/livedata-core-1.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [android.arch.core:runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9f6e957644e5c3abf3c95a4346af2d34/runtime-1.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:documentfile:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8a3c3bd212458805be563f8e82222fa4/documentfile-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:localbroadcastmanager:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/66637a71ed7b0c19bdb2a6d0f62c6df5/localbroadcastmanager-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:print:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/5adae96c5ac7f4b2080edbd6fb7d9552/print-28.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:interpolator:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d1b061c0673a3adc8af98c19a9222f07/interpolator-28.0.0/AndroidManifest.xml:17:1-22:12
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
	package
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:3:5-34
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:1-85:12
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:2:11-69
uses-sdk
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:4:5-6:40
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:4:5-6:40
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:4:5-6:40
MERGED from [com.android.support:design:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:design:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:appcompat-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:appcompat-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-v4:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d12d8297e54055d8c8fd7bcfbdd0aaba/support-v4-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-v4:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d12d8297e54055d8c8fd7bcfbdd0aaba/support-v4-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:recyclerview-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/96d6faed5a5735fd14bdec9d38b8895e/recyclerview-v7-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:recyclerview-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/96d6faed5a5735fd14bdec9d38b8895e/recyclerview-v7-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cardview-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e476e0dd04b5343b91450d154c8a112f/cardview-v7-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cardview-v7:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e476e0dd04b5343b91450d154c8a112f/cardview-v7-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e9624393a49dd94b2ca40a9ffdf35a3e/animated-vector-drawable-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e9624393a49dd94b2ca40a9ffdf35a3e/animated-vector-drawable-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/37e865960876485af47f95eda2fb5483/support-vector-drawable-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/37e865960876485af47f95eda2fb5483/support-vector-drawable-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-fragment:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/afecda216b6c983155685d2561c96315/support-fragment-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-fragment:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/afecda216b6c983155685d2561c96315/support-fragment-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8e2be69417a5434409aff2e241559cd2/support-core-ui-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-ui:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8e2be69417a5434409aff2e241559cd2/support-core-ui-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/a132f49195b33e6b679761c7a033334d/support-core-utils-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-core-utils:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/a132f49195b33e6b679761c7a033334d/support-core-utils-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:transition:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c8781cf76c3d46af7130a3e9cb97651e/transition-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:transition:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c8781cf76c3d46af7130a3e9cb97651e/transition-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-media-compat:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8fd5d53888178dd9013834ef7b031501/support-media-compat-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-media-compat:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8fd5d53888178dd9013834ef7b031501/support-media-compat-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:loader:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/015b56bcd73cc826db8dbdf9a2aba918/loader-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:loader:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/015b56bcd73cc826db8dbdf9a2aba918/loader-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:viewpager:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/03c282f76fad1e5d11a5e46adeaecf59/viewpager-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:viewpager:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/03c282f76fad1e5d11a5e46adeaecf59/viewpager-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:coordinatorlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/a431d7a91ec2d1f028c3cc59a84cc899/coordinatorlayout-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:coordinatorlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/a431d7a91ec2d1f028c3cc59a84cc899/coordinatorlayout-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:drawerlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1986b45cb766081b34849193e6950bdd/drawerlayout-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:drawerlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1986b45cb766081b34849193e6950bdd/drawerlayout-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:slidingpanelayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/0d1c5c6ca1bcd9b48dbd540322103c10/slidingpanelayout-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:slidingpanelayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/0d1c5c6ca1bcd9b48dbd540322103c10/slidingpanelayout-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:customview:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/fa6e4af4a16ea0dc41d191cc8da93421/customview-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:customview:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/fa6e4af4a16ea0dc41d191cc8da93421/customview-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:swiperefreshlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/37f8a6d72b11c046ce4d815a99520da7/swiperefreshlayout-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:swiperefreshlayout:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/37f8a6d72b11c046ce4d815a99520da7/swiperefreshlayout-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:asynclayoutinflater:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/7288d2d6d90f2c01f96d2e994c124206/asynclayoutinflater-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:asynclayoutinflater:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/7288d2d6d90f2c01f96d2e994c124206/asynclayoutinflater-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-compat:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/4518bb44f6ad6e1e929633d50f257c0d/runtime-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/4518bb44f6ad6e1e929633d50f257c0d/runtime-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ccdfb67e718a1978464704598ce1ec17/viewmodel-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:viewmodel:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ccdfb67e718a1978464704598ce1ec17/viewmodel-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9fb27d4ae00926bcad6f38c8c957ce27/livedata-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9fb27d4ae00926bcad6f38c8c957ce27/livedata-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:versionedparcelable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/2c8daf115cfeeeebdc4e90a34886e881/versionedparcelable-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:versionedparcelable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/2c8daf115cfeeeebdc4e90a34886e881/versionedparcelable-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cursoradapter:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/01f9028635fdcebf8f8dbceab8b8edb0/cursoradapter-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:cursoradapter:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/01f9028635fdcebf8f8dbceab8b8edb0/cursoradapter-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/be0964a4bb53975424fb17c579ce79a2/livedata-core-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [android.arch.lifecycle:livedata-core:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/be0964a4bb53975424fb17c579ce79a2/livedata-core-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [android.arch.core:runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9f6e957644e5c3abf3c95a4346af2d34/runtime-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [android.arch.core:runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9f6e957644e5c3abf3c95a4346af2d34/runtime-1.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:documentfile:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8a3c3bd212458805be563f8e82222fa4/documentfile-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:documentfile:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8a3c3bd212458805be563f8e82222fa4/documentfile-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:localbroadcastmanager:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/66637a71ed7b0c19bdb2a6d0f62c6df5/localbroadcastmanager-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:localbroadcastmanager:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/66637a71ed7b0c19bdb2a6d0f62c6df5/localbroadcastmanager-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:print:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/5adae96c5ac7f4b2080edbd6fb7d9552/print-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:print:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/5adae96c5ac7f4b2080edbd6fb7d9552/print-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:interpolator:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d1b061c0673a3adc8af98c19a9222f07/interpolator-28.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:interpolator:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d1b061c0673a3adc8af98c19a9222f07/interpolator-28.0.0/AndroidManifest.xml:20:5-44
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:4:5-6:40
INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:4:5-6:40
	android:targetSdkVersion
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:6:9-38
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:5:9-35
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml
uses-permission#android.permission.READ_LOGS
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:7:5-67
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:7:22-65
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:8:5-80
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:8:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:9:5-79
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:9:22-77
uses-permission#android.permission.PROCESS_OUTGOING_CALLS
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:10:5-80
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:10:22-78
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:11:5-66
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:11:22-64
uses-permission#android.permission.READ_CONTACTS
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:12:5-71
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:12:22-69
uses-permission#android.permission.WRITE_CONTACTS
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:13:5-72
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:13:22-70
uses-permission#android.permission.RECEIVE_SMS
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:14:5-69
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:14:22-67
uses-permission#android.permission.READ_SMS
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:15:5-66
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:15:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:16:5-78
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:16:22-76
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:17:5-77
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:17:22-75
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:18:5-80
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:18:22-78
uses-permission#android.permission.ANSWER_PHONE_CALLS
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:19:5-76
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:19:22-74
uses-permission#android.permission.CALL_PHONE
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:20:5-68
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:20:22-66
uses-permission#android.permission.MODIFY_PHONE_STATE
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:21:5-76
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:21:22-74
uses-permission#android.permission.READ_CALL_LOG
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:22:5-71
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:22:22-69
uses-permission#android.permission.WRITE_CALL_LOG
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:23:5-72
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:23:22-70
uses-permission#android.permission.READ_PHONE_STATE
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:24:5-74
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:24:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:25:5-76
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:25:22-74
application
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:26:5-84:19
MERGED from [com.android.support:design:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/AndroidManifest.xml:22:5-20
MERGED from [com.android.support:design:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/AndroidManifest.xml:22:5-20
MERGED from [com.android.support:support-compat:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/AndroidManifest.xml:22:5-94
MERGED from [com.android.support:support-compat:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/AndroidManifest.xml:22:5-94
MERGED from [com.android.support:versionedparcelable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/2c8daf115cfeeeebdc4e90a34886e881/versionedparcelable-28.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.android.support:versionedparcelable:28.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/2c8daf115cfeeeebdc4e90a34886e881/versionedparcelable-28.0.0/AndroidManifest.xml:22:5-23:19
	android:supportsRtl
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:31:9-35
	android:label
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:28:9-41
	android:appComponentFactory
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:33:9-82
	android:roundIcon
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:32:9-41
	android:icon
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:29:9-36
	android:allowBackup
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:30:9-35
	android:theme
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:27:9-40
activity#com.developer.faker.Activity.LoginActivity
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:34:9-43:20
	android:screenOrientation
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:37:13-49
	android:theme
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:35:13-56
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:36:13-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:38:13-42:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:39:17-68
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:39:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:40:17-76
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:40:27-74
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:41:17-75
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:41:27-73
activity#com.developer.faker.Activity.MainActivity
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:44:9-47:51
	android:screenOrientation
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:47:13-49
	android:theme
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:45:13-56
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:46:13-69
service#com.developer.faker.Service.MainService
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:48:9-55:19
	android:enabled
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:51:13-35
	android:permission
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:50:13-72
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:49:13-67
intent-filter#action:name:com.developer.faker.Service.MainService
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:52:13-54:29
action#com.developer.faker.Service.MainService
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:53:17-81
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:53:25-79
receiver#com.developer.faker.Service.RestartReceiver
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:56:9-79
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:56:19-77
receiver#com.developer.faker.Service.BootReceiver
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:57:9-61:20
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:57:19-74
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:58:13-60:29
action#android.intent.action.BOOT_COMPLETED
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:59:17-78
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:59:25-76
service#com.developer.faker.Service.FloatingViewService
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:62:9-65:39
	android:enabled
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:65:13-37
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:63:13-75
receiver#com.developer.faker.Service.NewPhonecallReceiver
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:66:9-74:20
	android:enabled
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:68:13-35
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:67:13-76
intent-filter#action:name:android.intent.action.NEW_OUTGOING_CALL+action:name:android.intent.action.PHONE_STATE+action:name:android.provider.Telephony.SMS_RECEIVED
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:69:13-73:29
	android:priority
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:69:28-57
action#android.intent.action.PHONE_STATE
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:70:17-75
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:70:25-73
action#android.intent.action.NEW_OUTGOING_CALL
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:71:17-81
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:71:25-79
action#android.provider.Telephony.SMS_RECEIVED
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:72:17-81
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:72:25-79
provider#android.support.v4.content.FileProvider
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:75:9-83:20
	android:grantUriPermissions
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:79:13-47
	android:authorities
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:78:13-54
	android:exported
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:77:13-37
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:76:13-67
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:80:13-82:57
	android:resource
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:82:17-55
	android:name
		ADDED from /Users/<USER>/paker/app/src/main/AndroidManifest.xml:81:17-67
